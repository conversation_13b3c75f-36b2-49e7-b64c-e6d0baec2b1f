# 🔧 Backend Testing & Troubleshooting

## 🚀 <PERSON><PERSON><PERSON>an Backend

### 1. Install Dependencies
```bash
cd backend
npm install
```

### 2. Start Backend Server
```bash
npm start
```

Backend akan ber<PERSON><PERSON> di: **http://localhost:5000**

## 🧪 Test Endpoints

### 1. Test Backend Connection
```bash
# Test di browser atau Postman
GET http://localhost:5000/api/test

# Expected Response:
{
  "message": "Backend is working!",
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

### 2. Test Login Endpoint
```bash
# Test dengan curl atau Postman
POST http://localhost:5000/api/auth/login
Content-Type: application/json

{
  "username": "admin",
  "password": "admin123"
}

# Expected Response:
{
  "success": true,
  "message": "Login berhasil!",
  "user": {
    "id": 1,
    "username": "admin",
    "email": "<EMAIL>",
    "role": "admin"
  },
  "token": "base64encodedtoken"
}
```

### 3. Test Register Endpoint
```bash
# Test dengan curl atau Postman
POST http://localhost:5000/api/auth/register
Content-Type: application/json

{
  "username": "newadmin",
  "password": "password123",
  "email": "<EMAIL>"
}

# Expected Response:
{
  "success": true,
  "message": "Registrasi berhasil! Silakan login.",
  "user": {
    "id": 1234567890,
    "username": "newadmin",
    "email": "<EMAIL>"
  }
}
```

## 🔍 Debug Login Error

### Error 401 Unauthorized - Possible Causes:

#### 1. **Backend Not Running**
```bash
# Check if backend is running
curl http://localhost:5000/api/test

# If error, start backend:
cd backend
npm start
```

#### 2. **Wrong Credentials**
```javascript
// Default credentials:
username: "admin"
password: "admin123"

// Case sensitive!
```

#### 3. **CORS Issues**
```javascript
// Check browser console for CORS errors
// Backend has CORS enabled, but check if port is correct
```

#### 4. **Network Issues**
```bash
# Check if port 5000 is available
netstat -an | findstr :5000

# Try different port if needed
```

## 🛠️ Troubleshooting Steps

### 1. **Check Backend Logs**
```bash
# Start backend and watch console output
cd backend
npm start

# Look for:
# - "Server running on port 5000"
# - "Login attempt: { username: 'admin', password: 'admin123' }"
# - "Login successful for: admin"
```

### 2. **Test with Browser DevTools**
```javascript
// Open browser console and test directly:
fetch('http://localhost:5000/api/auth/login', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({ username: 'admin', password: 'admin123' })
})
.then(res => res.json())
.then(data => console.log(data))
.catch(err => console.error(err));
```

### 3. **Check Frontend Network Tab**
```
1. Open browser DevTools (F12)
2. Go to Network tab
3. Try login
4. Check the request to /api/auth/login
5. Look at:
   - Request URL
   - Request Method (should be POST)
   - Request Headers
   - Request Payload
   - Response Status
   - Response Body
```

## 🔧 Common Fixes

### Fix 1: **Port Already in Use**
```bash
# Kill process on port 5000
npx kill-port 5000

# Or use different port
# Edit backend/app.js:
const PORT = process.env.PORT || 5001;
```

### Fix 2: **Module Not Found**
```bash
cd backend
rm -rf node_modules
rm package-lock.json
npm install
```

### Fix 3: **CORS Error**
```javascript
// Check if CORS is properly configured in app.js
app.use(cors({
  origin: ['http://localhost:3000', 'http://localhost:3001'],
  credentials: true
}));
```

### Fix 4: **JSON Parsing Error**
```javascript
// Make sure body parser is configured
app.use(express.json());
app.use(express.urlencoded({ extended: true }));
```

## 📋 Backend Status Check

### ✅ **Working Backend Should Show:**
```bash
# Console output:
Server running on port 5000
Connected to database (if using DB)

# Test endpoint works:
GET http://localhost:5000/api/test → 200 OK

# Auth endpoints work:
POST http://localhost:5000/api/auth/login → 200 OK (with valid creds)
POST http://localhost:5000/api/auth/register → 200 OK
```

### ❌ **Problem Indicators:**
```bash
# Backend not starting:
- Port already in use
- Module not found errors
- Syntax errors

# Endpoints not working:
- 404 Not Found → Route not configured
- 500 Internal Server Error → Code error
- CORS errors → CORS not configured
```

## 🎯 Quick Test Script

Create `test-backend.js`:
```javascript
const fetch = require('node-fetch');

async function testBackend() {
  try {
    // Test connection
    const testRes = await fetch('http://localhost:5000/api/test');
    console.log('Test endpoint:', await testRes.json());
    
    // Test login
    const loginRes = await fetch('http://localhost:5000/api/auth/login', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ username: 'admin', password: 'admin123' })
    });
    console.log('Login endpoint:', await loginRes.json());
    
  } catch (error) {
    console.error('Backend test failed:', error.message);
  }
}

testBackend();
```

Run with: `node test-backend.js`

## 🚀 Next Steps

1. **Start Backend**: `cd backend && npm start`
2. **Test Connection**: `http://localhost:5000/api/test`
3. **Test Login**: Use browser or Postman
4. **Check Logs**: Watch console output
5. **Try Frontend**: `http://localhost:3000/admin/login`

Backend dengan authMiddleware sudah dipindahkan dan siap digunakan!
