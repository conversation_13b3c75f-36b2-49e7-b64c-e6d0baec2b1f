<?php
/**
 * Migration script to add the 'image' column to the news table
 * Run this script once to fix the database schema
 */

require_once 'config.php';

try {
    $pdo = getConnection();
    
    // Check if the image column already exists
    $stmt = $pdo->query("DESCRIBE news");
    $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    if (!in_array('image', $columns)) {
        // Add the image column
        $sql = "ALTER TABLE news ADD COLUMN image VARCHAR(255) NULL AFTER content";
        $pdo->exec($sql);
        echo "✅ Successfully added 'image' column to news table.\n";
    } else {
        echo "ℹ️  'image' column already exists in news table.\n";
    }
    
    echo "✅ Migration completed successfully!\n";
    
} catch (Exception $e) {
    echo "❌ Error during migration: " . $e->getMessage() . "\n";
    exit(1);
}
?>
