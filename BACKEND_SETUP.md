# Backend Setup Instructions

## Men<PERSON>lankan Backend Node.js

### 1. Install Dependencies
```bash
cd backend
npm install
```

### 2. Start Backend Server
```bash
npm start
```

Backend akan ber<PERSON>lan di `http://localhost:5000`

## API Endpoints yang Tersedia

### Posts (Berita)
- `GET /api/posts` - Daftar semua berita dengan kategori
- `GET /api/posts/:id` - Detail berita berdasarkan ID
- `POST /api/posts` - Tambah berita baru
- `PUT /api/posts/:id` - Update berita
- `DELETE /api/posts/:id` - <PERSON><PERSON> berita

### Categories (Kategori)
- `GET /api/categories` - Daftar semua kategori
- `GET /api/categories/:id` - Detail kategori berdasarkan ID

### Saved Posts (Berita Tersimpan)
- `GET /api/saved` - Daftar berita yang disimpan
- `POST /api/posts/:id/save` - Simpan berita
- `DELETE /api/posts/:id/save` - <PERSON><PERSON> dari simpan

### Engagement Tracking
- `POST /api/posts/:id/share` - Update share count
- `POST /api/posts/:id/view` - Update view count

### Website Settings
- `GET /api/kostum` - Pengaturan website (logo, nama, dll)

## Troubleshooting

### Error: ERR_CONNECTION_REFUSED
Pastikan backend Node.js berjalan di port 5000:
```bash
cd backend
npm start
```

### Error: Module not found
Install dependencies:
```bash
cd backend
npm install
```

### Error: Database connection
Jika menggunakan database MySQL, pastikan:
1. MySQL/MariaDB running
2. Database `react_news` sudah dibuat
3. Konfigurasi database di `backend/config/db.js` benar

## File Structure Backend

```
backend/
├── app.js                 # Main application
├── package.json          # Dependencies
├── routes/
│   ├── postRoutes.js     # Routes untuk posts
│   ├── categoryRoutes.js # Routes untuk categories
│   └── savedRoutes.js    # Routes untuk saved posts
├── controller/
│   └── postController.js # Controller logic
├── models/
│   └── Post.js          # Data model
└── config/
    └── db.js            # Database configuration
```

## Data Format

### Post Object
```json
{
  "id": 1,
  "title": "Judul Berita",
  "description": "Konten berita",
  "content": "Konten berita",
  "image": "/uploads/image.jpg",
  "category": "teknologi",
  "category_name": "Teknologi",
  "category_color": "#3B82F6",
  "views": 150,
  "share": 25,
  "likes": 45,
  "date": "2024-01-01T00:00:00.000Z",
  "created_at": "2024-01-01T00:00:00.000Z"
}
```

### Category Object
```json
{
  "id": 1,
  "name": "Teknologi",
  "slug": "teknologi",
  "color": "#3B82F6",
  "post_count": 8
}
```

## Next Steps

1. **Start Backend**: `cd backend && npm start`
2. **Start Frontend**: `cd frontend && npm start`
3. **Access Admin**: `http://localhost/react-news/frontend/src/pages/admin/DashboardTable.php`
4. **Access Landing Page**: `http://localhost:3000`

Backend akan menyediakan data untuk:
- ✅ Landing page React (kategori, berita, saved posts)
- ✅ Bottom navigation dengan Font Awesome icons
- ✅ Bookmark functionality
- ✅ Share tracking
- ✅ View tracking
- ✅ Popular posts berdasarkan engagement
