<?php
// <PERSON><PERSON>t to migrate news images from assets/news to uploads folder

require_once __DIR__ . '/../frontend/src/pages/admin/config.php';

$oldNewsDir = __DIR__ . '/../frontend/src/pages/admin/assets/news/';
$newUploadDir = $_SERVER['DOCUMENT_ROOT'] . '/react-news/uploads/';

echo "🖼️  Migrating news images...\n";
echo "From: $oldNewsDir\n";
echo "To: $newUploadDir\n\n";

// Create new upload directory if it doesn't exist
if (!is_dir($newUploadDir)) {
    if (mkdir($newUploadDir, 0755, true)) {
        echo "✅ Created new upload directory: $newUploadDir\n";
    } else {
        echo "❌ Failed to create new upload directory\n";
        exit(1);
    }
} else {
    echo "✅ Upload directory exists\n";
}

// Check if old directory exists
if (!is_dir($oldNewsDir)) {
    echo "ℹ️  Old news directory doesn't exist: $oldNewsDir\n";
} else {
    echo "📁 Old news directory found\n";
    
    // Get all image files from old directory
    $files = glob($oldNewsDir . '*.{jpg,jpeg,png,gif,webp}', GLOB_BRACE);
    $migratedCount = 0;
    $errorCount = 0;

    foreach ($files as $file) {
        if (is_file($file)) {
            $filename = basename($file);
            $newFile = $newUploadDir . $filename;
            
            // Copy file to new location
            if (copy($file, $newFile)) {
                echo "✅ Migrated: $filename\n";
                $migratedCount++;
                
                // Remove old file after successful copy
                if (unlink($file)) {
                    echo "   🗑️  Removed old file\n";
                } else {
                    echo "   ⚠️  Could not remove old file\n";
                }
            } else {
                echo "❌ Failed to migrate: $filename\n";
                $errorCount++;
            }
        }
    }
    
    echo "\n📊 File Migration Summary:\n";
    echo "   Migrated: $migratedCount files\n";
    echo "   Errors: $errorCount files\n\n";
}

// Update database paths
try {
    $pdo = getConnection();
    
    echo "🔄 Updating database paths...\n";
    
    // Update posts table - change assets/news/ paths to /react-news/uploads/
    $stmt = $pdo->prepare("UPDATE posts SET image = REPLACE(image, 'assets/news/', '/react-news/uploads/') WHERE image LIKE 'assets/news/%'");
    $result = $stmt->execute();
    $updatedRows = $stmt->rowCount();
    
    if ($result) {
        echo "✅ Updated $updatedRows news image paths in database\n";
    } else {
        echo "❌ Failed to update database paths\n";
    }
    
    // Show sample of updated paths
    echo "\n📋 Sample updated news images:\n";
    $stmt = $pdo->prepare("SELECT id, title, image FROM posts WHERE image LIKE '/react-news/uploads/%' LIMIT 5");
    $stmt->execute();
    $samples = $stmt->fetchAll();
    
    foreach ($samples as $sample) {
        echo "   ID {$sample['id']}: {$sample['title']}\n";
        echo "   Image: {$sample['image']}\n";
        echo "   URL: http://localhost{$sample['image']}\n\n";
    }
    
} catch (Exception $e) {
    echo "❌ Database error: " . $e->getMessage() . "\n";
}

echo "🎉 Migration completed!\n\n";
echo "📝 Next steps:\n";
echo "   1. Test news images in admin dashboard\n";
echo "   2. Test news images in frontend\n";
echo "   3. Upload a new news image to test new path\n";
echo "   4. Check that URLs are accessible:\n";
echo "      http://localhost/react-news/uploads/news_xxxxx.jpg\n";
echo "   5. Remove old assets/news directory if everything works\n\n";

echo "🔗 Test URLs:\n";
try {
    $stmt = $pdo->prepare("SELECT image FROM posts WHERE image LIKE '/react-news/uploads/%' LIMIT 3");
    $stmt->execute();
    $testImages = $stmt->fetchAll();
    
    foreach ($testImages as $img) {
        echo "   http://localhost{$img['image']}\n";
    }
} catch (Exception $e) {
    echo "   Could not fetch test URLs\n";
}
?>
