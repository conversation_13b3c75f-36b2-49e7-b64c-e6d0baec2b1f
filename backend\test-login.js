// Test script untuk login endpoint
const fetch = require('node-fetch');

async function testLogin() {
  try {
    console.log('Testing backend login endpoint...');
    
    // Test data
    const loginData = {
      username: 'admin',
      password: 'admin123'
    };
    
    console.log('Sending request to: http://localhost:5000/api/auth/login');
    console.log('Request body:', loginData);
    
    const response = await fetch('http://localhost:5000/api/auth/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(loginData)
    });
    
    console.log('Response status:', response.status);
    console.log('Response ok:', response.ok);
    
    const result = await response.json();
    console.log('Response data:', result);
    
    if (response.ok && result.success) {
      console.log('✅ LOGIN TEST PASSED');
    } else {
      console.log('❌ LOGIN TEST FAILED');
    }
    
  } catch (error) {
    console.error('❌ TEST ERROR:', error.message);
    console.log('Make sure backend is running: npm start');
  }
}

// Test connection first
async function testConnection() {
  try {
    console.log('Testing backend connection...');
    
    const response = await fetch('http://localhost:5000/api/test');
    const result = await response.json();
    
    console.log('Connection test:', result);
    
    if (response.ok) {
      console.log('✅ CONNECTION TEST PASSED');
      return true;
    } else {
      console.log('❌ CONNECTION TEST FAILED');
      return false;
    }
  } catch (error) {
    console.error('❌ CONNECTION ERROR:', error.message);
    return false;
  }
}

async function runTests() {
  console.log('='.repeat(50));
  console.log('BACKEND LOGIN TEST');
  console.log('='.repeat(50));
  
  // Test connection first
  const connectionOk = await testConnection();
  
  if (connectionOk) {
    console.log('\n' + '-'.repeat(30));
    await testLogin();
  } else {
    console.log('\n❌ Cannot test login - backend not responding');
    console.log('Please start backend with: cd backend && npm start');
  }
  
  console.log('\n' + '='.repeat(50));
}

runTests();
