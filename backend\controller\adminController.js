// Endpoint untuk mengambil total admin
exports.getTotalAdmin = (req, res) => {
  db.query('SELECT COUNT(*) as total FROM admin', (err, results) => {
    if (err) return res.status(500).json({ error: err.message });
    res.json({ total: results[0].total });
  });
};
// Controller untuk login admin
const db = require('../config/db');
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');

// Register admin
exports.register = (req, res) => {
  const { username, password } = req.body;
  if (!username || !password) {
    return res.status(400).json({ error: 'Username dan password wajib diisi' });
  }
  
  // Check if JWT_SECRET exists
  if (!process.env.JWT_SECRET) {
    return res.status(500).json({ error: 'JWT_SECRET not configured' });
  }
  
  db.query('SELECT * FROM admin WHERE username = ?', [username], (err, results) => {
    if (err) return res.status(500).json({ error: err.message });
    if (results.length > 0) return res.status(409).json({ error: 'Username sudah terdaftar' });
    bcrypt.hash(password, 10, (err, hash) => {
      if (err) return res.status(500).json({ error: err.message });
      db.query('INSERT INTO admin (username, password) VALUES (?, ?)', [username, hash], (err, result) => {
        if (err) return res.status(500).json({ error: err.message });
        // Generate JWT token after successful registration
        const token = jwt.sign({ id: result.insertId, username: username }, process.env.JWT_SECRET, { expiresIn: '1d' });
        res.json({ message: 'Registrasi berhasil', token: token });
      });
    });
  });
};

exports.login = (req, res) => {
  const { username, password } = req.body;
  
  // Check if JWT_SECRET exists
  if (!process.env.JWT_SECRET) {
    return res.status(500).json({ error: 'JWT_SECRET not configured' });
  }
  
  db.query('SELECT * FROM admin WHERE username = ?', [username], (err, results) => {
    if (err) return res.status(500).json({ error: 'Database error: ' + err.message });
    if (results.length === 0) return res.status(401).json({ error: 'Username tidak ditemukan' });
    const admin = results[0];
    bcrypt.compare(password, admin.password, (err, isMatch) => {
      if (err) return res.status(500).json({ error: 'Bcrypt error: ' + err.message });
      if (!isMatch) return res.status(401).json({ error: 'Password salah' });
      if (!process.env.JWT_SECRET) return res.status(500).json({ error: 'JWT_SECRET belum diatur di .env' });
      try {
        const token = jwt.sign({ id: admin.id, username: admin.username }, process.env.JWT_SECRET, { expiresIn: '1d' });
        res.json({ token });
      } catch (e) {
        res.status(500).json({ error: 'JWT error: ' + e.message });
      }
    });
  });
};
