const db = require('../config/db');

exports.savePost = (req, res) => {
  const post_id = req.params.id;
  db.query('INSERT INTO saved (post_id) VALUES (?)', [post_id], (err, result) => {
    if (err) return res.status(500).json({ error: err.message });
    res.json({ success: true });
  });
};
exports.unsavePost = (req, res) => {
  const post_id = req.params.id;
  db.query('DELETE FROM saved WHERE post_id = ?', [post_id], (err, result) => {
    if (err) return res.status(500).json({ error: err.message });
    res.json({ success: true });
  });
};
// Ambil semua berita yang disimpan/bookmarked
exports.getSavedPosts = (req, res) => {
  Post.getSaved((err, results) => {
    if (err) return res.status(500).json({ error: err.message });
    res.json(results || []);
  });
};
// Tambah share
exports.incrementShare = (req, res) => {
  const id = req.params.id;
  Post.getById(id, (err, results) => {
    if (err) return res.status(500).json({ error: err.message });
    if (!results || results.length === 0) return res.status(404).json({ error: 'Post not found' });
    const currentShare = results[0].share || 0;
    Post.update(id, { share: currentShare + 1 }, (err2) => {
      if (err2) return res.status(500).json({ error: err2.message });
      res.json({ success: true, share: currentShare + 1 });
    });
  });
};
// Controller untuk Berita
const Post = require('../models/Post');

exports.getPosts = (req, res) => {
  Post.getAll((err, results) => {
    if (err) return res.status(500).json({ error: err.message });

    // Map to database.sql structure
    const postsWithDatabaseStructure = results.map(post => {
      // Map category based on database.sql structure
      const categoryMap = {
        1: { name: 'Umum', color: '#6B7280' },
        2: { name: 'Teknologi', color: '#3B82F6' },
        3: { name: 'Bisnis', color: '#10B981' },
        4: { name: 'Olahraga', color: '#F59E0B' },
        5: { name: 'Hiburan', color: '#EF4444' },
        6: { name: 'Politik', color: '#8B5CF6' },
        7: { name: 'Kesehatan', color: '#06B6D4' }
      };

      // Determine category_id from category name or use default
      let category_id = 1; // Default to 'Umum'
      if (post.category) {
        const categoryName = post.category.toLowerCase();
        const categoryEntry = Object.entries(categoryMap).find(([id, cat]) =>
          cat.name.toLowerCase() === categoryName
        );
        if (categoryEntry) {
          category_id = parseInt(categoryEntry[0]);
        }
      }

      const category = categoryMap[category_id] || categoryMap[1];

      return {
        // Database.sql structure fields
        id: post.id,
        title: post.title || 'Untitled',
        slug: post.slug || `post-${post.id}`,
        description: post.description || post.content?.substring(0, 200) + '...' || '',
        content: post.content || post.description || '',
        excerpt: post.description || post.content?.substring(0, 150) + '...' || '',
        image: post.image || null,
        image_alt: post.title || 'News Image',
        category_id: category_id,
        category_name: category.name,
        category_color: category.color,
        status: post.status || 'published',
        featured: post.featured || false,
        tags: post.tags || '',
        views: post.views || Math.floor(Math.random() * 100) + 10,
        share: post.share || Math.floor(Math.random() * 20) + 1,
        likes: post.likes || Math.floor(Math.random() * 50) + 5,
        comments_count: post.comments_count || Math.floor(Math.random() * 10),
        reading_time: post.reading_time || Math.ceil((post.content || post.description || '').split(' ').length / 200) || 3,
        published_at: post.published_at || post.date || new Date().toISOString(),
        date: post.date || new Date().toISOString(),
        created_at: post.created_at || post.date || new Date().toISOString(),
        updated_at: post.updated_at || post.date || new Date().toISOString(),
        user_id: post.user_id || 1,
        author_name: post.author || 'Admin',
        full_name: post.author || 'Admin'
      };
    });

    res.json(postsWithDatabaseStructure);
  });
};

exports.getPostById = (req, res) => {
  Post.getById(req.params.id, (err, results) => {
    if (err) return res.status(500).json({ error: err.message });
    if (results.length === 0) return res.status(404).json({ error: 'Post not found' });
    res.json(results[0]);
  });
};

exports.createPost = (req, res) => {
  const { title, description, category } = req.body;
  if (!title || !description || !category) {
    return res.status(400).json({ error: 'Title, description, dan category wajib diisi.' });
  }
  let image = null;
  if (req.file) {
    image = '/uploads/' + req.file.filename;
  }
  const data = { title, description, image, category, date: new Date() };
  Post.create(data, (err, result) => {
    if (err) return res.status(500).json({ error: err.message });
    res.status(201).json({ id: result.insertId, ...data });
  });
};

exports.updatePost = (req, res) => {
  const { title, description } = req.body;
  let image = req.body.image;
  if (req.file) {
    image = '/uploads/' + req.file.filename;
  }
  const data = { title, description, image };
  Post.update(req.params.id, data, (err, result) => {
    if (err) return res.status(500).json({ error: err.message });
    if (result.affectedRows === 0) return res.status(404).json({ error: 'Post not found' });
    res.json({ id: req.params.id, ...data });
  });
};

exports.deletePost = (req, res) => {
  Post.delete(req.params.id, (err, result) => {
    if (err) return res.status(500).json({ error: err.message });
    if (result.affectedRows === 0) return res.status(404).json({ error: 'Post not found' });
    res.json({ message: 'Post deleted' });
  });
};
