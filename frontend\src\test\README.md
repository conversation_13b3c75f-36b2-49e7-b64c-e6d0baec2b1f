# Admin Dashboard - Professional UI/UX

Admin dashboard yang profesional dan responsif dengan fitur lengkap untuk manajemen website. Dibangun dengan PHP, MySQL, Tailwind CSS, dan Font Awesome.

## 🚀 Fitur Utama

### 📊 Dashboard
- **Statistik Real-time**: Menampilkan total users, berita, page views, dan revenue
- **Grafik & Chart**: Area untuk menampilkan visualisasi data
- **Cards Responsif**: Layout yang menyesuaikan dengan berbagai ukuran layar

### 📰 Manajemen Berita (CRUD)
- **Create**: Tambah berita baru dengan editor yang user-friendly
- **Read**: Tampilkan daftar berita dengan pagination
- **Update**: Edit berita yang sudah ada
- **Delete**: Hapus berita dengan konfirmasi
- **Status Management**: Draft dan Published status

### ⚙️ Pengaturan Website
- **Nama Website**: Ubah nama website secara dinamis
- **Logo Upload**: Upload dan ganti logo website
- **Kustomisasi Warna**: 
  - Primary Color
  - Secondary Color  
  - Accent Color
- **Live Preview**: Perubahan warna langsung terlihat

### 📱 Responsive Design
- **Mobile First**: Optimized untuk semua perangkat
- **Sidebar Collapsible**: Menu samping yang dapat disembunyikan
- **Touch Friendly**: Interface yang mudah digunakan di perangkat sentuh
- **Cross Browser**: Compatible dengan semua browser modern

## 🛠️ Teknologi yang Digunakan

- **Backend**: PHP 7.4+
- **Database**: MySQL 5.7+
- **Frontend**: HTML5, CSS3, JavaScript (ES6+)
- **CSS Framework**: Tailwind CSS 3.x
- **Icons**: Font Awesome 6.4
- **AJAX**: Fetch API untuk komunikasi asynchronous

## 📋 Persyaratan Sistem

- PHP 7.4 atau lebih tinggi
- MySQL 5.7 atau lebih tinggi
- Web server (Apache/Nginx)
- Browser modern (Chrome, Firefox, Safari, Edge)

## 🚀 Instalasi

### 1. Clone atau Download
```bash
git clone [repository-url]
# atau download dan extract file
```

### 2. Setup Database
```bash
# Import database
mysql -u username -p < database.sql

# Atau jalankan query di phpMyAdmin/MySQL client
```

### 3. Konfigurasi Database
Edit file `config.php`:
```php
define('DB_HOST', 'localhost');
define('DB_USER', 'your_username');
define('DB_PASS', 'your_password');
define('DB_NAME', 'admin_dashboard');
```

### 4. Setup Permissions
```bash
# Buat folder assets untuk upload
mkdir assets
chmod 755 assets

# Set permissions untuk upload
chmod 644 *.php
```

### 5. Akses Dashboard
Buka browser dan akses:
```
http://localhost/dashboard.php
```

## 📁 Struktur File

```
admin-dashboard/
├── dashboard.php          # File utama dashboard
├── config.php            # Konfigurasi database dan fungsi
├── api.php               # API endpoint untuk AJAX
├── database.sql          # Script setup database
├── assets/               # Folder untuk upload file
│   └── logo.png         # Logo default
└── README.md            # Dokumentasi
```

## 🎨 Kustomisasi

### Mengubah Warna Tema
1. Masuk ke menu **Pengaturan**
2. Pilih tab **Pengaturan Warna**
3. Pilih warna menggunakan color picker atau input hex
4. Klik **Terapkan Warna**
5. Refresh halaman untuk melihat perubahan

### Upload Logo
1. Masuk ke menu **Pengaturan**
2. Klik **Upload Logo**
3. Pilih file gambar (JPG, PNG, GIF)
4. Logo akan otomatis ter-resize dan tersimpan

### Menambah Berita
1. Masuk ke menu **Berita**
2. Klik **Tambah Berita**
3. Isi judul dan konten
4. Pilih status (Draft/Published)
5. Klik **Simpan**

## 🔧 API Endpoints

### News Management
- `GET api.php?action=get_news` - Ambil semua berita
- `POST api.php` dengan `action=add_news` - Tambah berita
- `POST api.php` dengan `action=update_news` - Update berita
- `POST api.php` dengan `action=delete_news` - Hapus berita

### Settings Management
- `GET api.php?action=get_settings` - Ambil pengaturan
- `POST api.php` dengan `action=update_settings` - Update pengaturan
- `POST api.php` dengan `action=upload_logo` - Upload logo

### Statistics
- `GET api.php?action=get_stats` - Ambil statistik dashboard

## 🎯 Fitur Mendatang

- [ ] Authentication & Authorization
- [ ] User Management
- [ ] Advanced Analytics
- [ ] Export Data (PDF, Excel)
- [ ] Email Notifications
- [ ] Multi-language Support
- [ ] Dark Mode Theme
- [ ] Advanced Text Editor (WYSIWYG)

## 🐛 Troubleshooting

### Database Connection Error
- Pastikan MySQL service berjalan
- Cek kredensial database di `config.php`
- Pastikan database sudah dibuat

### Upload Error
- Cek permissions folder `assets/`
- Pastikan file size tidak melebihi limit PHP
- Cek format file yang diupload

### JavaScript Error
- Pastikan browser mendukung ES6+
- Cek console browser untuk error detail
- Pastikan koneksi internet untuk CDN

## 📞 Support

Jika mengalami masalah atau butuh bantuan:
1. Cek dokumentasi di README.md
2. Lihat error log di browser console
3. Pastikan semua requirements terpenuhi

## 📄 License

Project ini menggunakan MIT License. Bebas digunakan untuk keperluan komersial maupun non-komersial.

---

**Dibuat dengan ❤️ menggunakan PHP, MySQL, Tailwind CSS, dan Font Awesome**
