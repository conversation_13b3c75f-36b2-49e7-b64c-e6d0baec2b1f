const express = require('express');
const router = express.Router();

// Dummy saved posts data (replace with database calls later)
let savedPosts = [
  {
    id: 1,
    title: 'Berita Tersimpan 1',
    content: 'Ini adalah contoh berita yang telah disimpan oleh user.',
    description: 'Ini adalah contoh berita yang telah disimpan oleh user.',
    image: 'https://source.unsplash.com/400x300/?news',
    category: 'Teknologi',
    category_name: 'Teknologi',
    category_color: '#3B82F6',
    date: new Date().toISOString(),
    created_at: new Date().toISOString(),
    views: 150,
    share: 25,
    likes: 45
  },
  {
    id: 2,
    title: 'Berita Tersimpan 2',
    content: 'Contoh berita kedua yang disimpan dalam bookmark.',
    description: 'Contoh berita kedua yang disimpan dalam bookmark.',
    image: 'https://source.unsplash.com/400x300/?technology',
    category: 'Bisnis',
    category_name: '<PERSON><PERSON><PERSON>',
    category_color: '#10B981',
    date: new Date(Date.now() - 86400000).toISOString(),
    created_at: new Date(Date.now() - 86400000).toISOString(),
    views: 89,
    share: 12,
    likes: 23
  }
];

// Get all saved posts
router.get('/saved', (req, res) => {
  try {
    res.json(savedPosts);
  } catch (error) {
    console.error('Error fetching saved posts:', error);
    res.status(500).json({ error: 'Failed to fetch saved posts' });
  }
});

// Save a post
router.post('/:id/save', (req, res) => {
  try {
    const postId = parseInt(req.params.id);

    // Check if post is already saved
    const existingSaved = savedPosts.find(post => post.id === postId);
    if (existingSaved) {
      return res.json({ success: false, message: 'Post already saved' });
    }

    // In a real app, you would fetch the post from database
    // For now, create a dummy saved post
    const newSavedPost = {
      id: postId,
      title: `Saved Post ${postId}`,
      content: 'This is a saved post content.',
      description: 'This is a saved post content.',
      image: `https://source.unsplash.com/400x300/?post${postId}`,
      category: 'Umum',
      category_name: 'Umum',
      category_color: '#6B7280',
      date: new Date().toISOString(),
      created_at: new Date().toISOString(),
      views: Math.floor(Math.random() * 100),
      share: Math.floor(Math.random() * 20),
      likes: Math.floor(Math.random() * 50)
    };

    savedPosts.push(newSavedPost);
    res.json({ success: true, message: 'Post saved successfully' });
  } catch (error) {
    console.error('Error saving post:', error);
    res.status(500).json({ error: 'Failed to save post' });
  }
});

// Unsave a post
router.delete('/:id/save', (req, res) => {
  try {
    const postId = parseInt(req.params.id);
    const initialLength = savedPosts.length;
    savedPosts = savedPosts.filter(post => post.id !== postId);

    if (savedPosts.length < initialLength) {
      res.json({ success: true, message: 'Post unsaved successfully' });
    } else {
      res.json({ success: false, message: 'Post not found in saved list' });
    }
  } catch (error) {
    console.error('Error unsaving post:', error);
    res.status(500).json({ error: 'Failed to unsave post' });
  }
});

// Update share count
router.post('/:id/share', (req, res) => {
  try {
    const postId = parseInt(req.params.id);
    console.log(`Share count updated for post ${postId}`);
    res.json({ success: true, message: 'Share count updated' });
  } catch (error) {
    console.error('Error updating share count:', error);
    res.status(500).json({ error: 'Failed to update share count' });
  }
});

// Update view count
router.post('/:id/view', (req, res) => {
  try {
    const postId = parseInt(req.params.id);
    console.log(`View count updated for post ${postId}`);
    res.json({ success: true, message: 'View count updated' });
  } catch (error) {
    console.error('Error updating view count:', error);
    res.status(500).json({ error: 'Failed to update view count' });
  }
});

module.exports = router;
