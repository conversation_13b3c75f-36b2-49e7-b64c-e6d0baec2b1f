const express = require('express');
const router = express.Router();

// Categories data matching database.sql structure
const categories = [
  {
    id: 1,
    name: 'Um<PERSON>',
    slug: 'umum',
    description: 'Berita umum dan informasi terkini',
    color: '#6B7280',
    is_active: true,
    post_count: 5,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  },
  {
    id: 2,
    name: 'Teknologi',
    slug: 'teknologi',
    description: 'Berita teknologi dan inovasi digital',
    color: '#3B82F6',
    is_active: true,
    post_count: 8,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  },
  {
    id: 3,
    name: '<PERSON><PERSON><PERSON>',
    slug: 'bisnis',
    description: 'Berita bisnis dan ekonomi',
    color: '#10B981',
    is_active: true,
    post_count: 3,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  },
  {
    id: 4,
    name: '<PERSON>lah<PERSON>',
    slug: 'olah<PERSON>',
    description: 'Berita olahraga dan kompetisi',
    color: '#F59E0B',
    is_active: true,
    post_count: 6,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  },
  {
    id: 5,
    name: 'Hiburan',
    slug: 'hiburan',
    description: 'Berita hiburan dan selebriti',
    color: '#EF4444',
    is_active: true,
    post_count: 4,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  },
  {
    id: 6,
    name: 'Politik',
    slug: 'politik',
    description: 'Berita politik dan pemerintahan',
    color: '#8B5CF6',
    is_active: true,
    post_count: 2,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  },
  {
    id: 7,
    name: 'Kesehatan',
    slug: 'kesehatan',
    description: 'Berita kesehatan dan medis',
    color: '#06B6D4',
    is_active: true,
    post_count: 7,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  }
];

// Get all categories
router.get('/', (req, res) => {
  try {
    res.json(categories);
  } catch (error) {
    console.error('Error fetching categories:', error);
    res.status(500).json({ error: 'Failed to fetch categories' });
  }
});

// Get category by ID
router.get('/:id', (req, res) => {
  try {
    const categoryId = parseInt(req.params.id);
    const category = categories.find(cat => cat.id === categoryId);
    
    if (!category) {
      return res.status(404).json({ error: 'Category not found' });
    }
    
    res.json(category);
  } catch (error) {
    console.error('Error fetching category:', error);
    res.status(500).json({ error: 'Failed to fetch category' });
  }
});

module.exports = router;
