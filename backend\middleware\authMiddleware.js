const express = require('express');

// Authentication middleware for API routes
const authMiddleware = (req, res, next) => {
  try {
    // Get token from header
    const authHeader = req.headers.authorization;
    const token = authHeader && authHeader.split(' ')[1]; // Bearer TOKEN

    if (!token) {
      return res.status(401).json({
        success: false,
        message: 'Access token required'
      });
    }

    // Verify token (simple base64 decode for demo)
    try {
      const decoded = JSON.parse(Buffer.from(token, 'base64').toString());
      
      // Check if token is valid (basic validation)
      if (!decoded.id || !decoded.username || !decoded.timestamp) {
        throw new Error('Invalid token structure');
      }

      // Check token age (optional - 24 hours)
      const tokenAge = Date.now() - decoded.timestamp;
      const maxAge = 24 * 60 * 60 * 1000; // 24 hours
      
      if (tokenAge > maxAge) {
        return res.status(401).json({
          success: false,
          message: 'Token expired'
        });
      }

      // Add user info to request
      req.user = {
        id: decoded.id,
        username: decoded.username
      };

      next();
    } catch (error) {
      return res.status(401).json({
        success: false,
        message: 'Invalid token'
      });
    }
  } catch (error) {
    return res.status(500).json({
      success: false,
      message: 'Authentication error'
    });
  }
};

// Middleware to check admin role
const adminMiddleware = (req, res, next) => {
  if (!req.user) {
    return res.status(401).json({
      success: false,
      message: 'Authentication required'
    });
  }

  // For demo, all authenticated users are admin
  // In real app, check user role from database
  next();
};

// Authentication routes
const authRoutes = express.Router();

// Login endpoint
authRoutes.post('/login', (req, res) => {
  try {
    console.log('=== LOGIN ATTEMPT ===');
    console.log('Request headers:', req.headers);
    console.log('Request body:', req.body);
    console.log('Body type:', typeof req.body);
    console.log('Body keys:', Object.keys(req.body || {}));

    // Check if body exists
    if (!req.body) {
      console.log('ERROR: No request body');
      return res.status(400).json({
        success: false,
        message: 'Request body is required'
      });
    }

    const { username, password } = req.body;
    console.log('Extracted username:', username);
    console.log('Extracted password:', password ? '[HIDDEN]' : 'undefined');

    // Validation
    if (!username || !password) {
      console.log('ERROR: Missing username or password');
      return res.status(400).json({
        success: false,
        message: 'Username dan password harus diisi!'
      });
    }

    // Simple validation (in real app, check database)
    if (username === 'admin' && password === 'admin123') {
      const user = {
        id: 1,
        username: 'admin',
        email: '<EMAIL>',
        role: 'admin'
      };

      // Generate simple token
      const token = Buffer.from(JSON.stringify({
        id: user.id,
        username: user.username,
        timestamp: Date.now()
      })).toString('base64');

      console.log('SUCCESS: Login successful for:', username);

      return res.json({
        success: true,
        message: 'Login berhasil!',
        user: user,
        token: token
      });
    } else {
      console.log('ERROR: Invalid credentials for:', username);

      return res.status(400).json({
        success: false,
        message: 'Username atau password salah!'
      });
    }
  } catch (error) {
    console.error('FATAL ERROR in login:', error);

    return res.status(500).json({
      success: false,
      message: 'Server error: ' + error.message
    });
  }
});

// Register endpoint
authRoutes.post('/register', (req, res) => {
  try {
    console.log('Register attempt:', req.body); // Debug log

    const { username, password, email } = req.body;

    // Basic validation
    if (!username || !password || !email) {
      return res.status(400).json({
        success: false,
        message: 'Semua field harus diisi!'
      });
    }

    if (password.length < 6) {
      return res.status(400).json({
        success: false,
        message: 'Password minimal 6 karakter!'
      });
    }

    // Check if user already exists (simple check)
    if (username === 'admin') {
      return res.status(400).json({
        success: false,
        message: 'Username sudah digunakan!'
      });
    }

    // In real app, save to database
    const newUser = {
      id: Date.now(), // Simple ID generation
      username,
      email,
      role: 'admin',
      createdAt: new Date().toISOString()
    };

    console.log('Registration successful for:', username); // Debug log

    res.json({
      success: true,
      message: 'Registrasi berhasil! Silakan login.',
      user: {
        id: newUser.id,
        username: newUser.username,
        email: newUser.email
      }
    });
  } catch (error) {
    console.error('Register error:', error); // Debug log

    res.status(500).json({
      success: false,
      message: 'Server error: ' + error.message
    });
  }
});

// Logout endpoint
authRoutes.post('/logout', (req, res) => {
  // In real app, invalidate token in database
  res.json({
    success: true,
    message: 'Logout berhasil!'
  });
});

// Check authentication status
authRoutes.get('/me', authMiddleware, (req, res) => {
  res.json({
    success: true,
    user: req.user
  });
});

module.exports = {
  authMiddleware,
  adminMiddleware,
  authRoutes
};
