{"ast": null, "code": "var _jsxFileName = \"C:\\\\laragon\\\\www\\\\react-news\\\\frontend\\\\src\\\\App.js\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React from 'react';\nimport { BrowserRouter as Router, Routes, Route } from 'react-router-dom';\nimport LandingPage from './pages/user/LandingPage';\nimport DataNews from './pages/user/data-news';\nimport Saved from './pages/user/components/Saved';\nimport './App.css';\nimport Login from './pages/admin/auth/Login';\nimport Register from './pages/admin/auth/Register';\n\n// Component untuk embed DashboardTable.php tanpa menampilkan .php di URL\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst DashboardTable = () => {\n  _s();\n  React.useEffect(() => {\n    // Load DashboardTable.php content in iframe\n    const iframe = document.getElementById('dashboard-iframe');\n    if (iframe) {\n      iframe.src = 'http://localhost/react-news/frontend/src/pages/admin/DashboardTable.php';\n    }\n  }, []);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      width: '100%',\n      height: '100vh',\n      margin: 0,\n      padding: 0\n    },\n    children: /*#__PURE__*/_jsxDEV(\"iframe\", {\n      id: \"dashboard-iframe\",\n      src: \"about:blank\",\n      style: {\n        width: '100%',\n        height: '100%',\n        border: 'none',\n        margin: 0,\n        padding: 0\n      },\n      title: \"Admin Dashboard\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 22,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 21,\n    columnNumber: 5\n  }, this);\n};\n\n// Component untuk redirect ke dashboard\n_s(DashboardTable, \"OD7bBpZva5O2jO+Puf00hKivP7c=\");\n_c = DashboardTable;\nconst AdminRedirect = () => {\n  _s2();\n  React.useEffect(() => {\n    // Redirect to clean dashboard URL\n    window.location.href = '/dashboard';\n  }, []);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      padding: '20px',\n      textAlign: 'center',\n      fontFamily: 'Arial, sans-serif'\n    },\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        maxWidth: '400px',\n        margin: '0 auto',\n        marginTop: '100px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        style: {\n          color: '#3B82F6',\n          marginBottom: '20px'\n        },\n        children: \"\\uD83D\\uDE80 Redirecting to Dashboard...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 48,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        style: {\n          fontSize: '16px',\n          color: '#666'\n        },\n        children: \"Mengalihkan ke dashboard admin...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 49,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 47,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 46,\n    columnNumber: 5\n  }, this);\n};\n_s2(AdminRedirect, \"OD7bBpZva5O2jO+Puf00hKivP7c=\");\n_c2 = AdminRedirect;\nfunction App() {\n  return /*#__PURE__*/_jsxDEV(Router, {\n    children: /*#__PURE__*/_jsxDEV(Routes, {\n      children: [/*#__PURE__*/_jsxDEV(Route, {\n        path: \"/\",\n        element: /*#__PURE__*/_jsxDEV(LandingPage, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 62,\n          columnNumber: 34\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 62,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/home\",\n        element: /*#__PURE__*/_jsxDEV(LandingPage, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 63,\n          columnNumber: 38\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 63,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/saved\",\n        element: /*#__PURE__*/_jsxDEV(Saved, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 64,\n          columnNumber: 39\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 64,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/bookmark\",\n        element: /*#__PURE__*/_jsxDEV(Saved, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 65,\n          columnNumber: 42\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 65,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/admin\",\n        element: /*#__PURE__*/_jsxDEV(AdminRedirect, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 68,\n          columnNumber: 39\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 68,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/admin/login\",\n        element: /*#__PURE__*/_jsxDEV(Login, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 69,\n          columnNumber: 45\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 69,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/admin/register\",\n        element: /*#__PURE__*/_jsxDEV(Register, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 70,\n          columnNumber: 48\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 70,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/dashboard\",\n        element: /*#__PURE__*/_jsxDEV(DashboardTable, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 73,\n          columnNumber: 43\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 73,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/admin/dashboard\",\n        element: /*#__PURE__*/_jsxDEV(DashboardTable, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 74,\n          columnNumber: 49\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 74,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"*\",\n        element: /*#__PURE__*/_jsxDEV(LandingPage, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 77,\n          columnNumber: 34\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 77,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 60,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 59,\n    columnNumber: 5\n  }, this);\n}\n_c3 = App;\nexport default App;\nvar _c, _c2, _c3;\n$RefreshReg$(_c, \"DashboardTable\");\n$RefreshReg$(_c2, \"AdminRedirect\");\n$RefreshReg$(_c3, \"App\");", "map": {"version": 3, "names": ["React", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Router", "Routes", "Route", "LandingPage", "DataNews", "Saved", "<PERSON><PERSON>", "Register", "jsxDEV", "_jsxDEV", "DashboardTable", "_s", "useEffect", "iframe", "document", "getElementById", "src", "style", "width", "height", "margin", "padding", "children", "id", "border", "title", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "AdminRedirect", "_s2", "window", "location", "href", "textAlign", "fontFamily", "max<PERSON><PERSON><PERSON>", "marginTop", "color", "marginBottom", "fontSize", "_c2", "App", "path", "element", "_c3", "$RefreshReg$"], "sources": ["C:/laragon/www/react-news/frontend/src/App.js"], "sourcesContent": ["import React from 'react';\nimport { BrowserRouter as Router, Routes, Route } from 'react-router-dom';\nimport LandingPage from './pages/user/LandingPage';\nimport DataNews from './pages/user/data-news';\nimport Saved from './pages/user/components/Saved';\nimport './App.css';\nimport Login from './pages/admin/auth/Login';\nimport Register from './pages/admin/auth/Register';\n\n// Component untuk embed DashboardTable.php tanpa menampilkan .php di URL\nconst DashboardTable = () => {\n  React.useEffect(() => {\n    // Load DashboardTable.php content in iframe\n    const iframe = document.getElementById('dashboard-iframe');\n    if (iframe) {\n      iframe.src = 'http://localhost/react-news/frontend/src/pages/admin/DashboardTable.php';\n    }\n  }, []);\n\n  return (\n    <div style={{ width: '100%', height: '100vh', margin: 0, padding: 0 }}>\n      <iframe\n        id=\"dashboard-iframe\"\n        src=\"about:blank\"\n        style={{\n          width: '100%',\n          height: '100%',\n          border: 'none',\n          margin: 0,\n          padding: 0\n        }}\n        title=\"Admin Dashboard\"\n      />\n    </div>\n  );\n};\n\n// Component untuk redirect ke dashboard\nconst AdminRedirect = () => {\n  React.useEffect(() => {\n    // Redirect to clean dashboard URL\n    window.location.href = '/dashboard';\n  }, []);\n\n  return (\n    <div style={{ padding: '20px', textAlign: 'center', fontFamily: 'Arial, sans-serif' }}>\n      <div style={{ maxWidth: '400px', margin: '0 auto', marginTop: '100px' }}>\n        <h2 style={{ color: '#3B82F6', marginBottom: '20px' }}>🚀 Redirecting to Dashboard...</h2>\n        <p style={{ fontSize: '16px', color: '#666' }}>\n          Mengalihkan ke dashboard admin...\n        </p>\n      </div>\n    </div>\n  );\n};\n\nfunction App() {\n  return (\n    <Router>\n      <Routes>\n        {/* User Routes */}\n        <Route path=\"/\" element={<LandingPage />} />\n        <Route path=\"/home\" element={<LandingPage />} />\n        <Route path=\"/saved\" element={<Saved />} />\n        <Route path=\"/bookmark\" element={<Saved />} />\n\n        {/* Admin Routes */}\n        <Route path=\"/admin\" element={<AdminRedirect />} />\n        <Route path=\"/admin/login\" element={<Login />} />\n        <Route path=\"/admin/register\" element={<Register />} />\n\n        {/* Dashboard Routes - Clean URLs without .php */}\n        <Route path=\"/dashboard\" element={<DashboardTable />} />\n        <Route path=\"/admin/dashboard\" element={<DashboardTable />} />\n\n        {/* Fallback Route */}\n        <Route path=\"*\" element={<LandingPage />} />\n      </Routes>\n    </Router>\n  );\n}\n\nexport default App;\n"], "mappings": ";;;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,aAAa,IAAIC,MAAM,EAAEC,MAAM,EAAEC,KAAK,QAAQ,kBAAkB;AACzE,OAAOC,WAAW,MAAM,0BAA0B;AAClD,OAAOC,QAAQ,MAAM,wBAAwB;AAC7C,OAAOC,KAAK,MAAM,+BAA+B;AACjD,OAAO,WAAW;AAClB,OAAOC,KAAK,MAAM,0BAA0B;AAC5C,OAAOC,QAAQ,MAAM,6BAA6B;;AAElD;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,cAAc,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC3Bb,KAAK,CAACc,SAAS,CAAC,MAAM;IACpB;IACA,MAAMC,MAAM,GAAGC,QAAQ,CAACC,cAAc,CAAC,kBAAkB,CAAC;IAC1D,IAAIF,MAAM,EAAE;MACVA,MAAM,CAACG,GAAG,GAAG,yEAAyE;IACxF;EACF,CAAC,EAAE,EAAE,CAAC;EAEN,oBACEP,OAAA;IAAKQ,KAAK,EAAE;MAAEC,KAAK,EAAE,MAAM;MAAEC,MAAM,EAAE,OAAO;MAAEC,MAAM,EAAE,CAAC;MAAEC,OAAO,EAAE;IAAE,CAAE;IAAAC,QAAA,eACpEb,OAAA;MACEc,EAAE,EAAC,kBAAkB;MACrBP,GAAG,EAAC,aAAa;MACjBC,KAAK,EAAE;QACLC,KAAK,EAAE,MAAM;QACbC,MAAM,EAAE,MAAM;QACdK,MAAM,EAAE,MAAM;QACdJ,MAAM,EAAE,CAAC;QACTC,OAAO,EAAE;MACX,CAAE;MACFI,KAAK,EAAC;IAAiB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACxB;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEV,CAAC;;AAED;AAAAlB,EAAA,CA3BMD,cAAc;AAAAoB,EAAA,GAAdpB,cAAc;AA4BpB,MAAMqB,aAAa,GAAGA,CAAA,KAAM;EAAAC,GAAA;EAC1BlC,KAAK,CAACc,SAAS,CAAC,MAAM;IACpB;IACAqB,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,YAAY;EACrC,CAAC,EAAE,EAAE,CAAC;EAEN,oBACE1B,OAAA;IAAKQ,KAAK,EAAE;MAAEI,OAAO,EAAE,MAAM;MAAEe,SAAS,EAAE,QAAQ;MAAEC,UAAU,EAAE;IAAoB,CAAE;IAAAf,QAAA,eACpFb,OAAA;MAAKQ,KAAK,EAAE;QAAEqB,QAAQ,EAAE,OAAO;QAAElB,MAAM,EAAE,QAAQ;QAAEmB,SAAS,EAAE;MAAQ,CAAE;MAAAjB,QAAA,gBACtEb,OAAA;QAAIQ,KAAK,EAAE;UAAEuB,KAAK,EAAE,SAAS;UAAEC,YAAY,EAAE;QAAO,CAAE;QAAAnB,QAAA,EAAC;MAA8B;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC1FpB,OAAA;QAAGQ,KAAK,EAAE;UAAEyB,QAAQ,EAAE,MAAM;UAAEF,KAAK,EAAE;QAAO,CAAE;QAAAlB,QAAA,EAAC;MAE/C;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACG,GAAA,CAhBID,aAAa;AAAAY,GAAA,GAAbZ,aAAa;AAkBnB,SAASa,GAAGA,CAAA,EAAG;EACb,oBACEnC,OAAA,CAACT,MAAM;IAAAsB,QAAA,eACLb,OAAA,CAACR,MAAM;MAAAqB,QAAA,gBAELb,OAAA,CAACP,KAAK;QAAC2C,IAAI,EAAC,GAAG;QAACC,OAAO,eAAErC,OAAA,CAACN,WAAW;UAAAuB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC5CpB,OAAA,CAACP,KAAK;QAAC2C,IAAI,EAAC,OAAO;QAACC,OAAO,eAAErC,OAAA,CAACN,WAAW;UAAAuB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAChDpB,OAAA,CAACP,KAAK;QAAC2C,IAAI,EAAC,QAAQ;QAACC,OAAO,eAAErC,OAAA,CAACJ,KAAK;UAAAqB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC3CpB,OAAA,CAACP,KAAK;QAAC2C,IAAI,EAAC,WAAW;QAACC,OAAO,eAAErC,OAAA,CAACJ,KAAK;UAAAqB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAG9CpB,OAAA,CAACP,KAAK;QAAC2C,IAAI,EAAC,QAAQ;QAACC,OAAO,eAAErC,OAAA,CAACsB,aAAa;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACnDpB,OAAA,CAACP,KAAK;QAAC2C,IAAI,EAAC,cAAc;QAACC,OAAO,eAAErC,OAAA,CAACH,KAAK;UAAAoB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACjDpB,OAAA,CAACP,KAAK;QAAC2C,IAAI,EAAC,iBAAiB;QAACC,OAAO,eAAErC,OAAA,CAACF,QAAQ;UAAAmB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAGvDpB,OAAA,CAACP,KAAK;QAAC2C,IAAI,EAAC,YAAY;QAACC,OAAO,eAAErC,OAAA,CAACC,cAAc;UAAAgB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACxDpB,OAAA,CAACP,KAAK;QAAC2C,IAAI,EAAC,kBAAkB;QAACC,OAAO,eAAErC,OAAA,CAACC,cAAc;UAAAgB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAG9DpB,OAAA,CAACP,KAAK;QAAC2C,IAAI,EAAC,GAAG;QAACC,OAAO,eAAErC,OAAA,CAACN,WAAW;UAAAuB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtC;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEb;AAACkB,GAAA,GAxBQH,GAAG;AA0BZ,eAAeA,GAAG;AAAC,IAAAd,EAAA,EAAAa,GAAA,EAAAI,GAAA;AAAAC,YAAA,CAAAlB,EAAA;AAAAkB,YAAA,CAAAL,GAAA;AAAAK,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}