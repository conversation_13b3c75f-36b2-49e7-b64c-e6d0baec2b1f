-- Setup database untuk React News Admin Dashboard
-- Jalankan script ini untuk membuat database dan tabel yang diperlukan

-- Buat database jika belum ada
CREATE DATABASE IF NOT EXISTS react_news CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE react_news;

-- Tabel admin (user login)
CREATE TABLE IF NOT EXISTS admin (
  id INT AUTO_INCREMENT PRIMARY KEY,
  username VARCHAR(50) NOT NULL UNIQUE,
  password VARCHAR(255) NOT NULL,
  email VARCHAR(100) NULL,
  full_name VARCHAR(100) NULL,
  role ENUM('admin', 'editor', 'author') DEFAULT 'admin',
  avatar VARCHAR(255) NULL,
  is_active BOOLEAN DEFAULT TRUE,
  last_login DATETIME NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Tabel categories (kategori berita)
CREATE TABLE IF NOT EXISTS categories (
  id INT AUTO_INCREMENT PRIMARY KEY,
  name VARCHAR(100) NOT NULL,
  slug VARCHAR(100) NOT NULL UNIQUE,
  description TEXT NULL,
  color VARCHAR(7) DEFAULT '#3B82F6',
  is_active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Tabel posts (berita) - dengan kolom lengkap
CREATE TABLE IF NOT EXISTS posts (
  id INT AUTO_INCREMENT PRIMARY KEY,
  title VARCHAR(255) NOT NULL,
  slug VARCHAR(255) NOT NULL UNIQUE,
  description TEXT NOT NULL,
  content LONGTEXT NOT NULL,
  excerpt TEXT NULL,
  image VARCHAR(255) NULL,
  image_alt VARCHAR(255) NULL,
  category_id INT NOT NULL DEFAULT 1,
  status ENUM('draft', 'published', 'archived') DEFAULT 'draft',
  featured BOOLEAN DEFAULT FALSE,
  meta_title VARCHAR(255) NULL,
  meta_description TEXT NULL,
  tags TEXT NULL,
  share INT DEFAULT 0,
  views INT DEFAULT 0,
  likes INT DEFAULT 0,
  comments_count INT DEFAULT 0,
  reading_time INT DEFAULT 0,
  published_at DATETIME NULL,
  date DATETIME DEFAULT CURRENT_TIMESTAMP,
  user_id INT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (user_id) REFERENCES admin(id) ON DELETE SET NULL,
  FOREIGN KEY (category_id) REFERENCES categories(id) ON DELETE RESTRICT,
  INDEX idx_status (status),
  INDEX idx_category (category_id),
  INDEX idx_published (published_at),
  INDEX idx_featured (featured),
  FULLTEXT KEY ft_search (title, description, content)
);

-- Tabel settings (pengaturan website)
CREATE TABLE IF NOT EXISTS settings (
  id INT AUTO_INCREMENT PRIMARY KEY,
  setting_key VARCHAR(100) NOT NULL UNIQUE,
  setting_value LONGTEXT NOT NULL,
  setting_type ENUM('text', 'number', 'boolean', 'json', 'file') DEFAULT 'text',
  category VARCHAR(50) DEFAULT 'general',
  description TEXT NULL,
  is_public BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_category (category),
  INDEX idx_public (is_public)
);

-- Tabel saved posts (berita yang disimpan/bookmark)
CREATE TABLE IF NOT EXISTS saved (
  id INT AUTO_INCREMENT PRIMARY KEY,
  post_id INT NOT NULL,
  user_id INT NULL,
  ip_address VARCHAR(45) NULL,
  saved_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (post_id) REFERENCES posts(id) ON DELETE CASCADE,
  FOREIGN KEY (user_id) REFERENCES admin(id) ON DELETE SET NULL,
  UNIQUE KEY unique_save (post_id, user_id, ip_address)
);

-- Tabel page_views (tracking views halaman)
CREATE TABLE IF NOT EXISTS page_views (
  id INT AUTO_INCREMENT PRIMARY KEY,
  post_id INT NULL,
  page_type ENUM('post', 'category', 'home', 'search', 'other') DEFAULT 'other',
  page_url VARCHAR(500) NOT NULL,
  ip_address VARCHAR(45) NULL,
  user_agent TEXT NULL,
  referer VARCHAR(500) NULL,
  country VARCHAR(2) NULL,
  city VARCHAR(100) NULL,
  device_type ENUM('desktop', 'mobile', 'tablet') NULL,
  browser VARCHAR(50) NULL,
  os VARCHAR(50) NULL,
  viewed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (post_id) REFERENCES posts(id) ON DELETE SET NULL,
  INDEX idx_post (post_id),
  INDEX idx_date (viewed_at),
  INDEX idx_page_type (page_type),
  INDEX idx_ip (ip_address)
);

-- Insert default admin (username: admin, password: admin123)
INSERT IGNORE INTO admin (username, password, email, full_name, role) VALUES 
('admin', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '<EMAIL>', 'Administrator', 'admin');

-- Insert default categories
INSERT IGNORE INTO categories (id, name, slug, description, color) VALUES 
(1, 'Umum', 'umum', 'Kategori untuk berita umum dan informasi sehari-hari', '#6B7280'),
(2, 'Teknologi', 'teknologi', 'Berita seputar teknologi, gadget, dan inovasi terbaru', '#3B82F6'),
(3, 'Bisnis', 'bisnis', 'Informasi bisnis, ekonomi, dan keuangan', '#10B981'),
(4, 'Olahraga', 'olahraga', 'Berita olahraga dan kompetisi', '#F59E0B'),
(5, 'Hiburan', 'hiburan', 'Berita hiburan, selebriti, dan lifestyle', '#EF4444'),
(6, 'Politik', 'politik', 'Berita politik dan pemerintahan', '#8B5CF6'),
(7, 'Kesehatan', 'kesehatan', 'Tips kesehatan dan informasi medis', '#06B6D4');

-- Insert default settings
INSERT IGNORE INTO settings (setting_key, setting_value, setting_type, category, description, is_public) VALUES 
('website_name', 'React News Portal', 'text', 'general', 'Nama website', TRUE),
('website_description', 'Portal berita terkini dan terpercaya', 'text', 'general', 'Deskripsi website', TRUE),
('website_logo', 'assets/logo.png', 'file', 'general', 'Logo website', TRUE),
('website_favicon', 'assets/favicon.ico', 'file', 'general', 'Favicon website', TRUE),
('primary_color', '#3B82F6', 'text', 'appearance', 'Warna primary theme', TRUE),
('secondary_color', '#1E40AF', 'text', 'appearance', 'Warna secondary theme', TRUE),
('accent_color', '#F59E0B', 'text', 'appearance', 'Warna accent theme', TRUE),
('posts_per_page', '10', 'number', 'content', 'Jumlah post per halaman', FALSE),
('allow_comments', 'true', 'boolean', 'content', 'Izinkan komentar', FALSE),
('contact_email', '<EMAIL>', 'text', 'contact', 'Email kontak', TRUE),
('contact_phone', '+62 123 456 789', 'text', 'contact', 'Nomor telepon', TRUE),
('contact_address', 'Jakarta, Indonesia', 'text', 'contact', 'Alamat', TRUE),
('timezone', 'Asia/Jakarta', 'text', 'general', 'Timezone website', FALSE);

-- Insert sample posts
INSERT IGNORE INTO posts (id, title, slug, description, content, excerpt, category_id, status, featured, tags, views, share, likes, reading_time, published_at, user_id) VALUES 
(1, 'Selamat Datang di React News Portal', 'selamat-datang-di-react-news-portal', 'Portal berita terbaru dengan teknologi React yang modern dan responsif', 
'Portal berita modern dengan teknologi React yang memberikan pengalaman membaca optimal. Dilengkapi dengan fitur-fitur canggih seperti desain responsif, loading cepat, sistem pencarian yang powerful, kategori berita yang lengkap, dan sistem komentar interaktif.', 
'Portal berita modern dengan teknologi React yang memberikan pengalaman membaca optimal', 1, 'published', TRUE, 'react,portal,berita,teknologi', 150, 25, 45, 3, NOW(), 1),

(2, 'Teknologi AI Terbaru Mengubah Dunia Digital', 'teknologi-ai-terbaru-mengubah-dunia-digital', 'Perkembangan kecerdasan buatan yang revolusioner dalam berbagai industri', 
'Kecerdasan buatan (AI) terus berkembang pesat dan mengubah cara kita berinteraksi dengan teknologi digital. Beberapa perkembangan terbaru meliputi ChatGPT dan model bahasa besar lainnya, AI dalam bidang kesehatan dan diagnosa medis, otomasi industri dengan machine learning, dan AI untuk kreativitas dan seni digital.', 
'Revolusi AI mengubah berbagai aspek kehidupan digital dan industri modern', 2, 'published', TRUE, 'ai,teknologi,digital,machine learning', 89, 12, 23, 4, NOW(), 1),

(3, 'Tips Investasi untuk Pemula di Era Digital', 'tips-investasi-untuk-pemula-di-era-digital', 'Panduan lengkap memulai investasi dengan platform digital yang aman', 
'Investasi di era digital menawarkan kemudahan dan aksesibilitas yang tidak pernah ada sebelumnya. Tips untuk pemula: mulai dengan jumlah kecil, diversifikasi portfolio, pelajari fundamental perusahaan, gunakan platform terpercaya, dan konsisten serta sabar.', 
'Panduan praktis memulai investasi digital untuk pemula dengan strategi yang tepat', 3, 'published', FALSE, 'investasi,digital,keuangan,tips', 67, 8, 15, 5, NOW(), 1);

-- Insert sample page views untuk statistik
INSERT IGNORE INTO page_views (post_id, page_type, page_url, ip_address, device_type, browser, viewed_at) VALUES 
(1, 'post', '/post/selamat-datang-di-react-news-portal', '***********', 'desktop', 'Chrome', DATE_SUB(NOW(), INTERVAL 1 DAY)),
(1, 'post', '/post/selamat-datang-di-react-news-portal', '***********', 'mobile', 'Safari', DATE_SUB(NOW(), INTERVAL 2 DAY)),
(2, 'post', '/post/teknologi-ai-terbaru-mengubah-dunia-digital', '***********', 'desktop', 'Firefox', DATE_SUB(NOW(), INTERVAL 1 DAY)),
(3, 'post', '/post/tips-investasi-untuk-pemula-di-era-digital', '***********', 'mobile', 'Chrome', NOW()),
(NULL, 'home', '/', '***********', 'desktop', 'Chrome', NOW()),
(NULL, 'category', '/category/teknologi', '***********', 'tablet', 'Safari', NOW());
