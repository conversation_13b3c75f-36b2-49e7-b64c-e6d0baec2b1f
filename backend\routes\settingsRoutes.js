const express = require('express');
const router = express.Router();

// Default website settings
let websiteSettings = {
  website_name: 'React News Portal',
  website_logo: '/logo192.png',
  website_description: 'Portal berita terkini dan terpercaya',
  primary_color: '#3B82F6',
  secondary_color: '#10B981',
  accent_color: '#F59E0B',
  footer_text: '© 2024 React News Portal. All rights reserved.',
  contact_email: '<EMAIL>',
  social_facebook: '',
  social_twitter: '',
  social_instagram: '',
  social_youtube: '',
  meta_keywords: 'berita, news, portal, react',
  meta_description: 'Portal berita terkini dengan teknologi React',
  updated_at: new Date().toISOString()
};

// GET /api/settings - Get website settings
router.get('/', (req, res) => {
  try {
    res.json({
      success: true,
      data: websiteSettings
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Error fetching settings',
      error: error.message
    });
  }
});

// POST /api/settings - Update website settings
router.post('/', (req, res) => {
  try {
    const {
      website_name,
      website_logo,
      website_description,
      primary_color,
      secondary_color,
      accent_color,
      footer_text,
      contact_email,
      social_facebook,
      social_twitter,
      social_instagram,
      social_youtube,
      meta_keywords,
      meta_description
    } = req.body;

    // Update settings
    if (website_name !== undefined) websiteSettings.website_name = website_name;
    if (website_logo !== undefined) websiteSettings.website_logo = website_logo;
    if (website_description !== undefined) websiteSettings.website_description = website_description;
    if (primary_color !== undefined) websiteSettings.primary_color = primary_color;
    if (secondary_color !== undefined) websiteSettings.secondary_color = secondary_color;
    if (accent_color !== undefined) websiteSettings.accent_color = accent_color;
    if (footer_text !== undefined) websiteSettings.footer_text = footer_text;
    if (contact_email !== undefined) websiteSettings.contact_email = contact_email;
    if (social_facebook !== undefined) websiteSettings.social_facebook = social_facebook;
    if (social_twitter !== undefined) websiteSettings.social_twitter = social_twitter;
    if (social_instagram !== undefined) websiteSettings.social_instagram = social_instagram;
    if (social_youtube !== undefined) websiteSettings.social_youtube = social_youtube;
    if (meta_keywords !== undefined) websiteSettings.meta_keywords = meta_keywords;
    if (meta_description !== undefined) websiteSettings.meta_description = meta_description;

    websiteSettings.updated_at = new Date().toISOString();

    res.json({
      success: true,
      message: 'Settings updated successfully',
      data: websiteSettings
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Error updating settings',
      error: error.message
    });
  }
});

// GET /api/settings/public - Get public settings (for frontend)
router.get('/public', (req, res) => {
  try {
    // Return only public settings (no sensitive data)
    const publicSettings = {
      website_name: websiteSettings.website_name,
      website_logo: websiteSettings.website_logo,
      website_description: websiteSettings.website_description,
      primary_color: websiteSettings.primary_color,
      secondary_color: websiteSettings.secondary_color,
      accent_color: websiteSettings.accent_color,
      footer_text: websiteSettings.footer_text,
      social_facebook: websiteSettings.social_facebook,
      social_twitter: websiteSettings.social_twitter,
      social_instagram: websiteSettings.social_instagram,
      social_youtube: websiteSettings.social_youtube,
      meta_keywords: websiteSettings.meta_keywords,
      meta_description: websiteSettings.meta_description
    };

    res.json(publicSettings);
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Error fetching public settings',
      error: error.message
    });
  }
});

// PUT /api/settings/logo - Update logo only
router.put('/logo', (req, res) => {
  try {
    const { logo_url } = req.body;
    
    if (!logo_url) {
      return res.status(400).json({
        success: false,
        message: 'Logo URL is required'
      });
    }

    websiteSettings.website_logo = logo_url;
    websiteSettings.updated_at = new Date().toISOString();

    res.json({
      success: true,
      message: 'Logo updated successfully',
      data: {
        website_logo: websiteSettings.website_logo
      }
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Error updating logo',
      error: error.message
    });
  }
});

// PUT /api/settings/name - Update website name only
router.put('/name', (req, res) => {
  try {
    const { website_name } = req.body;
    
    if (!website_name) {
      return res.status(400).json({
        success: false,
        message: 'Website name is required'
      });
    }

    websiteSettings.website_name = website_name;
    websiteSettings.updated_at = new Date().toISOString();

    res.json({
      success: true,
      message: 'Website name updated successfully',
      data: {
        website_name: websiteSettings.website_name
      }
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Error updating website name',
      error: error.message
    });
  }
});

module.exports = router;
