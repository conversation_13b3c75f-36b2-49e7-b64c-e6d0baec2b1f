<?php
// <PERSON>ript to migrate uploaded files from old location to new web-accessible location

$oldUploadDir = __DIR__ . '/../frontend/src/pages/admin/uploads/';
$newUploadDir = $_SERVER['DOCUMENT_ROOT'] . '/react-news/uploads/';

echo "Migrating uploaded files...\n";
echo "From: $oldUploadDir\n";
echo "To: $newUploadDir\n\n";

// Create new upload directory if it doesn't exist
if (!is_dir($newUploadDir)) {
    if (mkdir($newUploadDir, 0755, true)) {
        echo "✅ Created new upload directory: $newUploadDir\n";
    } else {
        echo "❌ Failed to create new upload directory\n";
        exit(1);
    }
} else {
    echo "✅ New upload directory already exists\n";
}

// Check if old directory exists
if (!is_dir($oldUploadDir)) {
    echo "ℹ️  Old upload directory doesn't exist: $oldUploadDir\n";
    echo "✅ Migration complete (no files to migrate)\n";
    exit(0);
}

// Get all files from old directory
$files = glob($oldUploadDir . '*');
$migratedCount = 0;
$errorCount = 0;

foreach ($files as $file) {
    if (is_file($file)) {
        $filename = basename($file);
        $newFile = $newUploadDir . $filename;
        
        // Copy file to new location
        if (copy($file, $newFile)) {
            echo "✅ Migrated: $filename\n";
            $migratedCount++;
            
            // Remove old file after successful copy
            if (unlink($file)) {
                echo "   🗑️  Removed old file\n";
            } else {
                echo "   ⚠️  Could not remove old file\n";
            }
        } else {
            echo "❌ Failed to migrate: $filename\n";
            $errorCount++;
        }
    }
}

echo "\n📊 Migration Summary:\n";
echo "   Migrated: $migratedCount files\n";
echo "   Errors: $errorCount files\n";

// Update database paths if needed
try {
    require_once __DIR__ . '/../frontend/src/pages/admin/config.php';
    
    $pdo = getConnection();
    
    // Update settings table
    $stmt = $pdo->prepare("UPDATE settings SET setting_value = REPLACE(setting_value, 'frontend/src/pages/admin/uploads/', '/react-news/uploads/') WHERE setting_key = 'website_logo'");
    $stmt->execute();
    
    echo "✅ Updated database paths\n";
    
    // Show current logo setting
    $stmt = $pdo->prepare("SELECT setting_value FROM settings WHERE setting_key = 'website_logo'");
    $stmt->execute();
    $result = $stmt->fetch();
    
    if ($result) {
        echo "📷 Current logo path: " . $result['setting_value'] . "\n";
    }
    
} catch (Exception $e) {
    echo "⚠️  Could not update database: " . $e->getMessage() . "\n";
}

echo "\n🎉 Migration completed!\n";
echo "📝 Next steps:\n";
echo "   1. Test logo display in admin dashboard\n";
echo "   2. Test logo display in frontend\n";
echo "   3. Upload a new logo to test new path\n";
echo "   4. Remove old upload directory if everything works\n";
?>
