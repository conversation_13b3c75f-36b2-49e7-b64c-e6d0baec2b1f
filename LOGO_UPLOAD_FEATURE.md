# 🖼️ Logo Upload Feature - File Upload ke Database

## ✅ Fitur yang Sudah Dibuat

### 1. **File Upload System**
- ✅ Choose file button untuk upload logo
- ✅ Support format: JPG, PNG, GIF, WebP
- ✅ Validasi ukuran file (maksimal 2MB)
- ✅ Preview logo sebelum upload
- ✅ Auto save ke database

### 2. **Database Integration**
- ✅ Logo disimpan ke folder `uploads/`
- ✅ Path logo disimpan ke database settings
- ✅ Auto update navbar di LandingPage.js
- ✅ Fallback ke default logo jika tidak ada

### 3. **Error Fixes**
- ✅ Fixed CSS 404 error (removed custom.css)
- ✅ Fixed logo 404 error (updated default path)
- ✅ Fixed JavaScript null errors (added null checks)
- ✅ Improved error handling

## 🚀 Cara Menggunakan

### **Step 1: Akses Admin Dashboard**
```
URL: http://localhost:3000/dashboard
Login: admin / admin123
```

### **Step 2: Buka Settings**
```
1. Klik menu "Settings" di sidebar
2. <PERSON><PERSON> ke bagian "Pengaturan Website"
3. Lihat form "Logo Website"
```

### **Step 3: Upload Logo**
```
1. Klik tombol "Pilih File Logo"
2. Pilih file gambar (JPG, PNG, GIF, WebP)
3. Maksimal 2MB, rekomendasi 200x200px
4. Preview akan muncul otomatis
5. Isi nama website dan deskripsi
6. Klik "Simpan Pengaturan"
```

### **Step 4: Lihat Hasil**
```
1. Refresh halaman utama: http://localhost:3000
2. Logo baru akan muncul di navbar
3. Nama website juga akan berubah
```

## 🔧 Technical Implementation

### **Frontend Form (DashboardTable.php)**
```html
<div>
    <label>Logo Website</label>
    <div class="space-y-3">
        <div class="flex items-center space-x-4">
            <div id="logo-preview" class="w-20 h-20 bg-gray-200 rounded-lg">
                <!-- Preview logo -->
            </div>
            <div class="flex-1">
                <input type="file" id="logo-file" accept="image/*" onchange="previewLogo(this)">
                <button onclick="document.getElementById('logo-file').click()">
                    Pilih File Logo
                </button>
            </div>
        </div>
    </div>
</div>
```

### **JavaScript Upload Handler**
```javascript
function saveWebsiteSettings() {
    const logoFile = document.getElementById('logo-file').files[0];
    const formData = new FormData();
    
    if (logoFile) {
        formData.append('logo_file', logoFile);
    }
    
    fetch('api.php', {
        method: 'POST',
        body: formData
    });
}

function previewLogo(input) {
    const file = input.files[0];
    // Validate file type and size
    // Show preview
}
```

### **Backend Upload Handler (api.php)**
```php
function handleLogoUpload($file) {
    // Create uploads directory
    $uploadDir = __DIR__ . '/uploads/';
    
    // Validate file type and size
    $allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
    
    // Generate unique filename
    $filename = 'logo_' . time() . '_' . uniqid() . '.' . $extension;
    
    // Move uploaded file
    move_uploaded_file($file['tmp_name'], $uploadDir . $filename);
    
    // Return path for database
    return 'frontend/src/pages/admin/uploads/' . $filename;
}
```

### **Database Storage**
```sql
-- Settings table structure
CREATE TABLE settings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    website_name VARCHAR(255) DEFAULT 'React News Portal',
    website_logo VARCHAR(500) DEFAULT '/logo192.png',
    website_description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Update logo path
UPDATE settings SET 
    website_logo = 'frontend/src/pages/admin/uploads/logo_123456.png',
    updated_at = NOW()
WHERE id = 1;
```

### **Frontend Integration (LandingPage.js)**
```javascript
// Load settings from database
fetch('http://localhost/react-news/frontend/src/pages/admin/api/settings.php?action=get')
  .then(res => res.json())
  .then(data => {
    setKostum({ 
      logo: data.website_logo || '/logo192.png', 
      title: data.website_name || 'React News Portal'
    });
  });

// Navbar usage
<Avatar src={kostum.logo} alt="Logo" />
<Typography>{kostum.title}</Typography>
```

## 📁 File Structure

### **Upload Directory**
```
frontend/src/pages/admin/
├── uploads/                    # ✅ Auto-created
│   ├── logo_1234567890_abc.png # Uploaded logos
│   ├── logo_1234567891_def.jpg
│   └── ...
├── api.php                     # ✅ Upload handler
├── DashboardTable.php          # ✅ Upload form
└── config.php                  # ✅ Database functions
```

### **Database Tables**
```
react_news/
├── settings                    # ✅ Website settings
│   ├── website_name           # Website title
│   ├── website_logo           # Logo file path
│   ├── website_description    # Description
│   └── ...
└── posts                      # News posts
```

## 🔍 Validation & Security

### **File Validation**
```javascript
// Frontend validation
const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
const maxSize = 2 * 1024 * 1024; // 2MB

if (!allowedTypes.includes(file.type)) {
    showNotification('Format file tidak didukung', 'error');
    return;
}

if (file.size > maxSize) {
    showNotification('Ukuran file terlalu besar', 'error');
    return;
}
```

### **Backend Security**
```php
// Server-side validation
$allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
if (!in_array($file['type'], $allowedTypes)) {
    return ['success' => false, 'message' => 'Invalid file type'];
}

// Unique filename to prevent conflicts
$filename = 'logo_' . time() . '_' . uniqid() . '.' . $extension;
```

## 🧪 Testing

### **Test 1: Upload Logo**
```
1. Login ke dashboard
2. Go to Settings
3. Upload logo (PNG, 200x200px)
4. Check preview appears
5. Save settings
6. Check success notification
```

### **Test 2: Validate File Types**
```
1. Try upload .txt file → Should show error
2. Try upload 5MB image → Should show error
3. Try upload valid PNG → Should work
```

### **Test 3: Frontend Integration**
```
1. Upload logo in admin
2. Refresh http://localhost:3000
3. Check navbar shows new logo
4. Check website name updated
```

## 🔧 Troubleshooting

### **Upload tidak berfungsi:**
```
1. Check folder permissions: uploads/ writable
2. Check PHP upload settings: upload_max_filesize
3. Check file path in database
4. Check console for JavaScript errors
```

### **Logo tidak muncul di frontend:**
```
1. Check database: SELECT website_logo FROM settings
2. Check file exists: frontend/src/pages/admin/uploads/
3. Check LandingPage.js loads settings API
4. Check browser cache (hard refresh)
```

### **JavaScript errors:**
```
1. Check element exists before addEventListener
2. Check null checks in code
3. Check browser console for specific errors
```

## ✅ Status: WORKING

### **Features Ready:**
- [x] File upload dengan choose file
- [x] Format validation (JPG, PNG, GIF, WebP)
- [x] Size validation (2MB max)
- [x] Preview functionality
- [x] Database storage
- [x] Frontend integration
- [x] Error handling
- [x] Null checks for JavaScript
- [x] Security validation

### **URLs:**
- **Admin**: http://localhost:3000/dashboard
- **Settings**: Dashboard → Settings menu
- **Frontend**: http://localhost:3000 (navbar logo)

## 🎉 Ready to Use!

Logo upload feature sudah siap digunakan:
1. **Choose file** untuk upload logo
2. **Auto preview** sebelum save
3. **Database storage** yang aman
4. **Frontend integration** otomatis
5. **Error handling** yang robust

Upload logo sekarang langsung tersimpan ke database dan muncul di navbar!
