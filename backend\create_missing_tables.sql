-- Create missing tables for React News Portal
-- Run this in your MySQL database

USE react_news;

-- Create comments table if not exists
CREATE TABLE IF NOT EXISTS `comments` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `post_id` int(11) NOT NULL,
  `author_name` varchar(100) NOT NULL,
  `author_email` varchar(100) NOT NULL,
  `content` text NOT NULL,
  `status` enum('pending','approved','rejected') DEFAULT 'pending',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `post_id` (`post_id`),
  KEY `status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Create categories table if not exists
CREATE TABLE IF NOT EXISTS `categories` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `slug` varchar(100) NOT NULL,
  `description` text,
  `color` varchar(7) DEFAULT '#6B7280',
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `slug` (`slug`),
  KEY `is_active` (`is_active`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Create page_views table if not exists
CREATE TABLE IF NOT EXISTS `page_views` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `post_id` int(11) DEFAULT NULL,
  `page_url` varchar(255) NOT NULL,
  `ip_address` varchar(45) NOT NULL,
  `user_agent` text,
  `viewed_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `post_id` (`post_id`),
  KEY `viewed_at` (`viewed_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Create saved table if not exists
CREATE TABLE IF NOT EXISTS `saved` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `post_id` int(11) NOT NULL,
  `ip_address` varchar(45) NOT NULL,
  `saved_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_save` (`post_id`, `ip_address`),
  KEY `post_id` (`post_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Insert default categories if table is empty
INSERT IGNORE INTO `categories` (`id`, `name`, `slug`, `description`, `color`, `is_active`) VALUES
(1, 'Umum', 'umum', 'Berita umum dan informasi', '#6B7280', 1),
(2, 'Teknologi', 'teknologi', 'Berita teknologi dan inovasi', '#3B82F6', 1),
(3, 'Bisnis', 'bisnis', 'Berita bisnis dan ekonomi', '#10B981', 1),
(4, 'Olahraga', 'olahraga', 'Berita olahraga dan kompetisi', '#F59E0B', 1),
(5, 'Hiburan', 'hiburan', 'Berita hiburan dan selebriti', '#EF4444', 1),
(6, 'Politik', 'politik', 'Berita politik dan pemerintahan', '#8B5CF6', 1),
(7, 'Kesehatan', 'kesehatan', 'Berita kesehatan dan medis', '#06B6D4', 1);

-- Insert some dummy comments for testing
INSERT IGNORE INTO `comments` (`id`, `post_id`, `author_name`, `author_email`, `content`, `status`) VALUES
(1, 1, 'John Doe', '<EMAIL>', 'Artikel yang sangat menarik!', 'approved'),
(2, 1, 'Jane Smith', '<EMAIL>', 'Terima kasih atas informasinya.', 'approved'),
(3, 2, 'Bob Wilson', '<EMAIL>', 'Saya setuju dengan pendapat ini.', 'approved');

-- Insert some dummy page views for testing
INSERT IGNORE INTO `page_views` (`id`, `post_id`, `page_url`, `ip_address`, `viewed_at`) VALUES
(1, 1, '/news/1', '***********', NOW() - INTERVAL 1 DAY),
(2, 1, '/news/1', '***********', NOW() - INTERVAL 2 HOUR),
(3, 2, '/news/2', '***********', NOW() - INTERVAL 3 HOUR),
(4, NULL, '/', '***********', NOW() - INTERVAL 1 HOUR);

-- Insert some dummy saved posts for testing
INSERT IGNORE INTO `saved` (`id`, `post_id`, `ip_address`) VALUES
(1, 1, '***********'),
(2, 2, '***********'),
(3, 1, '***********');

-- Update posts table to add category_id if not exists
ALTER TABLE `posts` 
ADD COLUMN IF NOT EXISTS `category_id` int(11) DEFAULT 1 AFTER `content`,
ADD COLUMN IF NOT EXISTS `views` int(11) DEFAULT 0 AFTER `category_id`,
ADD COLUMN IF NOT EXISTS `share` int(11) DEFAULT 0 AFTER `views`,
ADD COLUMN IF NOT EXISTS `likes` int(11) DEFAULT 0 AFTER `share`,
ADD COLUMN IF NOT EXISTS `status` enum('draft','published','archived') DEFAULT 'published' AFTER `likes`,
ADD COLUMN IF NOT EXISTS `featured` tinyint(1) DEFAULT 0 AFTER `status`,
ADD COLUMN IF NOT EXISTS `reading_time` int(11) DEFAULT 5 AFTER `featured`,
ADD COLUMN IF NOT EXISTS `published_at` timestamp NULL AFTER `reading_time`;

-- Add foreign key constraints if not exists
ALTER TABLE `posts` 
ADD CONSTRAINT `fk_posts_category` FOREIGN KEY (`category_id`) REFERENCES `categories` (`id`) ON DELETE SET NULL;

ALTER TABLE `comments` 
ADD CONSTRAINT `fk_comments_post` FOREIGN KEY (`post_id`) REFERENCES `posts` (`id`) ON DELETE CASCADE;

ALTER TABLE `page_views` 
ADD CONSTRAINT `fk_page_views_post` FOREIGN KEY (`post_id`) REFERENCES `posts` (`id`) ON DELETE CASCADE;

ALTER TABLE `saved` 
ADD CONSTRAINT `fk_saved_post` FOREIGN KEY (`post_id`) REFERENCES `posts` (`id`) ON DELETE CASCADE;

-- Update existing posts with random data for testing
UPDATE `posts` SET 
  `views` = FLOOR(RAND() * 1000) + 50,
  `share` = FLOOR(RAND() * 100) + 5,
  `likes` = FLOOR(RAND() * 200) + 10,
  `category_id` = FLOOR(RAND() * 7) + 1,
  `published_at` = `created_at`
WHERE `views` = 0 OR `views` IS NULL;

SELECT 'Missing tables created successfully!' as message;
