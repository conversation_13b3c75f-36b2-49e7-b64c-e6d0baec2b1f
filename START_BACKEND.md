# 🚀 Cara Menjalankan Backend

## 1. Install Dependencies

Buka terminal dan jalankan:

```bash
cd backend
npm install
```

## 2. Start Backend Server

```bash
npm start
```

Atau untuk development dengan auto-reload:

```bash
npm run dev
```

Backend akan ber<PERSON>lan di: **http://localhost:5000**

## 3. Test Backend

Buka browser dan test endpoint berikut:

- **Posts**: http://localhost:5000/api/posts
- **Categories**: http://localhost:5000/api/categories  
- **Saved Posts**: http://localhost:5000/api/saved

## 4. Jika Ada Error

### Error: Cannot find module 'axios'
```bash
cd backend
npm install axios
```

### Error: Cannot find module 'express'
```bash
cd backend
npm install
```

### Error: Port 5000 already in use
Ubah port di `backend/app.js`:
```javascript
const PORT = process.env.PORT || 5001;
```

## 5. Struktur API Endpoints

### Posts (Berita)
- `GET /api/posts` - Daftar semua berita
- `GET /api/posts/:id` - Detail berita
- `POST /api/posts` - Tambah berita baru
- `PUT /api/posts/:id` - Update berita
- `DELETE /api/posts/:id` - Hapus berita

### Categories (Kategori)
- `GET /api/categories` - Daftar kategori
- `GET /api/categories/:id` - Detail kategori

### Saved Posts (Bookmark)
- `GET /api/saved` - Daftar berita tersimpan
- `POST /api/posts/:id/save` - Simpan berita
- `DELETE /api/posts/:id/save` - Hapus dari simpan

### Tracking
- `POST /api/posts/:id/share` - Update share count
- `POST /api/posts/:id/view` - Update view count

### Website Settings
- `GET /api/kostum` - Pengaturan website

## 6. Data Format

### Post Object
```json
{
  "id": 1,
  "title": "Judul Berita",
  "description": "Konten berita",
  "category": "teknologi",
  "category_name": "Teknologi",
  "category_color": "#3B82F6",
  "views": 150,
  "share": 25,
  "likes": 45,
  "date": "2024-01-01T00:00:00.000Z",
  "image": "/uploads/image.jpg"
}
```

### Category Object
```json
{
  "id": 1,
  "name": "Teknologi",
  "slug": "teknologi", 
  "color": "#3B82F6",
  "post_count": 8
}
```

## 7. Troubleshooting

### Backend tidak bisa diakses dari React
1. Pastikan backend berjalan di port 5000
2. Check CORS settings di `app.js`
3. Pastikan tidak ada firewall yang memblokir

### Data tidak muncul di frontend
1. Buka Network tab di browser DevTools
2. Check apakah API calls berhasil (status 200)
3. Check console untuk error messages

### Module not found errors
```bash
cd backend
rm -rf node_modules
rm package-lock.json
npm install
```

## 8. Next Steps

Setelah backend berjalan:

1. **Start Frontend**: `cd frontend && npm start`
2. **Test Landing Page**: http://localhost:3000
3. **Test Saved Page**: http://localhost:3000/saved
4. **Test Admin**: http://localhost/react-news/frontend/src/pages/admin/DashboardTable.php

## 9. Development Tips

- Gunakan `npm run dev` untuk auto-reload saat development
- Check terminal untuk error messages
- Test API endpoints dengan Postman atau browser
- Monitor console logs untuk debugging

Backend sudah siap dengan:
- ✅ Dummy data untuk testing
- ✅ CORS enabled untuk React
- ✅ Error handling
- ✅ RESTful API structure
- ✅ Categories dengan warna custom
- ✅ Saved posts functionality
- ✅ Share/view tracking
