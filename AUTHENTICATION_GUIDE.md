# 🔐 Sistem Autentikasi JavaScript - React News Portal

## ✅ Yang Sudah Dibuat

### 1. **Login & Register menggunakan JavaScript**
- **Login.js** - Komponen React untuk login admin
- **Register.js** - Komponen React untuk registrasi admin
- **authService.js** - Service JavaScript untuk autentikasi
- **Tanpa PHP** - Semua autentikasi menggunakan localStorage

### 2. **Routing React**
```javascript
// App.js Routes
/admin/login    → Login.js (React Component)
/admin/register → Register.js (React Component)
/admin          → AdminRedirect → Check Auth → Dashboard
```

### 3. **AuthService JavaScript**
```javascript
// Fitur authService.js:
- login(username, password)
- register(username, password, email)
- isAuthenticated()
- getCurrentUser()
- logout()
- localStorage management
```

## 🚀 Cara Menggunakan

### 1. **Akses Login**
```
URL: http://localhost:3000/admin/login
```

### 2. **Login Default**
```
Username: admin
Password: admin123
```

### 3. **Flow Autentikasi**
```
1. User buka /admin/login
2. Input username & password
3. authService.login() → localStorage
4. Redirect ke DashboardTable.php
5. DashboardTable.php check localStorage
6. Jika tidak login → redirect ke /admin/login
```

## 📁 File Structure

```
frontend/src/pages/admin/auth/
├── Login.js              # React login component
├── Register.js           # React register component
├── authService.js        # JavaScript auth service
└── authMiddleware.js     # Auth middleware (optional)

frontend/src/App.js       # React routing
frontend/src/pages/admin/DashboardTable.php  # Admin dashboard
```

## 🔧 Cara Kerja Detail

### 1. **Login Process (Login.js)**
```javascript
// User submit form
handleSubmit() {
  authService.login(username, password)
    .then(result => {
      // Save to localStorage
      MySwal.fire('Login Berhasil!')
      // Redirect to PHP dashboard
      window.location.href = 'DashboardTable.php'
    })
}
```

### 2. **Authentication Check (DashboardTable.php)**
```javascript
// JavaScript di DashboardTable.php
document.addEventListener('DOMContentLoaded', function() {
  if (!authService.isAuthenticated()) {
    // Show access denied
    // Redirect to /admin/login
  }
  // Update user info in UI
});
```

### 3. **Data Storage (localStorage)**
```javascript
// Auth data structure
{
  "admin_auth": {
    "isAuthenticated": true,
    "user": {
      "id": 1,
      "username": "admin",
      "email": "<EMAIL>",
      "role": "admin"
    },
    "loginTime": "2024-01-01T00:00:00.000Z",
    "token": "base64encodedtoken"
  }
}
```

## 🎯 URL dan Navigation

### **User URLs (React)**
- `http://localhost:3000/` - Landing Page
- `http://localhost:3000/saved` - Saved Posts
- `http://localhost:3000/admin/login` - Login Form
- `http://localhost:3000/admin/register` - Register Form

### **Admin URLs (PHP)**
- `http://localhost/react-news/frontend/src/pages/admin/DashboardTable.php` - Admin Dashboard

## 🔄 Authentication Flow

```mermaid
graph TD
    A[User akses /admin] --> B{Check localStorage}
    B -->|Authenticated| C[Redirect to DashboardTable.php]
    B -->|Not Authenticated| D[Redirect to /admin/login]
    D --> E[Login.js Form]
    E --> F[authService.login()]
    F -->|Success| G[Save to localStorage]
    G --> C
    F -->|Failed| H[Show Error]
    H --> E
```

## 🛠️ Testing

### 1. **Test Login**
```bash
# 1. Buka browser
http://localhost:3000/admin/login

# 2. Input credentials
Username: admin
Password: admin123

# 3. Klik "Masuk ke Dashboard"
# 4. Harus redirect ke DashboardTable.php
```

### 2. **Test Register**
```bash
# 1. Buka browser
http://localhost:3000/admin/register

# 2. Input data baru
Username: newadmin
Email: <EMAIL>
Password: password123

# 3. Klik "Daftar Akun Admin"
# 4. Harus redirect ke login
```

### 3. **Test Protection**
```bash
# 1. Buka browser (private/incognito)
http://localhost/react-news/frontend/src/pages/admin/DashboardTable.php

# 2. Harus muncul "Akses Ditolak"
# 3. Auto redirect ke login
```

## 🔧 Troubleshooting

### **Error: Cannot access dashboard**
```javascript
// Check localStorage
console.log(localStorage.getItem('admin_auth'));

// Should return auth object, if null = not logged in
```

### **Error: Login not working**
```javascript
// Check authService
console.log(window.authService);

// Check users in localStorage
console.log(localStorage.getItem('admin_users'));
```

### **Error: Redirect loop**
```javascript
// Clear localStorage
localStorage.clear();

// Try login again
```

## 📋 Features

### ✅ **Completed**
- [x] Login form dengan React (Login.js)
- [x] Register form dengan React (Register.js)
- [x] JavaScript authentication service
- [x] localStorage session management
- [x] Dashboard protection dengan JavaScript
- [x] User info display di dashboard
- [x] Logout functionality
- [x] Default admin user (admin/admin123)
- [x] Routing integration dengan React Router

### 🎯 **Key Benefits**
- **No PHP Session** - Semua menggunakan JavaScript
- **React Components** - Login & Register full React
- **localStorage** - Session management di browser
- **Seamless Integration** - React frontend + PHP dashboard
- **User Friendly** - SweetAlert notifications
- **Secure** - Authentication check di setiap akses

## 🚀 Ready to Use!

Sistem autentikasi JavaScript sudah siap digunakan:
1. **Login**: http://localhost:3000/admin/login
2. **Register**: http://localhost:3000/admin/register  
3. **Dashboard**: Auto redirect setelah login
4. **Protection**: Dashboard terlindungi dengan JavaScript auth check
