-- Setup settings table for React News Portal
-- Run this in your MySQL database

USE react_news;

-- Create settings table if not exists (key-value structure)
CREATE TABLE IF NOT EXISTS settings (
  id INT AUTO_INCREMENT PRIMARY KEY,
  setting_key VARCHAR(100) NOT NULL UNIQUE,
  setting_value LONGTEXT NOT NULL,
  setting_type ENUM('text', 'number', 'boolean', 'json', 'file') DEFAULT 'text',
  category VARCHAR(50) DEFAULT 'general',
  description TEXT NULL,
  is_public BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_category (category),
  INDEX idx_public (is_public)
);

-- Insert default settings
INSERT IGNORE INTO settings (setting_key, setting_value, setting_type, category, description, is_public) VALUES
('website_name', 'React News Portal', 'text', 'general', 'Nama website yang ditampilkan di navbar', TRUE),
('website_logo', '/logo192.png', 'file', 'general', 'Logo website yang ditampilkan di navbar', TRUE),
('website_description', 'Portal berita terkini dan terpercaya', 'text', 'general', 'Deskripsi website', TRUE),
('primary_color', '#3B82F6', 'text', 'appearance', 'Warna utama website', TRUE),
('secondary_color', '#10B981', 'text', 'appearance', 'Warna sekunder website', TRUE),
('accent_color', '#F59E0B', 'text', 'appearance', 'Warna aksen website', TRUE),
('footer_text', '© 2024 React News Portal. All rights reserved.', 'text', 'general', 'Teks footer website', TRUE),
('contact_email', '<EMAIL>', 'text', 'contact', 'Email kontak website', TRUE),
('social_facebook', '', 'text', 'social', 'URL Facebook', TRUE),
('social_twitter', '', 'text', 'social', 'URL Twitter', TRUE),
('social_instagram', '', 'text', 'social', 'URL Instagram', TRUE),
('social_youtube', '', 'text', 'social', 'URL YouTube', TRUE),
('meta_keywords', 'berita, news, portal, react, teknologi', 'text', 'seo', 'Meta keywords untuk SEO', FALSE),
('meta_description', 'Portal berita terkini dengan teknologi React', 'text', 'seo', 'Meta description untuk SEO', FALSE);

-- Show current settings
SELECT 'Settings table setup completed!' as message;
SELECT setting_key, setting_value, category FROM settings ORDER BY category, setting_key;

-- Test update functionality
UPDATE settings SET setting_value = 'React News Portal - Updated' WHERE setting_key = 'website_name';
UPDATE settings SET setting_value = 'React News Portal' WHERE setting_key = 'website_name';

SELECT 'Update test completed!' as message;

-- Check table structure
DESCRIBE settings;
