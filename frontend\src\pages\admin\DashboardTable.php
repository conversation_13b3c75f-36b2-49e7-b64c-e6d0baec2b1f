<?php
require_once __DIR__ . '/config.php';

// Get current settings with safe fallbacks
$website_logo = getSetting('website_logo');
// Clean up any invalid logo paths
if (empty($website_logo) || strpos($website_logo, 'assets/logo.png') !== false || !file_exists($_SERVER['DOCUMENT_ROOT'] . $website_logo)) {
    $website_logo = 'http://localhost:3000/logo192.png';
}

$settings = [
    'website_name' => getSetting('website_name') ?: 'React News Portal',
    'website_logo' => $website_logo,
    'website_description' => getSetting('website_description') ?: 'Portal berita terkini dan terpercaya',
    'primary_color' => getSetting('primary_color') ?: '#3B82F6',
    'secondary_color' => getSetting('secondary_color') ?: '#10B981',
    'accent_color' => getSetting('accent_color') ?: '#F59E0B'
];

// Get statistics
$stats = getStats();
?>
<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo htmlspecialchars($settings['website_name']); ?> - Admin Dashboard</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '<?php echo $settings['primary_color']; ?>',
                        secondary: '<?php echo $settings['secondary_color']; ?>',
                        accent: '<?php echo $settings['accent_color']; ?>',
                        dark: '#1F2937',
                        light: '#F9FAFB'
                    }
                }
            }
        }
    </script>

    <!-- Authentication Service -->
    <script type="module">
        // Authentication Service using localStorage
        class AuthService {
            constructor() {
                this.storageKey = 'admin_auth';
                this.usersKey = 'admin_users';
                this.initializeDefaultUser();
            }

            initializeDefaultUser() {
                const users = this.getUsers();
                if (users.length === 0) {
                    const defaultUser = {
                        id: 1,
                        username: 'admin',
                        password: 'admin123',
                        email: '<EMAIL>',
                        role: 'admin',
                        createdAt: new Date().toISOString()
                    };
                    this.saveUser(defaultUser);
                }
            }

            getUsers() {
                const users = localStorage.getItem(this.usersKey);
                return users ? JSON.parse(users) : [];
            }

            saveUser(user) {
                const users = this.getUsers();
                const existingIndex = users.findIndex(u => u.username === user.username);

                if (existingIndex >= 0) {
                    users[existingIndex] = user;
                } else {
                    user.id = users.length + 1;
                    users.push(user);
                }

                localStorage.setItem(this.usersKey, JSON.stringify(users));
                return user;
            }

            isAuthenticated() {
                const authData = localStorage.getItem(this.storageKey);
                if (!authData) return false;

                try {
                    const parsed = JSON.parse(authData);
                    return parsed.isAuthenticated === true;
                } catch {
                    return false;
                }
            }

            getCurrentUser() {
                const authData = localStorage.getItem(this.storageKey);
                if (!authData) return null;

                try {
                    const parsed = JSON.parse(authData);
                    return parsed.user || null;
                } catch {
                    return null;
                }
            }

            logout() {
                localStorage.removeItem(this.storageKey);
                return { success: true, message: 'Logout berhasil!' };
            }
        }

        // Initialize auth service
        window.authService = new AuthService();

        // Simple user info update on page load
        document.addEventListener('DOMContentLoaded', function() {
            // Try to get user info from localStorage
            const authData = localStorage.getItem('admin_auth');
            let username = 'Admin';

            if (authData) {
                try {
                    const parsed = JSON.parse(authData);
                    if (parsed.user && parsed.user.username) {
                        username = parsed.user.username;
                    }
                } catch (e) {
                    console.log('Could not parse auth data');
                }
            }

            // Update user info in UI
            const userInfoElements = document.querySelectorAll('[data-user-info]');
            userInfoElements.forEach(element => {
                element.textContent = username;
            });
        });

        // Logout function
        window.adminLogout = function() {
            if (confirm('Apakah Anda yakin ingin logout?')) {
                window.authService.logout();
                window.location.href = 'http://localhost:3000/admin/login';
            }
        };
    </script>
    <style>
        :root {
            --primary-color: <?php echo $settings['primary_color']; ?>;
            --secondary-color: <?php echo $settings['secondary_color']; ?>;
            --accent-color: <?php echo $settings['accent_color']; ?>;
        }
        .bg-primary { background-color: var(--primary-color) !important; }
        .text-primary { color: var(--primary-color) !important; }
        .border-primary { border-color: var(--primary-color) !important; }
        .hover\:bg-primary:hover { background-color: var(--primary-color) !important; }
        .hover\:text-primary:hover { color: var(--primary-color) !important; }
        .focus\:ring-primary:focus { --tw-ring-color: var(--primary-color) !important; }

        /* Additional responsive and UI improvements */
        .line-clamp-2 {
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }

        /* Form styling improvements */
        .form-section {
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
        }

        .form-section h4 {
            color: #374151;
            font-weight: 600;
            margin-bottom: 1rem;
            font-size: 1.1rem;
        }

        /* Enhanced textarea styling */
        textarea:focus {
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
            border-color: var(--primary-color);
        }

        /* Status select styling */
        #edit-news-status {
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
            background-position: right 0.5rem center;
            background-repeat: no-repeat;
            background-size: 1.5em 1.5em;
            padding-right: 2.5rem;
        }

        /* Status option styling */
        #edit-news-status option[value="draft"] {
            color: #f59e0b;
        }

        #edit-news-status option[value="published"] {
            color: #10b981;
        }

        /* Better spacing for form elements */
        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-group:last-child {
            margin-bottom: 0;
        }

        /* Table row consistency */
        #news-table-body tr {
            min-height: 80px;
        }

        #news-table-body td {
            vertical-align: top;
            height: auto;
        }

        /* Action column styling */
        .action-column {
            width: 120px;
            min-width: 120px;
        }

        /* Full width table styling */
        .table-container {
            width: 100%;
            max-width: 100%;
        }

        .table-container table {
            table-layout: fixed;
            width: 100%;
        }

        /* Desktop table full width */
        @media (min-width: 1024px) {
            .table-container table {
                min-width: 100%;
                width: 100%;
                table-layout: fixed;
            }

            /* Column width distribution */
            .table-container table th:nth-child(1), /* Checkbox */
            .table-container table td:nth-child(1) {
                width: 60px;
            }

            .table-container table th:nth-child(2), /* No */
            .table-container table td:nth-child(2) {
                width: 60px;
            }

            .table-container table th:nth-child(3), /* Gambar */
            .table-container table td:nth-child(3) {
                width: 80px;
            }

            .table-container table th:nth-child(4), /* Judul & Deskripsi */
            .table-container table td:nth-child(4) {
                width: auto; /* Takes remaining space */
                min-width: 300px;
            }

            .table-container table th:nth-child(5), /* Tgl & Waktu */
            .table-container table td:nth-child(5) {
                width: 140px;
            }

            .table-container table th:nth-child(6), /* Status */
            .table-container table td:nth-child(6) {
                width: 100px;
            }

            .table-container table th:nth-child(7), /* Aksi */
            .table-container table td:nth-child(7) {
                width: 120px;
            }
        }

        /* Smooth transitions */
        * {
            transition: all 0.2s ease-in-out;
        }

        /* Card hover effects */
        .card-hover:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        }

        /* Better mobile sidebar */
        .w-70 {
            width: 17.5rem; /* 280px */
        }

        @media (max-width: 1023px) {
            #sidebar {
                backdrop-filter: blur(10px);
            }

            /* Larger mobile UI elements */
            .mobile-larger {
                font-size: 1.1rem;
                padding: 0.75rem 1rem;
            }

            .mobile-icon-larger {
                font-size: 1.25rem;
                min-width: 1.5rem;
                text-align: center;
                display: inline-block;
                flex-shrink: 0;
            }

            /* Sidebar navigation improvements */
            .nav-link {
                display: flex;
                align-items: center;
                gap: 0.75rem;
            }

            .nav-link i {
                flex-shrink: 0;
                width: 1.5rem;
                text-align: center;
            }

            .mobile-button-larger {
                padding: 0.75rem 1.5rem;
                font-size: 1rem;
            }

            /* Larger mobile buttons and inputs */
            .mobile-input-larger {
                padding: 0.875rem 1rem;
                font-size: 1rem;
            }

            /* Larger mobile table elements */
            .mobile-table-larger td {
                padding: 1rem 0.75rem;
            }
        }

        /* Table row hover */
        tbody tr:hover {
            background-color: rgba(59, 130, 246, 0.05);
        }

        /* Scrollable table with shadow indicators */
        .table-container {
            position: relative;
        }

        .table-container::before,
        .table-container::after {
            content: '';
            position: absolute;
            top: 0;
            bottom: 0;
            width: 20px;
            pointer-events: none;
            z-index: 10;
            transition: opacity 0.3s ease;
        }

        .table-container::before {
            left: 0;
            background: linear-gradient(to right, rgba(255,255,255,1), rgba(255,255,255,0));
            opacity: 0;
        }

        .table-container::after {
            right: 0;
            background: linear-gradient(to left, rgba(255,255,255,1), rgba(255,255,255,0));
            opacity: 1;
        }

        .table-container.scrolled-left::before {
            opacity: 1;
        }

        .table-container.scrolled-right::after {
            opacity: 0;
        }

        /* Sticky column shadow */
        .sticky-shadow {
            box-shadow: 2px 0 5px rgba(0, 0, 0, 0.1);
        }

        /* Mobile table improvements */
        @media (max-width: 768px) {
            .table-container {
                border-radius: 0.5rem;
                box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            }

            .mobile-scroll-hint {
                display: block;
                text-align: center;
                padding: 0.5rem;
                background: #f3f4f6;
                color: #6b7280;
                font-size: 0.75rem;
                border-radius: 0.5rem 0.5rem 0 0;
            }

            /* Larger touch targets on mobile */
            .mobile-touch-target {
                min-height: 44px;
                min-width: 44px;
            }

            /* Smaller checkbox on mobile */
            .mobile-checkbox-small {
                width: 16px !important;
                height: 16px !important;
                min-width: 16px !important;
                min-height: 16px !important;
            }

            /* Ensure checkbox container has proper padding */
            .checkbox-container-mobile {
                padding: 0.5rem;
                display: flex;
                align-items: center;
                justify-content: center;
            }
        }

        @media (min-width: 769px) {
            .mobile-scroll-hint {
                display: none;
            }

            /* Larger checkboxes on desktop */
            .mobile-checkbox-small {
                width: 20px !important;
                height: 20px !important;
                min-width: 20px !important;
                min-height: 20px !important;
            }
        }

        /* Extra small mobile devices */
        @media (max-width: 480px) {
            .mobile-checkbox-small {
                width: 14px !important;
                height: 14px !important;
                min-width: 14px !important;
                min-height: 14px !important;
            }

            .checkbox-container-mobile {
                padding: 0.25rem;
            }
        }

        /* Sidebar Enhancements */
        #sidebar .nav-link.active {
            background: var(--primary-color);
            color: white;
            box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
        }

        #sidebar .nav-link.active i {
            color: white !important;
        }

        #sidebar .nav-link:hover {
            transform: translateX(4px);
        }

        /* Logo styling */
        #sidebar-logo {
            transition: transform 0.3s ease;
        }

        #sidebar-logo:hover {
            transform: scale(1.1);
        }

        /* Solid background for header */
        .bg-primary-solid {
            background: var(--primary-color);
        }
    </style>
</head>
<body class="bg-gray-100 font-sans">
    <div class="flex h-screen bg-gray-100">
        <!-- Sidebar -->
        <div id="sidebar" class="fixed inset-y-0 left-0 z-50 w-70 lg:w-64 bg-white shadow-lg transform -translate-x-full transition-transform duration-300 ease-in-out lg:translate-x-0 lg:relative lg:flex lg:flex-col">
            <!-- Sidebar Header -->
            <div class="flex items-center justify-center h-20 bg-primary-solid text-white border-b border-gray-200">
                <div class="flex items-center space-x-3">
                    <!-- Logo -->
                    <div class="w-10 h-10 rounded-lg bg-white bg-opacity-25 flex items-center justify-center overflow-hidden">
                        <img id="sidebar-logo"
                             src="<?php
                                $logoPath = $settings['website_logo'] ?? '';
                                if (empty($logoPath)) {
                                    echo 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzIiIGhlaWdodD0iMzIiIHZpZXdCb3g9IjAgMCAzMiAzMiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjMyIiBoZWlnaHQ9IjMyIiByeD0iNCIgZmlsbD0iIzM5OEVGNyIvPgo8cGF0aCBkPSJNOCAxMEgxNlY2SDhWMTBaTTggMTZIMjRWMTJIOFYxNlpNOCAyMkgyNFYxOEg4VjIyWiIgZmlsbD0id2hpdGUiLz4KPC9zdmc+';
                                } elseif (strpos($logoPath, 'http') === 0) {
                                    echo htmlspecialchars($logoPath);
                                } elseif (strpos($logoPath, '/react-news/uploads/') === 0) {
                                    echo 'http://localhost' . htmlspecialchars($logoPath);
                                } else {
                                    echo 'http://localhost/react-news/' . htmlspecialchars(ltrim($logoPath, '/'));
                                }
                             ?>"
                             alt="Logo"
                             class="w-8 h-8 object-cover rounded"
                             onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
                        <i class="fas fa-newspaper text-white text-lg" style="display: none;"></i>
                    </div>
                    <!-- Website Name -->
                    <div>
                        <h1 class="text-lg font-bold leading-tight">
                            <?php echo htmlspecialchars($settings['website_name']); ?>
                        </h1>
                        <p class="text-xs text-white/80">Admin Panel</p>
                    </div>
                </div>
            </div>

            <!-- Navigation -->
            <nav class="flex-1 px-4 py-6">
                <!-- Main Menu -->
                <div class="space-y-1 mb-8">
                    <div class="px-3 py-2 text-xs font-semibold text-gray-500 uppercase tracking-wider">
                        Menu Utama
                    </div>
                    <a href="#dashboard" onclick="showSection('dashboard')" class="nav-link flex items-center px-4 py-3 text-gray-700 rounded-lg hover:bg-primary hover:text-white transition-all duration-200 group">
                        <i class="fas fa-chart-pie w-5 h-5 mr-3 text-gray-400 group-hover:text-white"></i>
                        <span class="font-medium">Dashboard</span>
                    </a>
                    <a href="#berita" onclick="showSection('berita')" class="nav-link flex items-center px-4 py-3 text-gray-700 rounded-lg hover:bg-primary hover:text-white transition-all duration-200 group">
                        <i class="fas fa-newspaper w-5 h-5 mr-3 text-gray-400 group-hover:text-white"></i>
                        <span class="font-medium">Kelola Berita</span>
                    </a>
                </div>

                <!-- Settings Menu -->
                <div class="space-y-1">
                    <div class="px-3 py-2 text-xs font-semibold text-gray-500 uppercase tracking-wider">
                        Pengaturan
                    </div>
                    <a href="#pengaturan" onclick="showSection('pengaturan')" class="nav-link flex items-center px-4 py-3 text-gray-700 rounded-lg hover:bg-primary hover:text-white transition-all duration-200 group">
                        <i class="fas fa-cog w-5 h-5 mr-3 text-gray-400 group-hover:text-white"></i>
                        <span class="font-medium">Website Settings</span>
                    </a>
                </div>

                <!-- Footer Menu -->
                <div class="absolute bottom-6 left-4 right-4">
                    <div class="border-t border-gray-200 pt-4">
                        <a href="/" target="_blank" class="flex items-center px-4 py-3 text-gray-600 hover:text-primary transition-colors duration-200 group">
                            <i class="fas fa-external-link-alt w-4 h-4 mr-3 text-gray-400 group-hover:text-primary"></i>
                            <span class="text-sm">Lihat Website</span>
                        </a>
                        <button onclick="adminLogout()" class="w-full flex items-center px-4 py-3 text-gray-600 hover:text-red-600 hover:bg-red-50 rounded-lg transition-all duration-200 group">
                            <i class="fas fa-sign-out-alt w-4 h-4 mr-3 text-gray-400 group-hover:text-red-600"></i>
                            <span class="text-sm">Logout</span>
                        </button>
                    </div>
                </div>
            </nav>
        </div>

        <!-- Mobile menu button -->
        <div class="lg:hidden">
            <button id="mobile-menu-button" class="fixed top-4 left-4 z-50 p-2 rounded-md bg-primary text-white shadow-lg">
                <i class="fas fa-bars"></i>
            </button>
        </div>

        <!-- Main Content -->
        <div class="flex-1 flex flex-col overflow-hidden lg:ml-0">
            <!-- Header -->
            <header class="bg-white shadow-sm border-b border-gray-200 flex-shrink-0">
                <div class="flex items-center justify-between px-6 py-4">
                    <div class="flex items-center">
                        <button id="mobile-menu-button-header" class="lg:hidden mr-4 p-2 rounded-md text-gray-600 hover:text-primary hover:bg-gray-100">
                            <i class="fas fa-bars"></i>
                        </button>
                        <h2 id="page-title" class="text-2xl font-semibold text-gray-800">Dashboard</h2>
                    </div>
                    <div class="flex items-center space-x-4">
                        <!-- User Info -->
                        <div class="flex items-center text-gray-700">
                            <i class="fas fa-user-circle text-2xl mr-2 text-primary"></i>
                            <div class="hidden md:block">
                                <div class="text-sm font-medium" data-user-info>Admin</div>
                                <div class="text-xs text-gray-500">Administrator</div>
                            </div>
                        </div>

                        <!-- Logout Button -->
                        <button
                            onclick="adminLogout()"
                            class="flex items-center px-3 py-2 text-sm text-gray-600 hover:text-red-600 hover:bg-red-50 rounded-lg transition-colors duration-200"
                            title="Logout"
                        >
                            <i class="fas fa-sign-out-alt mr-2"></i>
                            <span class="hidden md:block">Logout</span>
                        </button>
                    </div>
                </div>
            </header>

            <!-- Main Content Area -->
            <main class="flex-1 overflow-x-hidden overflow-y-auto bg-gray-100">
                <!-- Dashboard Section -->
                <div id="dashboard-section" class="section-content p-6">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                <!-- Stats Cards -->
                <div class="bg-white rounded-lg shadow-md p-6 card-hover border-l-4 border-blue-500">
                    <div class="flex items-center">
                        <div class="p-3 rounded-full bg-blue-100 text-blue-600">
                            <i class="fas fa-users text-xl"></i>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">Total Users</p>
                            <p class="text-2xl font-semibold text-gray-900" data-stat="total-users"><?php echo number_format($stats['total_users']); ?></p>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-lg shadow-md p-6 card-hover border-l-4 border-green-500">
                    <div class="flex items-center">
                        <div class="p-3 rounded-full bg-green-100 text-green-600">
                            <i class="fas fa-newspaper text-xl"></i>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">Total Berita</p>
                            <p class="text-2xl font-semibold text-gray-900" data-stat="total-news"><?php echo number_format($stats['total_news']); ?></p>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-lg shadow-md p-6 card-hover border-l-4 border-yellow-500">
                    <div class="flex items-center">
                        <div class="p-3 rounded-full bg-yellow-100 text-yellow-600">
                            <i class="fas fa-eye text-xl"></i>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">Page Views</p>
                            <p class="text-2xl font-semibold text-gray-900" data-stat="page-views"><?php echo number_format($stats['page_views']); ?></p>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-lg shadow-md p-6 card-hover border-l-4 border-red-500">
                    <div class="flex items-center">
                        <div class="p-3 rounded-full bg-red-100 text-red-600">
                            <i class="fas fa-share text-xl"></i>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">Total Shares</p>
                            <p class="text-2xl font-semibold text-gray-900" data-stat="total-shares"><?php echo number_format($stats['total_shares'] ?? 0); ?></p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Additional Stats Row -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                <div class="bg-white rounded-lg shadow-md p-6 card-hover border-l-4 border-emerald-500">
                    <div class="flex items-center">
                        <div class="p-3 rounded-full bg-emerald-100 text-emerald-600">
                            <i class="fas fa-check-circle text-xl"></i>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">Published</p>
                            <p class="text-2xl font-semibold text-gray-900" data-stat="published-news"><?php echo number_format($stats['published_news'] ?? 0); ?></p>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-lg shadow-md p-6 card-hover border-l-4 border-orange-500">
                    <div class="flex items-center">
                        <div class="p-3 rounded-full bg-orange-100 text-orange-600">
                            <i class="fas fa-edit text-xl"></i>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">Draft</p>
                            <p class="text-2xl font-semibold text-gray-900" data-stat="draft-news"><?php echo number_format($stats['draft_news'] ?? 0); ?></p>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-lg shadow-md p-6 card-hover border-l-4 border-pink-500">
                    <div class="flex items-center">
                        <div class="p-3 rounded-full bg-pink-100 text-pink-600">
                            <i class="fas fa-heart text-xl"></i>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">Total Likes</p>
                            <p class="text-2xl font-semibold text-gray-900" data-stat="total-likes"><?php echo number_format($stats['total_likes'] ?? 0); ?></p>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-lg shadow-md p-6 card-hover border-l-4 border-purple-500">
                    <div class="flex items-center">
                        <div class="p-3 rounded-full bg-purple-100 text-purple-600">
                            <i class="fas fa-bookmark text-xl"></i>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">Saved Posts</p>
                            <p class="text-2xl font-semibold text-gray-900" data-stat="saved-posts"><?php echo number_format($stats['saved_posts'] ?? 0); ?></p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Charts Section -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <div class="bg-white rounded-lg shadow-md p-6">
                    <h3 class="text-lg font-semibold text-gray-800 mb-4">
                        <i class="fas fa-chart-bar mr-2"></i>
                        Statistik Bulanan
                    </h3>
                    <div class="h-64 flex items-center justify-center bg-gray-50 rounded">
                        <p class="text-gray-500">Chart akan ditampilkan di sini</p>
                    </div>
                </div>

                <div class="bg-white rounded-lg shadow-md p-6">
                    <h3 class="text-lg font-semibold text-gray-800 mb-4">
                        <i class="fas fa-chart-pie mr-2"></i>
                        Distribusi Konten
                    </h3>
                    <div class="h-64 flex items-center justify-center bg-gray-50 rounded">
                        <p class="text-gray-500">Pie chart akan ditampilkan di sini</p>
                    </div>
                </div>
            </div>
        </div>

                <!-- Berita Section -->
                <div id="berita-section" class="section-content hidden">
            <div class="bg-white rounded-lg shadow-md mx-6 mb-6">
                <div class="px-6 py-4 border-b border-gray-200">
                    <div class="flex flex-col lg:flex-row lg:items-center justify-between gap-4">
                        <h3 class="text-lg font-semibold text-gray-800">
                            <i class="fas fa-newspaper mr-2"></i>
                            Manajemen Berita
                        </h3>
                        <div class="flex flex-row gap-2 justify-end">
                            <button id="delete-selected-btn" onclick="deleteSelectedNews()" class="bg-red-500 text-white px-4 py-2 rounded-lg hover:bg-red-600 transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed mobile-button-larger text-sm lg:text-base" disabled>
                                <i class="fas fa-trash mr-1 lg:mr-2"></i>
                                <span class="hidden sm:inline">Hapus Terpilih</span>
                                <span class="sm:hidden">Hapus</span>
                                (<span id="selected-count">0</span>)
                            </button>
                            <button onclick="showAddBeritaModal()" class="bg-primary text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors duration-200 mobile-button-larger text-sm lg:text-base">
                                <i class="fas fa-plus mr-1 lg:mr-2"></i>
                                <span class="hidden sm:inline">Tambah Berita</span>
                                <span class="sm:hidden">Tambah</span>
                            </button>
                        </div>
                    </div>
                </div>
                
                <div class="pb-6">
                    <div class="bg-white rounded-lg shadow overflow-hidden w-full">
                        <div class="mobile-scroll-hint">
                            <i class="fas fa-arrows-alt-h mr-1"></i>
                            Geser tabel ke kiri/kanan untuk melihat semua kolom
                        </div>
                        <div class="table-container overflow-x-auto w-full" id="table-container">
                            <table class="w-full divide-y divide-gray-200" style="min-width: 800px; width: 100%;">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th class="px-2 py-3 text-center w-12 sticky left-0 bg-gray-50 z-10">
                                            <div class="checkbox-container-mobile">
                                                <input type="checkbox" id="select-all" name="select-all" class="rounded border-gray-300 text-primary focus:ring-primary mobile-checkbox-small" onchange="toggleSelectAll()">
                                            </div>
                                        </th>
                                        <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider" style="width: 60px;">No</th>
                                        <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider" style="width: 80px;">Gambar</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider" style="width: auto; min-width: 300px;">Judul & Deskripsi</th>
                                        <th class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider" style="width: 140px;">Tgl & Waktu</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider" style="width: 100px;">Status</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider" style="width: 120px;">Aksi</th>
                                    </tr>
                                </thead>
                                <tbody id="news-table-body" class="bg-white divide-y divide-gray-200">
                                    <!-- News will be loaded here via JavaScript -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

                <!-- News Detail View Section -->
                <div id="news-detail-section" class="section-content hidden">
                    <div class="bg-white rounded-lg shadow-sm">
                        <!-- Breadcrumb -->
                        <div class="px-6 py-4 border-b border-gray-200">
                            <nav class="flex items-center space-x-2 text-sm text-gray-600">
                                <a href="#" onclick="showSection('berita')" class="hover:text-primary transition-colors">
                                    <i class="fas fa-newspaper mr-1"></i>
                                    Kelola Berita
                                </a>
                                <i class="fas fa-chevron-right text-gray-400"></i>
                                <span class="text-gray-800 font-medium" id="news-detail-breadcrumb">Detail Berita</span>
                            </nav>
                        </div>

                        <!-- Content -->
                        <div class="p-6">
                            <div id="news-detail-content">
                                <!-- Content will be loaded here -->
                            </div>

                            <!-- Action Buttons -->
                            <div class="flex items-center justify-between mt-8 pt-6 border-t border-gray-200">
                                <button onclick="showSection('berita')" class="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors">
                                    <i class="fas fa-arrow-left mr-2"></i>
                                    Kembali ke Daftar Berita
                                </button>
                                <div class="flex space-x-3">
                                    <button onclick="editNews(currentNewsId)" class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                                        <i class="fas fa-edit mr-2"></i>
                                        Edit Berita
                                    </button>
                                    <button onclick="deleteNews(currentNewsId)" class="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors">
                                        <i class="fas fa-trash mr-2"></i>
                                        Hapus Berita
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- News Edit Section -->
                <div id="news-edit-section" class="section-content hidden">
                    <div class="bg-white rounded-lg shadow-sm">
                        <!-- Breadcrumb -->
                        <div class="px-6 py-4 border-b border-gray-200">
                            <nav class="flex items-center space-x-2 text-sm text-gray-600">
                                <a href="#" onclick="showSection('berita')" class="hover:text-primary transition-colors">
                                    <i class="fas fa-newspaper mr-1"></i>
                                    Kelola Berita
                                </a>
                                <i class="fas fa-chevron-right text-gray-400"></i>
                                <span class="text-gray-800 font-medium" id="news-edit-breadcrumb">Edit Berita</span>
                            </nav>
                        </div>

                        <!-- Edit Form -->
                        <div class="p-6">
                            <form id="edit-news-form" enctype="multipart/form-data">
                                <input type="hidden" id="edit-news-id" name="id">

                                <!-- Basic Information Section -->
                                <div class="form-section">
                                    <h4><i class="fas fa-info-circle mr-2 text-blue-600"></i>Informasi Dasar</h4>
                                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                                        <div class="form-group">
                                            <label for="edit-news-title" class="block text-sm font-medium text-gray-700 mb-2">Judul Berita</label>
                                            <input type="text" id="edit-news-title" name="title" required
                                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                                                   placeholder="Masukkan judul berita yang menarik...">
                                        </div>

                                        <div class="form-group">
                                            <label for="edit-news-category" class="block text-sm font-medium text-gray-700 mb-2">Kategori</label>
                                            <select id="edit-news-category" name="category_id" required
                                                    class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent">
                                                <option value="">Pilih kategori...</option>
                                                <!-- Options will be loaded dynamically -->
                                            </select>
                                        </div>
                                    </div>
                                </div>

                                <!-- Image Upload Section -->
                                <div class="form-section">
                                    <h4><i class="fas fa-image mr-2 text-green-600"></i>Gambar Berita</h4>
                                    <div class="form-group">
                                        <label for="edit-news-image" class="block text-sm font-medium text-gray-700 mb-2">Upload Gambar Baru</label>
                                        <input type="file" id="edit-news-image" name="image" accept="image/*"
                                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent">
                                        <p class="text-sm text-gray-500 mt-1">
                                            <i class="fas fa-info-circle mr-1"></i>
                                            Kosongkan jika tidak ingin mengubah gambar. Format yang didukung: JPG, PNG, GIF (Max: 5MB)
                                        </p>

                                        <!-- Current Image Preview -->
                                        <div id="edit-current-image" class="mt-4">
                                            <!-- Current image will be shown here -->
                                        </div>
                                    </div>
                                </div>

                                <!-- Content Section -->
                                <div class="form-section">
                                    <h4><i class="fas fa-edit mr-2 text-purple-600"></i>Konten Berita</h4>
                                    <div class="form-group">
                                        <label for="edit-news-content" class="block text-sm font-medium text-gray-700 mb-2">Isi Berita Lengkap</label>
                                        <textarea id="edit-news-content" name="content" rows="15" required
                                                  class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                                                  placeholder="Tulis konten berita lengkap di sini...&#10;&#10;Tips:&#10;- Gunakan paragraf yang jelas&#10;- Sertakan informasi yang akurat&#10;- Tulis dengan bahasa yang mudah dipahami"></textarea>
                                        <p class="text-sm text-gray-500 mt-2">
                                            <i class="fas fa-lightbulb mr-1"></i>
                                            Pastikan konten informatif, akurat, dan mudah dipahami pembaca.
                                        </p>
                                    </div>
                                </div>

                                <!-- Status Section -->
                                <div class="form-section">
                                    <h4><i class="fas fa-toggle-on mr-2 text-orange-600"></i>Status Publikasi</h4>
                                    <div class="form-group">
                                        <label for="edit-news-status" class="block text-sm font-medium text-gray-700 mb-2">Status Berita</label>
                                        <select id="edit-news-status" name="status" required
                                                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent">
                                            <option value="">Pilih status...</option>
                                            <option value="draft">Draft</option>
                                            <option value="published">Publish</option>
                                        </select>
                                        <p class="text-sm text-gray-500 mt-2">
                                            <i class="fas fa-info-circle mr-1"></i>
                                            <strong>Draft:</strong> Berita disimpan tapi tidak ditampilkan di website.
                                            <strong>Publish:</strong> Berita akan ditampilkan di website.
                                        </p>
                                    </div>
                                </div>

                                <!-- Action Buttons -->
                                <div class="bg-gray-50 border-t border-gray-200 px-6 py-4 rounded-b-lg">
                                    <div class="flex flex-col sm:flex-row items-center justify-between space-y-3 sm:space-y-0">
                                        <button type="button" onclick="showSection('berita')"
                                                class="flex items-center px-4 py-2 text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded-lg transition-all duration-200">
                                            <i class="fas fa-arrow-left mr-2"></i>
                                            Kembali ke Daftar Berita
                                        </button>

                                        <div class="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-3 w-full sm:w-auto">
                                            <button type="button" onclick="viewNews(document.getElementById('edit-news-id').value)"
                                                    class="flex items-center justify-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-all duration-200 shadow-sm">
                                                <i class="fas fa-eye mr-2"></i>
                                                Lihat Detail
                                            </button>
                                            <button type="submit"
                                                    class="flex items-center justify-center px-6 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-all duration-200 shadow-sm font-medium">
                                                <i class="fas fa-save mr-2"></i>
                                                Simpan Perubahan
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                <!-- Pengaturan Section -->
                <div id="pengaturan-section" class="section-content p-6 hidden">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <!-- Website Settings -->
                <div class="bg-white rounded-lg shadow-md p-6">
                    <h3 class="text-lg font-semibold text-gray-800 mb-4">
                        <i class="fas fa-globe mr-2"></i>
                        Pengaturan Website
                    </h3>
                    <form class="space-y-4">
                        <div>
                            <label for="website-name" class="block text-sm font-medium text-gray-700 mb-2">Nama Website</label>
                            <input type="text" id="website-name" name="website-name" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary" value="<?php echo htmlspecialchars($settings['website_name']); ?>">
                        </div>
                        <div>
                            <label for="logo-file" class="block text-sm font-medium text-gray-700 mb-2">Logo Website</label>
                            <div class="space-y-3">
                                <div class="flex items-center space-x-4">
                                    <div id="logo-preview" class="w-20 h-20 bg-gray-200 rounded-lg flex items-center justify-center overflow-hidden border-2 border-dashed border-gray-300">
                                        <?php
                                        $logoPath = $settings['website_logo'];
                                        $showLogo = !empty($logoPath) && $logoPath !== 'http://localhost:3000/logo192.png' && !strpos($logoPath, 'assets/');
                                        if ($showLogo):
                                        ?>
                                            <img src="<?php echo htmlspecialchars($logoPath); ?>" alt="Logo Preview" class="w-full h-full object-cover rounded-lg" onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
                                            <div class="w-full h-full flex items-center justify-center" style="display: none;">
                                                <i class="fas fa-image text-gray-400 text-2xl"></i>
                                            </div>
                                        <?php else: ?>
                                            <i class="fas fa-image text-gray-400 text-2xl"></i>
                                        <?php endif; ?>
                                    </div>
                                    <div class="flex-1">
                                        <input type="file" id="logo-file" name="logo-file" accept="image/*" class="hidden" onchange="previewLogo(this)">
                                        <button type="button" onclick="document.getElementById('logo-file').click()" class="bg-primary text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors duration-200 mb-2 w-full">
                                            <i class="fas fa-upload mr-2"></i>Pilih File Logo
                                        </button>
                                        <div class="text-sm text-gray-600">
                                            <p>Format: JPG, PNG, GIF, WebP</p>
                                            <p class="text-xs">Maksimal 2MB, Rekomendasi: 200x200px</p>
                                        </div>
                                    </div>
                                </div>
                                <div id="logo-info" class="text-sm text-gray-500" style="display: none;">
                                    <p>File dipilih: <span id="logo-filename"></span></p>
                                    <p>Ukuran: <span id="logo-filesize"></span></p>
                                </div>
                            </div>
                        </div>
                        <div>
                            <label for="website-description" class="block text-sm font-medium text-gray-700 mb-2">Deskripsi Website</label>
                            <textarea id="website-description" name="website-description" rows="3" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary" placeholder="Deskripsi singkat tentang website"><?php echo htmlspecialchars($settings['website_description'] ?? 'Portal berita terkini dan terpercaya'); ?></textarea>
                        </div>
                        <button type="button" onclick="saveWebsiteSettings()" class="w-full bg-primary text-white py-2 rounded-lg hover:bg-blue-700 transition-colors duration-200">
                            Simpan Pengaturan
                        </button>
                    </form>
                </div>

                <!-- Color Settings -->
                <div class="bg-white rounded-lg shadow-md p-6">
                    <h3 class="text-lg font-semibold text-gray-800 mb-4">
                        <i class="fas fa-palette mr-2"></i>
                        Pengaturan Warna
                    </h3>
                    <form class="space-y-4">
                        <div>
                            <label for="primary-color" class="block text-sm font-medium text-gray-700 mb-2">Warna Primary</label>
                            <div class="flex items-center space-x-2">
                                <input type="color" id="primary-color" name="primary-color" value="<?php echo $settings['primary_color']; ?>" class="w-12 h-10 border border-gray-300 rounded">
                                <input type="text" id="primary-color-text" name="primary-color-text" value="<?php echo $settings['primary_color']; ?>" class="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary">
                            </div>
                        </div>
                        <div>
                            <label for="secondary-color" class="block text-sm font-medium text-gray-700 mb-2">Warna Secondary</label>
                            <div class="flex items-center space-x-2">
                                <input type="color" id="secondary-color" name="secondary-color" value="<?php echo $settings['secondary_color']; ?>" class="w-12 h-10 border border-gray-300 rounded">
                                <input type="text" id="secondary-color-text" name="secondary-color-text" value="<?php echo $settings['secondary_color']; ?>" class="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary">
                            </div>
                        </div>
                        <div>
                            <label for="accent-color" class="block text-sm font-medium text-gray-700 mb-2">Warna Accent</label>
                            <div class="flex items-center space-x-2">
                                <input type="color" id="accent-color" name="accent-color" value="<?php echo $settings['accent_color']; ?>" class="w-12 h-10 border border-gray-300 rounded">
                                <input type="text" id="accent-color-text" name="accent-color-text" value="<?php echo $settings['accent_color']; ?>" class="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary">
                            </div>
                        </div>
                        <button type="submit" onclick="saveColorSettings()" class="w-full bg-primary text-white py-2 rounded-lg hover:bg-blue-700 transition-colors duration-200">
                            Terapkan Warna
                        </button>
                    </form>
                </div>
            </div>
            </main>
        </div>
    </div>

    <!-- Add Berita Modal -->
    <div id="addBeritaModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-white rounded-lg shadow-xl max-w-md w-full">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-semibold text-gray-800">Tambah Berita Baru</h3>
                </div>
                <div class="p-6">
                    <form id="news-form" class="space-y-4" enctype="multipart/form-data">
                        <input type="hidden" id="news-id" name="news-id" value="">
                        <div>
                            <label for="news-title" class="block text-sm font-medium text-gray-700 mb-2">Judul Berita</label>
                            <input type="text" id="news-title" name="news-title" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary" required>
                        </div>
                        <div>
                            <label for="news-image" class="block text-sm font-medium text-gray-700 mb-2">Gambar Berita</label>
                            <div class="flex items-center space-x-4">
                                <div id="news-image-preview" class="w-20 h-20 bg-gray-200 rounded-lg flex items-center justify-center overflow-hidden">
                                    <i class="fas fa-image text-gray-400"></i>
                                </div>
                                <div class="flex-1">
                                    <input type="file" id="news-image" name="news-image" accept="image/*" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary">
                                    <p class="text-xs text-gray-500 mt-1">Format: JPG, PNG, GIF. Max: 2MB</p>
                                </div>
                            </div>
                        </div>
                        <div>
                            <label for="news-content" class="block text-sm font-medium text-gray-700 mb-2">Konten</label>
                            <textarea rows="4" id="news-content" name="news-content" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary" required></textarea>
                        </div>
                        <div>
                            <label for="news-category" class="block text-sm font-medium text-gray-700 mb-2">Kategori</label>
                            <select id="news-category" name="news-category" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary">
                                <option value="1">Umum</option>
                            </select>
                        </div>
                        <div>
                            <label for="news-status" class="block text-sm font-medium text-gray-700 mb-2">Status</label>
                            <select id="news-status" name="news-status" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary">
                                <option value="draft">Draft</option>
                                <option value="published">Published</option>
                            </select>
                        </div>
                        <div class="flex justify-end space-x-3">
                            <button type="button" onclick="hideAddBeritaModal()" class="px-4 py-2 text-gray-700 bg-gray-200 rounded-lg hover:bg-gray-300 transition-colors duration-200">
                                Batal
                            </button>
                            <button type="submit" class="px-4 py-2 bg-primary text-white rounded-lg hover:bg-blue-700 transition-colors duration-200">
                                Simpan
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Define global functions first for onclick/onchange handlers

        // Preview logo function - Available immediately
        window.previewLogo = function(input) {
            const file = input.files[0];
            const preview = document.getElementById('logo-preview');
            const logoInfo = document.getElementById('logo-info');
            const filename = document.getElementById('logo-filename');
            const filesize = document.getElementById('logo-filesize');

            if (file) {
                // Validate file type
                const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
                if (!allowedTypes.includes(file.type)) {
                    showNotification('Format file tidak didukung. Gunakan JPG, PNG, GIF, atau WebP.', 'error');
                    input.value = '';
                    return;
                }

                // Validate file size (2MB max)
                if (file.size > 2 * 1024 * 1024) {
                    showNotification('Ukuran file terlalu besar. Maksimal 2MB.', 'error');
                    input.value = '';
                    return;
                }

                // Show file info
                if (filename) filename.textContent = file.name;
                if (filesize) filesize.textContent = formatFileSize(file.size);
                if (logoInfo) logoInfo.style.display = 'block';

                // Preview image
                const reader = new FileReader();
                reader.onload = function(e) {
                    if (preview) {
                        preview.innerHTML = `<img src="${e.target.result}" alt="Logo Preview" class="w-full h-full object-cover rounded-lg">`;
                    }
                };
                reader.readAsDataURL(file);
            } else {
                if (logoInfo) logoInfo.style.display = 'none';
            }
        };

        // Format file size helper
        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        // Helper function for safe fetch with JSON validation
        async function safeFetch(url, options = {}) {
            try {
                const response = await fetch(url, options);

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const contentType = response.headers.get('content-type');
                if (!contentType || !contentType.includes('application/json')) {
                    const text = await response.text();
                    console.error('Non-JSON response:', text);
                    throw new Error('Server returned non-JSON response');
                }

                return await response.json();
            } catch (error) {
                console.error('Fetch error:', error);
                throw error;
            }
        }

        // Mobile menu toggle
        const mobileMenuButton = document.getElementById('mobile-menu-button');
        const mobileMenuButtonHeader = document.getElementById('mobile-menu-button-header');
        const sidebar = document.getElementById('sidebar');

        function toggleSidebar() {
            sidebar.classList.toggle('-translate-x-full');
        }

        if (mobileMenuButton) {
            mobileMenuButton.addEventListener('click', toggleSidebar);
        }

        if (mobileMenuButtonHeader) {
            mobileMenuButtonHeader.addEventListener('click', toggleSidebar);
        }

        // Close sidebar when clicking outside on mobile
        document.addEventListener('click', (e) => {
            if (window.innerWidth < 1024 &&
                !sidebar.contains(e.target) &&
                !mobileMenuButton?.contains(e.target) &&
                !mobileMenuButtonHeader?.contains(e.target)) {
                sidebar.classList.add('-translate-x-full');
            }
        });

        // Section navigation - Make it globally available
        window.showSection = function(sectionName) {
            // Hide all sections
            document.querySelectorAll('.section-content').forEach(section => {
                section.classList.add('hidden');
            });

            // Show selected section
            const targetSection = document.getElementById(sectionName + '-section');
            if (targetSection) {
                targetSection.classList.remove('hidden');
            }

            // Update page title
            const titles = {
                'dashboard': 'Dashboard',
                'berita': 'Manajemen Berita',
                'news-detail': 'Detail Berita',
                'news-edit': 'Edit Berita',
                'pengaturan': 'Pengaturan'
            };
            document.getElementById('page-title').textContent = titles[sectionName] || 'Dashboard';

            // Update active nav link
            document.querySelectorAll('.nav-link').forEach(link => {
                link.classList.remove('active', 'bg-primary', 'text-white');
                link.classList.add('text-gray-700');

                // Reset icon colors
                const icon = link.querySelector('i');
                if (icon) {
                    icon.classList.remove('text-white');
                    icon.classList.add('text-gray-400');
                }
            });

            // Find and update the clicked nav link
            const navLink = document.querySelector(`a[href="#${sectionName}"]`);
            if (navLink) {
                navLink.classList.add('active', 'bg-primary', 'text-white');
                navLink.classList.remove('text-gray-700');

                // Update icon color for active link
                const icon = navLink.querySelector('i');
                if (icon) {
                    icon.classList.remove('text-gray-400');
                    icon.classList.add('text-white');
                }
            }

            // Close mobile menu
            if (window.innerWidth < 1024) {
                sidebar.classList.add('-translate-x-full');
            }

            // Save current section to localStorage
            localStorage.setItem('currentSection', sectionName);

            // Load section-specific data
            if (sectionName === 'berita') {
                loadNews();
            }
        }

        // Load last visited section
        function loadLastSection() {
            const lastSection = localStorage.getItem('currentSection') || 'dashboard';

            // Show a brief indicator if returning to a saved section
            if (lastSection !== 'dashboard' && localStorage.getItem('currentSection')) {
                setTimeout(() => {
                    showNotification(`Kembali ke halaman ${lastSection === 'berita' ? 'Berita' : 'Pengaturan'}`, 'info');
                }, 500);
            }

            showSection(lastSection);
        }

        // Load categories for form
        function loadCategories() {
            safeFetch('api.php?action=get_categories')
                .then(data => {
                    const categorySelect = document.getElementById('news-category');
                    categorySelect.innerHTML = '<option value="">Pilih kategori...</option>';

                    if (Array.isArray(data)) {
                        data.forEach(category => {
                            // Exclude "Semua" and "Umum" categories
                            if (category.name.toLowerCase() !== 'semua' &&
                                category.name.toLowerCase() !== 'umum') {
                                const option = document.createElement('option');
                                option.value = category.id;
                                option.textContent = category.name;
                                categorySelect.appendChild(option);
                            }
                        });
                    }
                })
                .catch(error => {
                    console.error('Error loading categories:', error);
                    showNotification('Error memuat kategori', 'error');
                });
        }

        // Modal functions
        function showAddBeritaModal() {
            document.getElementById('addBeritaModal').classList.remove('hidden');
            document.getElementById('news-form').reset();
            document.getElementById('news-id').value = '';
            document.getElementById('news-image-preview').innerHTML = '<i class="fas fa-image text-gray-400"></i>';
            document.querySelector('#addBeritaModal h3').textContent = 'Tambah Berita Baru';

            // Load categories
            loadCategories();

            // Save modal state
            localStorage.setItem('modalOpen', 'addBerita');
        }

        function hideAddBeritaModal() {
            document.getElementById('addBeritaModal').classList.add('hidden');

            // Clear modal state
            localStorage.removeItem('modalOpen');
        }

        // Restore modal state if needed
        function restoreModalState() {
            const modalOpen = localStorage.getItem('modalOpen');
            if (modalOpen === 'addBerita') {
                // Don't auto-open modal on refresh to avoid confusion
                localStorage.removeItem('modalOpen');
            }
        }

        // Close modal when clicking outside
        document.getElementById('addBeritaModal').addEventListener('click', (e) => {
            if (e.target.id === 'addBeritaModal') {
                hideAddBeritaModal();
            }
        });

        // Load news data
        function loadNews() {
            safeFetch('api.php?action=get_news')
                .then(data => {
                    const tbody = document.getElementById('news-table-body');
                    tbody.innerHTML = '';

                    // Handle case where data is an error response
                    if (data && data.success === false) {
                        tbody.innerHTML = `<tr><td colspan="7" class="px-6 py-4 text-center text-red-500">Error: ${data.message}</td></tr>`;
                        return;
                    }

                    // Handle case where data is empty or not an array
                    if (!Array.isArray(data) || data.length === 0) {
                        tbody.innerHTML = '<tr><td colspan="7" class="px-6 py-4 text-center text-gray-500">Belum ada berita</td></tr>';
                        return;
                    }

                    data.forEach((news, index) => {
                        const row = document.createElement('tr');
                        row.className = 'hover:bg-gray-50';
                        const statusClass = news.status === 'published' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800';
                        const statusText = news.status === 'published' ? 'Published' : 'Draft';
                        const createdDate = new Date(news.created_at || news.date);
                        const formattedDate = createdDate.toLocaleDateString('id-ID', {
                            day: '2-digit',
                            month: '2-digit',
                            year: '2-digit'
                        });
                        const formattedTime = createdDate.toLocaleTimeString('id-ID', {
                            hour: '2-digit',
                            minute: '2-digit'
                        });

                        row.innerHTML = `
                            <td class="px-2 py-4 sticky left-0 bg-white z-10">
                                <div class="checkbox-container-mobile">
                                    <input type="checkbox" id="news-checkbox-${news.id}" class="news-checkbox rounded border-gray-300 text-primary focus:ring-primary mobile-checkbox-small" name="news-checkbox-${news.id}" value="${news.id}" onchange="updateSelectedCount()">
                                </div>
                            </td>
                            <td class="px-3 py-4 text-sm text-gray-900 font-medium">${index + 1}</td>
                            <td class="px-3 py-4">
                                <div class="w-12 h-12 bg-gray-200 rounded-lg flex items-center justify-center overflow-hidden">
                                    ${news.image ? `<img src="${news.image}" alt="News Image" class="w-full h-full object-cover">` : '<i class="fas fa-image text-gray-400"></i>'}
                                </div>
                            </td>
                            <td class="px-6 py-4">
                                <div class="text-sm font-medium text-gray-900 mb-1">${escapeHtml(news.title)}</div>
                                <div class="text-sm text-gray-500 line-clamp-2">${escapeHtml((news.content || '').substring(0, 100))}...</div>
                                ${news.category_name ? `<div class="mt-1"><span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium" style="background-color: ${news.category_color || '#3B82F6'}20; color: ${news.category_color || '#3B82F6'}">${escapeHtml(news.category_name)}</span></div>` : ''}
                                <div class="mt-1 flex items-center space-x-3 text-xs text-gray-500">
                                    <span><i class="fas fa-eye mr-1"></i>${news.views || 0}</span>
                                    <span><i class="fas fa-share mr-1"></i>${news.share || 0}</span>
                                    <span><i class="fas fa-heart mr-1"></i>${news.likes || 0}</span>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 text-center">
                                <div class="flex flex-col items-center">
                                    <span class="text-xs font-medium text-gray-700">${formattedDate}</span>
                                    <span class="text-xs text-gray-500 mt-0.5">${formattedTime}</span>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${statusClass}">${statusText}</span>
                                ${news.featured ? '<div class="mt-1"><span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-purple-100 text-purple-800">Featured</span></div>' : ''}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium align-top h-full">
                                <div class="flex space-x-1 items-start justify-start h-full min-h-[60px]">
                                    <button onclick="viewNews(${news.id})" class="text-blue-600 hover:text-blue-900 p-1.5 rounded hover:bg-blue-50 transition-colors duration-200" title="Lihat">
                                        <i class="fas fa-eye text-sm"></i>
                                    </button>
                                    <button onclick="editNews(${news.id})" class="text-indigo-600 hover:text-indigo-900 p-1.5 rounded hover:bg-indigo-50 transition-colors duration-200" title="Edit">
                                        <i class="fas fa-edit text-sm"></i>
                                    </button>
                                    <button onclick="deleteNews(${news.id})" class="text-red-600 hover:text-red-900 p-1.5 rounded hover:bg-red-50 transition-colors duration-200" title="Hapus">
                                        <i class="fas fa-trash text-sm"></i>
                                    </button>
                                </div>
                            </td>
                        `;
                        tbody.appendChild(row);
                    });

                    // Setup table scroll indicators
                    setupTableScrollIndicators();

                    // Restore selected checkboxes
                    setTimeout(() => {
                        restoreSelectedCheckboxes();
                    }, 100);
                })
                .catch(error => {
                    console.error('Error loading news:', error);
                    const tbody = document.getElementById('news-table-body');
                    tbody.innerHTML = `<tr><td colspan="7" class="px-6 py-4 text-center text-red-500">Error loading news: ${error.message}</td></tr>`;
                    showNotification('Error loading news: ' + error.message, 'error');
                });
        }

        // Helper function to escape HTML
        function escapeHtml(text) {
            // Handle null, undefined, or non-string values
            if (text === null || text === undefined) {
                return '';
            }

            // Convert to string if not already
            text = String(text);

            const map = {
                '&': '&amp;',
                '<': '&lt;',
                '>': '&gt;',
                '"': '&quot;',
                "'": '&#039;'
            };
            return text.replace(/[&<>"']/g, function(m) { return map[m]; });
        }

        // Notification function
        function showNotification(message, type = 'info') {
            // Create notification element
            const notification = document.createElement('div');
            notification.className = `fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg max-w-sm transition-all duration-300 transform translate-x-full`;

            // Set colors based on type
            const colors = {
                'success': 'bg-green-500 text-white',
                'error': 'bg-red-500 text-white',
                'warning': 'bg-yellow-500 text-white',
                'info': 'bg-blue-500 text-white'
            };

            notification.className += ` ${colors[type] || colors.info}`;

            // Set content
            notification.innerHTML = `
                <div class="flex items-center">
                    <div class="flex-1">
                        <p class="text-sm font-medium">${escapeHtml(message)}</p>
                    </div>
                    <button onclick="this.parentElement.parentElement.remove()" class="ml-4 text-white hover:text-gray-200">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            `;

            // Add to page
            document.body.appendChild(notification);

            // Animate in
            setTimeout(() => {
                notification.classList.remove('translate-x-full');
            }, 100);

            // Auto remove after 5 seconds
            setTimeout(() => {
                notification.classList.add('translate-x-full');
                setTimeout(() => {
                    if (notification.parentElement) {
                        notification.remove();
                    }
                }, 300);
            }, 5000);
        }

        // Setup table scroll indicators
        function setupTableScrollIndicators() {
            const tableContainer = document.getElementById('table-container');
            if (!tableContainer) return;

            function updateScrollIndicators() {
                const { scrollLeft, scrollWidth, clientWidth } = tableContainer;
                const container = tableContainer.closest('.table-container');
                const stickyColumns = tableContainer.querySelectorAll('.sticky');

                if (scrollLeft > 0) {
                    container.classList.add('scrolled-left');
                    stickyColumns.forEach(col => col.classList.add('sticky-shadow'));
                } else {
                    container.classList.remove('scrolled-left');
                    stickyColumns.forEach(col => col.classList.remove('sticky-shadow'));
                }

                if (scrollLeft + clientWidth >= scrollWidth - 1) {
                    container.classList.add('scrolled-right');
                } else {
                    container.classList.remove('scrolled-right');
                }

                // Save scroll position
                localStorage.setItem('tableScrollLeft', scrollLeft);
            }

            tableContainer.addEventListener('scroll', updateScrollIndicators);

            // Restore scroll position
            const savedScrollLeft = localStorage.getItem('tableScrollLeft');
            if (savedScrollLeft) {
                tableContainer.scrollLeft = parseInt(savedScrollLeft);
            }

            updateScrollIndicators(); // Initial check
        }

        // Edit news
        function editNews(id) {
            safeFetch(`api.php?action=get_news_by_id&id=${id}`)
                .then(result => {
                    if (result.success) {
                        const news = result.data;

                        // Load categories first, then populate form
                        loadCategories();

                        setTimeout(() => {
                            document.getElementById('news-id').value = news.id;
                            document.getElementById('news-title').value = news.title;
                            document.getElementById('news-content').value = news.content;
                            document.getElementById('news-status').value = news.status;

                            // Set category if available
                            if (news.category_id) {
                                document.getElementById('news-category').value = news.category_id;
                            }

                            // Show existing image if available
                            const imagePreview = document.getElementById('news-image-preview');
                            if (news.image) {
                                imagePreview.innerHTML = `<img src="${news.image}" alt="Current Image" class="w-full h-full object-cover">`;
                            } else {
                                imagePreview.innerHTML = '<i class="fas fa-image text-gray-400"></i>';
                            }

                            document.querySelector('#addBeritaModal h3').textContent = 'Edit Berita';
                            document.getElementById('addBeritaModal').classList.remove('hidden');
                        }, 100);
                    } else {
                        showNotification(result.message, 'error');
                    }
                })
                .catch(error => {
                    console.error('Error loading news:', error);
                    showNotification('Error loading news', 'error');
                });
        }

        // Delete news
        function deleteNews(id) {
            if (!confirm('Apakah Anda yakin ingin menghapus berita ini?')) {
                return;
            }

            const formData = new FormData();
            formData.append('action', 'delete_news');
            formData.append('id', id);

            fetch('api.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(result => {
                if (result.success) {
                    showNotification(result.message, 'success');
                    loadNews();
                } else {
                    showNotification(result.message, 'error');
                }
            })
            .catch(error => {
                console.error('Error deleting news:', error);
                showNotification('Error deleting news', 'error');
            });
        }

        // Handle news form submission (with null check)
        const newsForm = document.getElementById('news-form');
        if (newsForm) {
            newsForm.addEventListener('submit', function(e) {
                e.preventDefault();

                const formData = new FormData();
                const newsId = document.getElementById('news-id').value;
                const imageFile = document.getElementById('news-image').files[0];

                if (newsId) {
                    formData.append('action', 'update_news');
                    formData.append('id', newsId);
                } else {
                    formData.append('action', 'add_news');
                }

                formData.append('title', document.getElementById('news-title').value);
                formData.append('content', document.getElementById('news-content').value);
                formData.append('status', document.getElementById('news-status').value);
                formData.append('category_id', document.getElementById('news-category').value);

            if (imageFile) {
                formData.append('image', imageFile);
            }

            // Show loading state
            const submitBtn = e.target.querySelector('button[type="submit"]');
            const originalText = submitBtn.innerHTML;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Menyimpan...';
            submitBtn.disabled = true;

            fetch('api.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(result => {
                if (result.success) {
                    showNotification(result.message, 'success');
                    hideAddBeritaModal();
                    loadNews();
                } else {
                    showNotification(result.message, 'error');
                }
            })
            .catch(error => {
                console.error('Error saving news:', error);
                showNotification('Error saving news: ' + error.message, 'error');
            })
            .finally(() => {
                // Restore button state
                submitBtn.innerHTML = originalText;
                submitBtn.disabled = false;
            });
            });
        }

        // Checkbox functions
        function toggleSelectAll() {
            const selectAll = document.getElementById('select-all');
            const checkboxes = document.querySelectorAll('.news-checkbox');

            checkboxes.forEach(checkbox => {
                checkbox.checked = selectAll.checked;
            });

            updateSelectedCount();
        }

        function updateSelectedCount() {
            const checkboxes = document.querySelectorAll('.news-checkbox:checked');
            const count = checkboxes.length;
            const selectAll = document.getElementById('select-all');
            const deleteBtn = document.getElementById('delete-selected-btn');
            const countSpan = document.getElementById('selected-count');

            countSpan.textContent = count;
            deleteBtn.disabled = count === 0;

            // Save selected items
            const selectedIds = Array.from(checkboxes).map(cb => cb.value);
            localStorage.setItem('selectedNewsIds', JSON.stringify(selectedIds));

            // Update select all checkbox state
            const allCheckboxes = document.querySelectorAll('.news-checkbox');
            if (count === 0) {
                selectAll.indeterminate = false;
                selectAll.checked = false;
            } else if (count === allCheckboxes.length) {
                selectAll.indeterminate = false;
                selectAll.checked = true;
            } else {
                selectAll.indeterminate = true;
                selectAll.checked = false;
            }
        }

        // Restore selected checkboxes
        function restoreSelectedCheckboxes() {
            const selectedIds = JSON.parse(localStorage.getItem('selectedNewsIds') || '[]');
            selectedIds.forEach(id => {
                const checkbox = document.querySelector(`.news-checkbox[value="${id}"]`);
                if (checkbox) {
                    checkbox.checked = true;
                }
            });
            updateSelectedCount();
        }

        // Delete selected news
        function deleteSelectedNews() {
            const checkboxes = document.querySelectorAll('.news-checkbox:checked');
            const ids = Array.from(checkboxes).map(cb => cb.value);

            if (ids.length === 0) {
                showNotification('Pilih berita yang ingin dihapus', 'error');
                return;
            }

            if (!confirm(`Apakah Anda yakin ingin menghapus ${ids.length} berita terpilih?`)) {
                return;
            }

            // Delete each selected news
            let completed = 0;
            let errors = 0;

            ids.forEach(id => {
                const formData = new FormData();
                formData.append('action', 'delete_news');
                formData.append('id', id);

                fetch('api.php', {
                    method: 'POST',
                    body: formData
                })
                .then(response => response.json())
                .then(result => {
                    completed++;
                    if (!result.success) errors++;

                    if (completed === ids.length) {
                        if (errors === 0) {
                            showNotification(`${ids.length} berita berhasil dihapus`, 'success');
                        } else {
                            showNotification(`${ids.length - errors} berita berhasil dihapus, ${errors} gagal`, 'error');
                        }
                        // Clear selected items from localStorage
                        localStorage.removeItem('selectedNewsIds');
                        loadNews();
                        updateSelectedCount();
                    }
                })
                .catch(error => {
                    completed++;
                    errors++;
                    console.error('Error deleting news:', error);

                    if (completed === ids.length) {
                        showNotification(`${ids.length - errors} berita berhasil dihapus, ${errors} gagal`, 'error');
                        loadNews();
                        updateSelectedCount();
                    }
                });
            });
        }

        // View news function
        function viewNews(id) {
            fetch(`api.php?action=get_news_by_id&id=${id}`)
                .then(response => response.json())
                .then(result => {
                    if (result.success) {
                        const news = result.data;
                        // Create a simple modal to view news
                        const modal = document.createElement('div');
                        modal.className = 'fixed inset-0 bg-gray-600 bg-opacity-50 z-50 flex items-center justify-center p-4';
                        modal.innerHTML = `
                            <div class="bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
                                <div class="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
                                    <h3 class="text-lg font-semibold text-gray-800">Detail Berita</h3>
                                    <button onclick="this.closest('.fixed').remove()" class="text-gray-400 hover:text-gray-600">
                                        <i class="fas fa-times"></i>
                                    </button>
                                </div>
                                <div class="p-6">
                                    <h2 class="text-xl font-bold text-gray-900 mb-4">${news.title}</h2>
                                    ${news.image ? `<img src="${news.image}" alt="News Image" class="w-full h-48 object-cover rounded-lg mb-4">` : ''}
                                    <div class="text-gray-700 mb-4">${news.content}</div>
                                    <div class="flex items-center justify-between text-sm text-gray-500">
                                        <span>Status: <span class="px-2 py-1 rounded-full text-xs ${news.status === 'published' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'}">${news.status === 'published' ? 'Published' : 'Draft'}</span></span>
                                        <span>${new Date(news.created_at).toLocaleString('id-ID')}</span>
                                    </div>
                                </div>
                            </div>
                        `;
                        document.body.appendChild(modal);

                        // Close modal when clicking outside
                        modal.addEventListener('click', (e) => {
                            if (e.target === modal) {
                                modal.remove();
                            }
                        });
                    } else {
                        showNotification(result.message, 'error');
                    }
                })
                .catch(error => {
                    console.error('Error loading news:', error);
                    showNotification('Error loading news', 'error');
                });
        }

        // Handle image preview in form (with null check)
        const newsImage = document.getElementById('news-image');
        if (newsImage) {
            newsImage.addEventListener('change', function(e) {
                const file = e.target.files[0];
                const preview = document.getElementById('news-image-preview');

                if (file && preview) {
                    const reader = new FileReader();
                    reader.onload = function(e) {
                        preview.innerHTML = `<img src="${e.target.result}" alt="Preview" class="w-full h-full object-cover">`;
                    };
                    reader.readAsDataURL(file);
                } else if (preview) {
                    preview.innerHTML = '<i class="fas fa-image text-gray-400"></i>';
                }
            });
        }

        // Save website settings
        function saveWebsiteSettings() {
            const formData = new FormData();
            formData.append('action', 'update_settings');
            formData.append('website_name', document.getElementById('website-name').value);

            fetch('api.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(result => {
                if (result.success) {
                    showNotification(result.message, 'success');
                    // Update page title
                    document.title = document.getElementById('website-name').value + ' - Admin Dashboard';

                    // Update sidebar header
                    const sidebarTitle = document.querySelector('#sidebar h1');
                    if (sidebarTitle) {
                        sidebarTitle.textContent = document.getElementById('website-name').value;
                    }

                    // Update sidebar logo if new logo was uploaded
                    if (logoFile && result.data && result.data.website_logo) {
                        const sidebarLogo = document.getElementById('sidebar-logo');
                        if (sidebarLogo) {
                            sidebarLogo.src = result.data.website_logo.startsWith('http')
                                ? result.data.website_logo
                                : 'http://localhost' + result.data.website_logo;
                        }
                    }
                } else {
                    showNotification(result.message, 'error');
                }
            })
            .catch(error => {
                console.error('Error saving settings:', error);
                showNotification('Error saving settings', 'error');
            });
        }

        // Save color settings
        function saveColorSettings() {
            const formData = new FormData();
            formData.append('action', 'update_settings');
            formData.append('primary_color', document.getElementById('primary-color').value);
            formData.append('secondary_color', document.getElementById('secondary-color').value);
            formData.append('accent_color', document.getElementById('accent-color').value);

            fetch('api.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(result => {
                if (result.success) {
                    showNotification(result.message + ' - Refresh halaman untuk melihat perubahan', 'success');
                } else {
                    showNotification(result.message, 'error');
                }
            })
            .catch(error => {
                console.error('Error saving color settings:', error);
                showNotification('Error saving color settings', 'error');
            });
        }

        // Handle logo upload (with null check)
        const logoUpload = document.getElementById('logo-upload');
        if (logoUpload) {
            logoUpload.addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (!file) return;

            const formData = new FormData();
            formData.append('action', 'upload_logo');
            formData.append('logo', file);

            fetch('api.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(result => {
                if (result.success) {
                    showNotification(result.message, 'success');
                    // Update logo preview
                    document.getElementById('logo-preview').innerHTML = `<img src="${result.filepath}" alt="Logo" class="w-full h-full object-cover">`;
                } else {
                    showNotification(result.message, 'error');
                }
            })
            .catch(error => {
                console.error('Error uploading logo:', error);
                showNotification('Error uploading logo', 'error');
            });
            });
        }

        // Color input synchronization (with null checks)
        const primaryColor = document.getElementById('primary-color');
        const primaryColorText = document.getElementById('primary-color-text');
        const secondaryColor = document.getElementById('secondary-color');
        const secondaryColorText = document.getElementById('secondary-color-text');
        const accentColor = document.getElementById('accent-color');
        const accentColorText = document.getElementById('accent-color-text');

        if (primaryColor && primaryColorText) {
            primaryColor.addEventListener('change', function() {
                primaryColorText.value = this.value;
            });
            primaryColorText.addEventListener('change', function() {
                primaryColor.value = this.value;
            });
        }

        if (secondaryColor && secondaryColorText) {
            secondaryColor.addEventListener('change', function() {
                secondaryColorText.value = this.value;
            });
            secondaryColorText.addEventListener('change', function() {
                secondaryColor.value = this.value;
            });
        }

        if (accentColor && accentColorText) {
            accentColor.addEventListener('change', function() {
                accentColorText.value = this.value;
            });
            accentColorText.addEventListener('change', function() {
                accentColor.value = this.value;
            });
        }

        // Notification system (removed duplicate - using the one defined earlier)

        // Global variable to store current news ID
        let currentNewsId = null;

        // View news detail function
        window.viewNews = function(id) {
            currentNewsId = id;

            // Fetch news detail
            safeFetch(`api.php?action=get_news_by_id&id=${id}`)
                .then(data => {
                    if (data.success && data.data) {
                        const news = data.data;

                        // Update breadcrumb
                        document.getElementById('news-detail-breadcrumb').textContent = `Detail: ${news.title || 'Berita'}`;

                        // Build detail content
                        const detailContent = `
                            <div class="space-y-6">
                                <!-- Header -->
                                <div class="border-b border-gray-200 pb-6">
                                    <h1 class="text-3xl font-bold text-gray-900 mb-2">${escapeHtml(news.title || 'Judul Tidak Tersedia')}</h1>
                                    <div class="flex items-center space-x-4 text-sm text-gray-600">
                                        <span class="flex items-center">
                                            <i class="fas fa-folder mr-1"></i>
                                            ${escapeHtml(news.category_name || 'Umum')}
                                        </span>
                                        <span class="flex items-center">
                                            <i class="fas fa-calendar mr-1"></i>
                                            ${formatDate(news.created_at)}
                                        </span>
                                        <span class="flex items-center">
                                            <i class="fas fa-eye mr-1"></i>
                                            ${news.views || 0} views
                                        </span>
                                    </div>
                                </div>

                                <!-- Image -->
                                ${news.image ? `
                                    <div class="text-center">
                                        <img src="${getImageUrl(news.image)}" alt="${escapeHtml(news.title || 'Gambar Berita')}"
                                             class="max-w-full h-auto rounded-lg shadow-md mx-auto"
                                             style="max-height: 400px;"
                                             onerror="this.parentElement.style.display='none';">
                                    </div>
                                ` : ''}

                                <!-- Content -->
                                <div>
                                    <h3 class="text-lg font-semibold text-gray-900 mb-3">Konten Berita</h3>
                                    <div class="prose max-w-none text-gray-700 leading-relaxed">
                                        ${escapeHtml(news.content || 'Tidak ada konten').replace(/\n/g, '<br>')}
                                    </div>
                                </div>

                                <!-- Metadata -->
                                <div class="bg-gray-50 rounded-lg p-4">
                                    <h3 class="text-lg font-semibold text-gray-900 mb-3">Informasi Tambahan</h3>
                                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                                        <div>
                                            <span class="font-medium text-gray-600">ID Berita:</span>
                                            <span class="text-gray-800">${news.id}</span>
                                        </div>
                                        <div>
                                            <span class="font-medium text-gray-600">Slug:</span>
                                            <span class="text-gray-800">${news.slug || '-'}</span>
                                        </div>
                                        <div>
                                            <span class="font-medium text-gray-600">Dibuat:</span>
                                            <span class="text-gray-800">${formatDateTime(news.created_at)}</span>
                                        </div>
                                        <div>
                                            <span class="font-medium text-gray-600">Diupdate:</span>
                                            <span class="text-gray-800">${formatDateTime(news.updated_at)}</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        `;

                        document.getElementById('news-detail-content').innerHTML = detailContent;
                        showSection('news-detail');
                    } else {
                        showNotification('Gagal memuat detail berita', 'error');
                    }
                })
                .catch(error => {
                    console.error('Error loading news detail:', error);
                    showNotification('Error memuat detail berita', 'error');
                });
        };

        // Edit news function
        window.editNews = function(id) {
            currentNewsId = id;

            // Load categories first
            loadCategoriesForEdit();

            // Fetch news detail
            safeFetch(`api.php?action=get_news_by_id&id=${id}`)
                .then(data => {
                    if (data.success && data.data) {
                        const news = data.data;

                        // Update breadcrumb
                        document.getElementById('news-edit-breadcrumb').textContent = `Edit: ${news.title}`;

                        // Fill form
                        document.getElementById('edit-news-id').value = news.id;
                        document.getElementById('edit-news-title').value = news.title;
                        document.getElementById('edit-news-content').value = news.content;
                        document.getElementById('edit-news-status').value = news.status || 'draft';

                        // Set category after categories are loaded
                        setTimeout(() => {
                            document.getElementById('edit-news-category').value = news.category_id;
                        }, 100);

                        // Show current image
                        if (news.image) {
                            document.getElementById('edit-current-image').innerHTML = `
                                <div class="text-center">
                                    <p class="text-sm text-gray-600 mb-2">Gambar saat ini:</p>
                                    <img src="${getImageUrl(news.image)}" alt="Current image"
                                         class="max-w-xs h-auto rounded-lg shadow-md mx-auto">
                                </div>
                            `;
                        } else {
                            document.getElementById('edit-current-image').innerHTML = '';
                        }

                        showSection('news-edit');
                    } else {
                        showNotification('Gagal memuat data berita untuk edit', 'error');
                    }
                })
                .catch(error => {
                    console.error('Error loading news for edit:', error);
                    showNotification('Error memuat data berita', 'error');
                });
        };

        // Load categories for edit form
        function loadCategoriesForEdit() {
            safeFetch('api.php?action=get_categories')
                .then(data => {
                    const categorySelect = document.getElementById('edit-news-category');
                    categorySelect.innerHTML = '<option value="">Pilih kategori...</option>';

                    if (Array.isArray(data)) {
                        data.forEach(category => {
                            // Exclude "Semua" and "Umum" categories
                            if (category.name.toLowerCase() !== 'semua' &&
                                category.name.toLowerCase() !== 'umum') {
                                const option = document.createElement('option');
                                option.value = category.id;
                                option.textContent = category.name;
                                categorySelect.appendChild(option);
                            }
                        });
                    }
                })
                .catch(error => {
                    console.error('Error loading categories:', error);
                    showNotification('Error memuat kategori', 'error');
                });
        }

        // Helper functions
        function getImageUrl(imagePath) {
            if (!imagePath) return '';

            if (imagePath.startsWith('http://') || imagePath.startsWith('https://')) {
                return imagePath;
            }

            if (imagePath.startsWith('/react-news/uploads/')) {
                return `http://localhost${imagePath}`;
            }

            if (imagePath.startsWith('uploads/')) {
                return `http://localhost/react-news/${imagePath}`;
            }

            return `http://localhost/react-news/${imagePath}`;
        }

        function formatDate(dateString) {
            if (!dateString) return '-';
            const date = new Date(dateString);
            return date.toLocaleDateString('id-ID', {
                year: 'numeric',
                month: 'long',
                day: 'numeric'
            });
        }

        function formatDateTime(dateString) {
            if (!dateString) return '-';
            const date = new Date(dateString);
            return date.toLocaleString('id-ID', {
                year: 'numeric',
                month: 'long',
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit'
            });
        }

        // Settings functions - Make globally available

        window.saveWebsiteSettings = function() {
            const websiteName = document.getElementById('website-name').value;
            const websiteDescription = document.getElementById('website-description').value;
            const logoFile = document.getElementById('logo-file').files[0];

            if (!websiteName.trim()) {
                showNotification('Nama website tidak boleh kosong', 'error');
                return;
            }

            const formData = new FormData();
            formData.append('action', 'update_website_settings');
            formData.append('website_name', websiteName);
            formData.append('website_description', websiteDescription);

            // Add logo file if selected
            if (logoFile) {
                formData.append('logo_file', logoFile);
            }

            // Show loading state
            const saveButton = document.querySelector('button[onclick="saveWebsiteSettings()"]');
            const originalText = saveButton.innerHTML;
            saveButton.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Menyimpan...';
            saveButton.disabled = true;

            fetch('api.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(result => {
                if (result.success) {
                    showNotification('Pengaturan website berhasil disimpan! Perubahan akan terlihat di halaman utama.', 'success');

                    // Update the header title
                    const headerTitle = document.querySelector('.bg-primary h1');
                    if (headerTitle) {
                        headerTitle.innerHTML = `<i class="fas fa-tachometer-alt mr-2"></i>${escapeHtml(websiteName)}`;
                    }

                    // Update logo preview if new logo was uploaded
                    if (logoFile && result.data && result.data.website_logo) {
                        updateLogoPreview(result.data.website_logo);
                    }

                    // Clear file input after successful upload
                    const logoFileInput = document.getElementById('logo-file');
                    if (logoFileInput) {
                        logoFileInput.value = '';
                    }
                    const logoInfo = document.getElementById('logo-info');
                    if (logoInfo) {
                        logoInfo.style.display = 'none';
                    }

                    // Trigger settings refresh in LandingPage (if open in another tab)
                    try {
                        localStorage.setItem('settings_updated', Date.now().toString());
                        localStorage.removeItem('settings_updated'); // Trigger storage event
                    } catch (e) {
                        console.log('Could not trigger settings refresh:', e);
                    }

                    // Also update settings API cache
                    fetch('api/settings.php?action=update', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            website_name: websiteName,
                            website_logo: result.data.website_logo || '',
                            website_description: websiteDescription
                        })
                    }).catch(err => console.log('Settings API sync error:', err));

                } else {
                    showNotification(result.message, 'error');
                }
            })
            .catch(error => {
                console.error('Error saving website settings:', error);
                showNotification('Error saving website settings', 'error');
            })
            .finally(() => {
                // Restore button state
                saveButton.innerHTML = originalText;
                saveButton.disabled = false;
            });
        }

        function updateLogoPreview(logoUrl) {
            const logoPreview = document.getElementById('logo-preview');
            if (!logoPreview) return;

            if (logoUrl && logoUrl.trim() && !logoUrl.includes('assets/')) {
                // Create new image element with error handling
                logoPreview.innerHTML = `
                    <img src="${logoUrl}" alt="Logo Preview" class="w-full h-full object-cover rounded-lg"
                         onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
                    <div class="w-full h-full flex items-center justify-center" style="display: none;">
                        <i class="fas fa-image text-gray-400 text-2xl"></i>
                    </div>
                `;
            } else {
                // Show default icon
                logoPreview.innerHTML = '<i class="fas fa-image text-gray-400 text-2xl"></i>';
            }
        }

        function saveColorSettings() {
            const primaryColor = document.getElementById('primary-color').value;
            const secondaryColor = document.getElementById('secondary-color').value;
            const accentColor = document.getElementById('accent-color').value;

            const formData = new FormData();
            formData.append('action', 'update_color_settings');
            formData.append('primary_color', primaryColor);
            formData.append('secondary_color', secondaryColor);
            formData.append('accent_color', accentColor);

            fetch('api.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(result => {
                if (result.success) {
                    showNotification(result.message, 'success');
                    // Reload page to apply new colors
                    setTimeout(() => {
                        location.reload();
                    }, 1000);
                } else {
                    showNotification(result.message, 'error');
                }
            })
            .catch(error => {
                console.error('Error saving color settings:', error);
                showNotification('Error saving color settings', 'error');
            });
        }

        // Load dashboard statistics
        function loadDashboardStats() {
            safeFetch('api.php?action=get_stats')
                .then(data => {
                    // Update stats cards
                    const totalUsersEl = document.querySelector('[data-stat="total-users"]');
                    const totalNewsEl = document.querySelector('[data-stat="total-news"]');
                    const pageViewsEl = document.querySelector('[data-stat="page-views"]');
                    const revenueEl = document.querySelector('[data-stat="revenue"]');

                    if (totalUsersEl) totalUsersEl.textContent = data.total_users || 0;
                    if (totalNewsEl) totalNewsEl.textContent = data.total_news || 0;
                    if (pageViewsEl) pageViewsEl.textContent = data.page_views || 0;
                    if (revenueEl) revenueEl.textContent = data.revenue || 0;

                    // Update additional stats if available
                    const publishedNewsEl = document.querySelector('[data-stat="published-news"]');
                    const draftNewsEl = document.querySelector('[data-stat="draft-news"]');
                    const totalSharesEl = document.querySelector('[data-stat="total-shares"]');
                    const totalLikesEl = document.querySelector('[data-stat="total-likes"]');
                    const savedPostsEl = document.querySelector('[data-stat="saved-posts"]');

                    if (publishedNewsEl) publishedNewsEl.textContent = data.published_news || 0;
                    if (draftNewsEl) draftNewsEl.textContent = data.draft_news || 0;
                    if (totalSharesEl) totalSharesEl.textContent = data.total_shares || 0;
                    if (totalLikesEl) totalLikesEl.textContent = data.total_likes || 0;
                    if (savedPostsEl) savedPostsEl.textContent = data.saved_posts || 0;
                })
                .catch(error => {
                    console.error('Error loading dashboard stats:', error);
                });
        }

        // Handle edit form submission
        function initializeEditForm() {
            const editForm = document.getElementById('edit-news-form');
            if (editForm) {
                editForm.addEventListener('submit', function(e) {
                    e.preventDefault();

                    const formData = new FormData(this);
                    formData.append('action', 'update_news');

                    // Show loading state
                    const submitBtn = this.querySelector('button[type="submit"]');
                    const originalText = submitBtn.innerHTML;
                    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Menyimpan...';
                    submitBtn.disabled = true;

                    fetch('api.php', {
                        method: 'POST',
                        body: formData
                    })
                    .then(response => response.json())
                    .then(result => {
                        if (result.success) {
                            showNotification('Berita berhasil diupdate!', 'success');

                            // Refresh news list
                            loadNews();

                            // Go back to news list or show updated detail
                            setTimeout(() => {
                                showSection('berita');
                            }, 1000);
                        } else {
                            showNotification(result.message || 'Gagal mengupdate berita', 'error');
                        }
                    })
                    .catch(error => {
                        console.error('Error updating news:', error);
                        showNotification('Error mengupdate berita', 'error');
                    })
                    .finally(() => {
                        // Reset button state
                        submitBtn.innerHTML = originalText;
                        submitBtn.disabled = false;
                    });
                });
            }
        }

        // Initialize dashboard
        document.addEventListener('DOMContentLoaded', function() {
            // Load last visited section
            loadLastSection();

            // Initialize edit form
            initializeEditForm();

            // Restore modal state
            restoreModalState();

            // Load initial data for all sections
            loadNews();
            loadCategories();
            loadDashboardStats();

            // Refresh stats every 30 seconds
            setInterval(loadDashboardStats, 30000);
        });
    </script>
</body>
</html>
