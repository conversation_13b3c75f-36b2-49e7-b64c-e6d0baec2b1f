/* Custom CSS for Admin Dashboard */

/* Smooth transitions for all elements */
* {
    transition: all 0.3s ease;
}

/* Custom scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* Loading animation */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Hover effects for cards */
.card-hover {
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.card-hover:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

/* Button animations */
.btn-animate {
    position: relative;
    overflow: hidden;
}

.btn-animate::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.btn-animate:hover::before {
    left: 100%;
}

/* Sidebar animations */
.sidebar-item {
    position: relative;
}

.sidebar-item::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    height: 100%;
    width: 3px;
    background: var(--primary-color);
    transform: scaleY(0);
    transition: transform 0.3s ease;
}

.sidebar-item.active::before {
    transform: scaleY(1);
}

/* Table animations */
.table-row {
    transition: background-color 0.2s ease;
}

.table-row:hover {
    background-color: rgba(59, 130, 246, 0.05);
}

/* Modal animations */
.modal-enter {
    animation: modalEnter 0.3s ease-out;
}

.modal-exit {
    animation: modalExit 0.3s ease-in;
}

@keyframes modalEnter {
    from {
        opacity: 0;
        transform: scale(0.9);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes modalExit {
    from {
        opacity: 1;
        transform: scale(1);
    }
    to {
        opacity: 0;
        transform: scale(0.9);
    }
}

/* Notification animations */
.notification-enter {
    animation: slideInRight 0.3s ease-out;
}

.notification-exit {
    animation: slideOutRight 0.3s ease-in;
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes slideOutRight {
    from {
        transform: translateX(0);
        opacity: 1;
    }
    to {
        transform: translateX(100%);
        opacity: 0;
    }
}

/* Form focus effects */
.form-input:focus {
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    border-color: var(--primary-color);
}

/* Stats card animations */
.stats-card {
    background: linear-gradient(135deg, #fff 0%, #f8fafc 100%);
    border-left: 4px solid var(--primary-color);
}

.stats-icon {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
}

/* Responsive utilities */
@media (max-width: 768px) {
    .mobile-hidden {
        display: none !important;
    }
    
    .mobile-full {
        width: 100% !important;
    }
    
    .mobile-text-sm {
        font-size: 0.875rem !important;
    }
}

/* Dark mode support (for future implementation) */
@media (prefers-color-scheme: dark) {
    .dark-mode {
        background-color: #1a202c;
        color: #e2e8f0;
    }
    
    .dark-mode .bg-white {
        background-color: #2d3748 !important;
    }
    
    .dark-mode .text-gray-900 {
        color: #e2e8f0 !important;
    }
    
    .dark-mode .text-gray-700 {
        color: #a0aec0 !important;
    }
    
    .dark-mode .border-gray-200 {
        border-color: #4a5568 !important;
    }
}

/* Print styles */
@media print {
    .no-print {
        display: none !important;
    }
    
    .sidebar {
        display: none !important;
    }
    
    .main-content {
        margin-left: 0 !important;
    }
}

/* Custom utilities */
.text-shadow {
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.backdrop-blur {
    backdrop-filter: blur(10px);
}

.gradient-bg {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
}

/* Animation delays for staggered effects */
.delay-100 { animation-delay: 0.1s; }
.delay-200 { animation-delay: 0.2s; }
.delay-300 { animation-delay: 0.3s; }
.delay-400 { animation-delay: 0.4s; }
.delay-500 { animation-delay: 0.5s; }
