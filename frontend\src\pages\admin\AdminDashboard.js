import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';

const AdminDashboard = () => {
  const [selectedNews, setSelectedNews] = useState([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const navigate = useNavigate();

  // Check authentication on component mount
  useEffect(() => {
    const token = localStorage.getItem('jwt');
    if (!token) {
      navigate('/admin/login');
    }
  }, [navigate]);

  // Sample data
  const newsData = [
    {
      id: 1,
      image: 'https://placehold.co/60x60?text=News1',
      title: 'Teknologi AI Mengubah Dunia Digital',
      category: 'Teknologi',
      author: 'Admin',
      status: 'published',
      views: 12500,
      date: '15 Jan 2024',
      time: '10:30'
    },
    {
      id: 2,
      image: 'https://placehold.co/60x60?text=News2',
      title: 'Ekonomi Indonesia Tumbuh Pesat',
      category: 'Ekonomi',
      author: 'Editor',
      status: 'draft',
      views: 0,
      date: '14 Jan 2024',
      time: '14:20'
    },
    {
      id: 3,
      image: 'https://placehold.co/60x60?text=News3',
      title: 'Olahraga Nasional Berprestasi',
      category: 'Olahraga',
      author: 'Reporter',
      status: 'published',
      views: 8900,
      date: '13 Jan 2024',
      time: '16:45'
    }
  ];

  const filteredNews = newsData.filter(news => {
    const matchesSearch = news.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         news.author.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = statusFilter === 'all' || news.status === statusFilter;
    return matchesSearch && matchesStatus;
  });

  const handleSelectAll = (e) => {
    if (e.target.checked) {
      setSelectedNews(filteredNews.map(news => news.id));
    } else {
      setSelectedNews([]);
    }
  };

  const handleSelectNews = (newsId) => {
    setSelectedNews(prev => 
      prev.includes(newsId) 
        ? prev.filter(id => id !== newsId)
        : [...prev, newsId]
    );
  };

  const handleDelete = (newsId) => {
    if (window.confirm('Apakah Anda yakin ingin menghapus berita ini?')) {
      alert('Berita ID ' + newsId + ' telah dihapus!');
    }
  };

  const handleBulkDelete = () => {
    if (selectedNews.length === 0) return;
    
    if (window.confirm(`Apakah Anda yakin ingin menghapus ${selectedNews.length} berita yang dipilih?`)) {
      alert(`${selectedNews.length} berita telah dihapus!`);
      setSelectedNews([]);
    }
  };

  return (
    <div className="p-6 bg-gray-50 min-h-screen">
      {/* Search and Filter */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 mb-6 p-4">
        <div className="flex flex-col lg:flex-row gap-4 items-center justify-between">
          <div className="flex flex-col sm:flex-row gap-4 flex-1">
            <div className="relative flex-1 max-w-md">
              <i className="fas fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
              <input
                type="text"
                placeholder="Cari berita, penulis..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
            
            <div className="relative">
              <i className="fas fa-filter absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
              <select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
                className="pl-10 pr-8 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent appearance-none bg-white"
              >
                <option value="all">Semua Status</option>
                <option value="published">Dipublikasi</option>
                <option value="draft">Draft</option>
              </select>
            </div>
          </div>
          
          {selectedNews.length > 0 && (
            <div className="flex gap-2">
              <button
                onClick={handleBulkDelete}
                className="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg transition-colors flex items-center gap-2"
              >
                <i className="fas fa-trash"></i>
                <span>Hapus Terpilih ({selectedNews.length})</span>
              </button>
            </div>
          )}
        </div>
      </div>

      {/* News Table */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        <div className="overflow-x-auto">
          <table className="w-full divide-y divide-gray-200" style={{minWidth: '600px'}}>
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  <input
                    type="checkbox"
                    checked={selectedNews.length === filteredNews.length && filteredNews.length > 0}
                    onChange={handleSelectAll}
                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">No</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Gambar</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Judul</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Kategori</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Penulis</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Views</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Tanggal</th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Aksi</th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {filteredNews.length === 0 ? (
                <tr>
                  <td colSpan="10" className="px-6 py-12 text-center text-gray-500">
                    <div className="flex flex-col items-center">
                      <i className="fas fa-search text-4xl text-gray-300 mb-4"></i>
                      <p className="text-lg font-medium">Tidak ada berita ditemukan</p>
                      <p className="text-sm">Coba ubah kata kunci pencarian atau filter</p>
                    </div>
                  </td>
                </tr>
              ) : (
                filteredNews.map((news, index) => (
                  <tr key={news.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <input
                        type="checkbox"
                        checked={selectedNews.includes(news.id)}
                        onChange={() => handleSelectNews(news.id)}
                        className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                      />
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 font-medium">{index + 1}</td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <img
                        src={news.image}
                        alt={news.title}
                        className="h-12 w-12 rounded-lg object-cover border border-gray-200"
                      />
                    </td>
                    <td className="px-6 py-4 text-sm font-medium text-gray-900">
                      <div className="max-w-xs truncate">{news.title}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-600">{news.category}</td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-600">{news.author}</td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                        news.status === 'published' 
                          ? 'bg-green-100 text-green-800' 
                          : 'bg-yellow-100 text-yellow-800'
                      }`}>
                        {news.status === 'published' ? 'Dipublikasi' : 'Draft'}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-600">{news.views.toLocaleString()}</td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      <div>{news.date}</div>
                      <div className="text-xs text-gray-400">{news.time}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <div className="flex justify-end gap-1">
                        <button
                          onClick={() => alert('Melihat detail berita ID: ' + news.id)}
                          className="text-blue-600 hover:text-blue-900 p-2 rounded-lg hover:bg-blue-50 transition-colors"
                          title="Lihat Detail"
                        >
                          <i className="fas fa-eye"></i>
                        </button>
                        <button
                          onClick={() => alert('Edit berita ID: ' + news.id)}
                          className="text-green-600 hover:text-green-900 p-2 rounded-lg hover:bg-green-50 transition-colors"
                          title="Edit"
                        >
                          <i className="fas fa-edit"></i>
                        </button>
                        <button
                          onClick={() => handleDelete(news.id)}
                          className="text-red-600 hover:text-red-900 p-2 rounded-lg hover:bg-red-50 transition-colors"
                          title="Hapus"
                        >
                          <i className="fas fa-trash"></i>
                        </button>
                      </div>
                    </td>
                  </tr>
                ))
              )}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};

export default AdminDashboard;
