-- Create settings table for website customization
-- Run this in your MySQL database

USE react_news;

-- Create settings table if not exists
CREATE TABLE IF NOT EXISTS `settings` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `website_name` varchar(255) DEFAULT 'React News Portal',
  `website_logo` varchar(500) DEFAULT '/logo192.png',
  `website_description` text DEFAULT 'Portal berita terkini dan terpercaya',
  `primary_color` varchar(7) DEFAULT '#3B82F6',
  `secondary_color` varchar(7) DEFAULT '#10B981',
  `accent_color` varchar(7) DEFAULT '#F59E0B',
  `footer_text` text DEFAULT '© 2024 React News Portal. All rights reserved.',
  `contact_email` varchar(255) DEFAULT '<EMAIL>',
  `social_facebook` varchar(255) DEFAULT '',
  `social_twitter` varchar(255) DEFAULT '',
  `social_instagram` varchar(255) DEFAULT '',
  `social_youtube` varchar(255) DEFAULT '',
  `meta_keywords` text DEFAULT 'berita, news, portal, react',
  `meta_description` text DEFAULT 'Portal berita terkini dengan teknologi React',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Insert default settings if table is empty
INSERT IGNORE INTO `settings` (
  `id`,
  `website_name`,
  `website_logo`,
  `website_description`,
  `primary_color`,
  `secondary_color`,
  `accent_color`,
  `footer_text`,
  `contact_email`,
  `social_facebook`,
  `social_twitter`,
  `social_instagram`,
  `social_youtube`,
  `meta_keywords`,
  `meta_description`
) VALUES (
  1,
  'React News Portal',
  '/logo192.png',
  'Portal berita terkini dan terpercaya dengan teknologi React modern',
  '#3B82F6',
  '#10B981',
  '#F59E0B',
  '© 2024 React News Portal. All rights reserved.',
  '<EMAIL>',
  'https://facebook.com/reactnews',
  'https://twitter.com/reactnews',
  'https://instagram.com/reactnews',
  'https://youtube.com/reactnews',
  'berita, news, portal, react, teknologi, informasi, terkini',
  'Portal berita terkini dengan teknologi React yang memberikan informasi akurat dan terpercaya'
);

-- Sample data for testing different website names and logos
INSERT IGNORE INTO `settings` (
  `id`,
  `website_name`,
  `website_logo`,
  `website_description`,
  `primary_color`,
  `secondary_color`,
  `accent_color`,
  `footer_text`,
  `contact_email`,
  `meta_keywords`,
  `meta_description`
) VALUES (
  2,
  'Berita Nusantara',
  'https://source.unsplash.com/100x100/?indonesia,flag',
  'Portal berita Indonesia terdepan',
  '#DC2626',
  '#059669',
  '#D97706',
  '© 2024 Berita Nusantara. Semua hak dilindungi.',
  '<EMAIL>',
  'berita, indonesia, nusantara, politik, ekonomi',
  'Portal berita Indonesia terdepan dengan liputan nasional dan internasional'
);

INSERT IGNORE INTO `settings` (
  `id`,
  `website_name`,
  `website_logo`,
  `website_description`,
  `primary_color`,
  `secondary_color`,
  `accent_color`,
  `footer_text`,
  `contact_email`,
  `meta_keywords`,
  `meta_description`
) VALUES (
  3,
  'Tech News ID',
  'https://source.unsplash.com/100x100/?technology,computer',
  'Portal berita teknologi terdepan di Indonesia',
  '#7C3AED',
  '#0891B2',
  '#EA580C',
  '© 2024 Tech News ID. Innovation never stops.',
  '<EMAIL>',
  'teknologi, gadget, startup, digital, innovation',
  'Portal berita teknologi Indonesia dengan liputan startup, gadget, dan inovasi digital'
);

-- Show current settings
SELECT 'Settings table created successfully!' as message;
SELECT * FROM settings ORDER BY id;

-- Instructions for admin to change settings
SELECT 'To change website name and logo:' as instruction;
SELECT '1. Login to admin dashboard' as step1;
SELECT '2. Go to Settings menu' as step2;
SELECT '3. Update Website Name and Logo URL' as step3;
SELECT '4. Save changes' as step4;
SELECT '5. Refresh landing page to see changes' as step5;
