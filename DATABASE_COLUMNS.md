# Database Columns untuk React News Portal

## Tabel yang <PERSON><PERSON><PERSON><PERSON>

### 1. Tabel `admin` (User Management)
```sql
CREATE TABLE admin (
  id INT AUTO_INCREMENT PRIMARY KEY,
  username VARCHAR(50) NOT NULL UNIQUE,
  password VARCHAR(255) NOT NULL,
  email VARCHAR(100) NULL,
  full_name VA<PERSON>HAR(100) NULL,
  role ENUM('admin', 'editor', 'author') DEFAULT 'admin',
  avatar VARCHAR(255) NULL,
  is_active BOOLEAN DEFAULT TRUE,
  last_login DATETIME NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

### 2. Tabel `categories` (<PERSON>gori Berita)
```sql
CREATE TABLE categories (
  id INT AUTO_INCREMENT PRIMARY KEY,
  name VARCHAR(100) NOT NULL,
  slug VARCHAR(100) NOT NULL UNIQUE,
  description TEXT NULL,
  color VARCHAR(7) DEFAULT '#3B82F6',
  is_active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

### 3. Tabel `posts` (Berita) - KOLOM UTAMA
```sql
CREATE TABLE posts (
  id INT AUTO_INCREMENT PRIMARY KEY,
  title VARCHAR(255) NOT NULL,
  slug VARCHAR(255) NOT NULL UNIQUE,
  description TEXT NOT NULL,
  content LONGTEXT NOT NULL,
  excerpt TEXT NULL,
  image VARCHAR(255) NULL,
  image_alt VARCHAR(255) NULL,
  category_id INT NOT NULL DEFAULT 1,
  status ENUM('draft', 'published', 'archived') DEFAULT 'draft',
  featured BOOLEAN DEFAULT FALSE,
  meta_title VARCHAR(255) NULL,
  meta_description TEXT NULL,
  tags TEXT NULL,
  share INT DEFAULT 0,              -- Kolom untuk tracking share
  views INT DEFAULT 0,              -- Kolom untuk tracking views
  likes INT DEFAULT 0,              -- Kolom untuk tracking likes
  comments_count INT DEFAULT 0,     -- Kolom untuk jumlah komentar
  reading_time INT DEFAULT 0,       -- Estimasi waktu baca (menit)
  published_at DATETIME NULL,       -- Tanggal publikasi
  date DATETIME DEFAULT CURRENT_TIMESTAMP,  -- Backward compatibility
  user_id INT NULL,                 -- ID admin yang membuat
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (user_id) REFERENCES admin(id) ON DELETE SET NULL,
  FOREIGN KEY (category_id) REFERENCES categories(id) ON DELETE RESTRICT
);
```

### 4. Tabel `settings` (Pengaturan Website)
```sql
CREATE TABLE settings (
  id INT AUTO_INCREMENT PRIMARY KEY,
  setting_key VARCHAR(100) NOT NULL UNIQUE,
  setting_value LONGTEXT NOT NULL,
  setting_type ENUM('text', 'number', 'boolean', 'json', 'file') DEFAULT 'text',
  category VARCHAR(50) DEFAULT 'general',
  description TEXT NULL,
  is_public BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

### 5. Tabel `saved` (Berita yang Disimpan)
```sql
CREATE TABLE saved (
  id INT AUTO_INCREMENT PRIMARY KEY,
  post_id INT NOT NULL,
  user_id INT NULL,
  ip_address VARCHAR(45) NULL,
  saved_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (post_id) REFERENCES posts(id) ON DELETE CASCADE,
  FOREIGN KEY (user_id) REFERENCES admin(id) ON DELETE SET NULL,
  UNIQUE KEY unique_save (post_id, user_id, ip_address)
);
```

### 6. Tabel `page_views` (Tracking Views)
```sql
CREATE TABLE page_views (
  id INT AUTO_INCREMENT PRIMARY KEY,
  post_id INT NULL,
  page_type ENUM('post', 'category', 'home', 'search', 'other') DEFAULT 'other',
  page_url VARCHAR(500) NOT NULL,
  ip_address VARCHAR(45) NULL,
  user_agent TEXT NULL,
  referer VARCHAR(500) NULL,
  country VARCHAR(2) NULL,
  city VARCHAR(100) NULL,
  device_type ENUM('desktop', 'mobile', 'tablet') NULL,
  browser VARCHAR(50) NULL,
  os VARCHAR(50) NULL,
  viewed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (post_id) REFERENCES posts(id) ON DELETE SET NULL
);
```

## Kolom Penting untuk Dashboard Admin

### Kolom Statistik:
- `posts.views` - Jumlah views per berita
- `posts.share` - Jumlah share per berita  
- `posts.likes` - Jumlah likes per berita
- `posts.comments_count` - Jumlah komentar per berita
- `posts.status` - Status publikasi (draft/published/archived)
- `posts.featured` - Berita unggulan
- `posts.reading_time` - Estimasi waktu baca

### Kolom Relasi:
- `posts.category_id` - Relasi ke tabel categories
- `posts.user_id` - Relasi ke tabel admin (author)
- `categories.name` - Nama kategori
- `categories.color` - Warna kategori untuk UI
- `admin.username` - Nama author

### Kolom Metadata:
- `posts.slug` - URL-friendly identifier
- `posts.meta_title` - SEO title
- `posts.meta_description` - SEO description
- `posts.tags` - Tags berita (comma separated)
- `posts.published_at` - Tanggal publikasi
- `posts.excerpt` - Ringkasan berita

## API Endpoints yang Tersedia

### Admin Dashboard:
- `GET api.php?action=get_news` - Ambil semua berita
- `GET api.php?action=get_stats` - Statistik dashboard
- `GET api.php?action=get_categories` - Daftar kategori
- `POST api.php` dengan `action=add_news` - Tambah berita
- `POST api.php` dengan `action=update_news` - Update berita
- `POST api.php` dengan `action=delete_news` - Hapus berita

### User Frontend:
- `GET api.php?action=get_saved_posts` - Berita yang disimpan
- `POST api.php` dengan `action=save_post` - Simpan berita
- `POST api.php` dengan `action=unsave_post` - Hapus dari simpan
- `POST api.php` dengan `action=update_post_share` - Update share count
- `POST api.php` dengan `action=update_post_views` - Update view count

## Cara Setup Database

1. Jalankan file `backend/database.sql` atau `frontend/src/pages/admin/setup_database.sql`
2. Database akan otomatis dibuat dengan nama `react_news`
3. Semua tabel dan data default akan dibuat
4. Admin default: username `admin`, password `admin123`

## Integrasi dengan Landing Page

Landing page sudah diupdate untuk:
- Menggunakan kategori dari database
- Menampilkan view count dan share count
- Integrasi bookmark dengan database
- Tracking share dan view otomatis
- Menampilkan kategori dengan warna sesuai database
