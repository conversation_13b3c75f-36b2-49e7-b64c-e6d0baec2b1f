-- Database setup for Admin Dashboard
-- Create database
CREATE DATABASE IF NOT EXISTS admin_dashboard;
USE admin_dashboard;

-- Create news table
CREATE TABLE IF NOT EXISTS news (
    id INT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    content TEXT NOT NULL,
    image VARCHAR(255) NULL,
    status ENUM('draft', 'published') DEFAULT 'draft',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Add image column if it doesn't exist (for existing installations)
ALTER TABLE news ADD COLUMN image VARCHAR(255) NULL;

-- Create settings table
CREATE TABLE IF NOT EXISTS settings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    setting_key VARCHAR(100) UNIQUE NOT NULL,
    setting_value TEXT NOT NULL,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Insert default settings
INSERT IGNORE INTO settings (setting_key, setting_value) VALUES
('website_name', 'My Website'),
('website_logo', 'assets/logo.png'),
('primary_color', '#3B82F6'),
('secondary_color', '#1E40AF'),
('accent_color', '#F59E0B');

-- Insert sample news data
INSERT IGNORE INTO news (title, content, status) VALUES
('Selamat Datang di Admin Dashboard', 'Ini adalah berita pertama di admin dashboard yang baru. Dashboard ini dilengkapi dengan fitur CRUD untuk manajemen berita dan pengaturan website yang lengkap.', 'published'),
('Fitur Baru: Pengaturan Warna', 'Sekarang Anda dapat mengubah warna tema website melalui panel pengaturan. Pilih warna primary, secondary, dan accent sesuai dengan brand Anda.', 'published'),
('Tutorial Upload Logo', 'Panduan lengkap cara mengupload dan mengganti logo website melalui panel admin. Logo akan otomatis ter-resize dan ter-optimasi.', 'draft');

-- Create users table (for future authentication)
CREATE TABLE IF NOT EXISTS users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    role ENUM('admin', 'editor') DEFAULT 'editor',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Insert default admin user (password: admin123)
INSERT IGNORE INTO users (username, email, password, role) VALUES
('admin', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'admin');

-- Create categories table for news categorization
CREATE TABLE IF NOT EXISTS categories (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    slug VARCHAR(100) UNIQUE NOT NULL,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Insert default categories
INSERT IGNORE INTO categories (name, slug, description) VALUES
('Umum', 'umum', 'Kategori untuk berita umum'),
('Teknologi', 'teknologi', 'Berita seputar teknologi dan inovasi'),
('Bisnis', 'bisnis', 'Informasi bisnis dan ekonomi');

-- Add category_id to news table
ALTER TABLE news ADD COLUMN category_id INT DEFAULT 1;
ALTER TABLE news ADD FOREIGN KEY (category_id) REFERENCES categories(id);

-- Create page_views table for analytics
CREATE TABLE IF NOT EXISTS page_views (
    id INT AUTO_INCREMENT PRIMARY KEY,
    page VARCHAR(255) NOT NULL,
    ip_address VARCHAR(45),
    user_agent TEXT,
    viewed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create website_stats table for dashboard statistics
CREATE TABLE IF NOT EXISTS website_stats (
    id INT AUTO_INCREMENT PRIMARY KEY,
    stat_key VARCHAR(100) UNIQUE NOT NULL,
    stat_value INT DEFAULT 0,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Insert default stats
INSERT IGNORE INTO website_stats (stat_key, stat_value) VALUES
('total_users', 1234),
('page_views', 12345),
('revenue', 12345);
