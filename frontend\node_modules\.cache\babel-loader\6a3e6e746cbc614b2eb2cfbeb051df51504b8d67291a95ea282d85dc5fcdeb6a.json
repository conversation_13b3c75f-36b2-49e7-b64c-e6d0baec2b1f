{"ast": null, "code": "var _jsxFileName = \"C:\\\\laragon\\\\www\\\\react-news\\\\frontend\\\\src\\\\pages\\\\user\\\\data-news.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useParams, useNavigate } from 'react-router-dom';\nimport Navbar from './components/Navbar';\nimport Box from '@mui/material/Box';\nimport Typography from '@mui/material/Typography';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst DataNews = () => {\n  _s();\n  const {\n    id\n  } = useParams();\n  const navigate = useNavigate();\n  const [news, setNews] = useState(null);\n  const [relatedNews, setRelatedNews] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [bottomNav, setBottomNav] = useState(0);\n  useEffect(() => {\n    if (id) {\n      fetchNewsDetail(id);\n    }\n  }, [id]);\n  const fetchNewsDetail = async newsId => {\n    try {\n      setLoading(true);\n      const response = await fetch(`http://localhost/react-news/backend/api.php?action=get_news_by_id&id=${newsId}`);\n      if (!response.ok) {\n        throw new Error(`HTTP error! status: ${response.status}`);\n      }\n      const data = await response.json();\n      if (data.success) {\n        setNews(data.data);\n        // Fetch related news\n        fetchRelatedNews(data.data.category_id, newsId);\n      } else {\n        setError('Berita tidak ditemukan');\n      }\n    } catch (error) {\n      console.error('Error fetching news:', error);\n\n      // Fallback to dummy data for testing\n      const dummyNews = {\n        id: newsId,\n        title: 'Contoh Berita - ' + newsId,\n        content: 'Ini adalah contoh konten berita yang akan ditampilkan ketika API tidak tersedia. Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.\\n\\nUt enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur.\\n\\nExcepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.',\n        image: 'https://via.placeholder.com/800x400/3B82F6/FFFFFF?text=Berita+' + newsId,\n        category_name: 'Teknologi',\n        category_id: 1,\n        created_at: new Date().toISOString(),\n        date: new Date().toISOString(),\n        views: Math.floor(Math.random() * 1000) + 100\n      };\n      setNews(dummyNews);\n      fetchRelatedNews(1, newsId); // Use dummy category ID\n    } finally {\n      setLoading(false);\n    }\n  };\n  const fetchRelatedNews = async (categoryId, currentNewsId) => {\n    try {\n      const response = await fetch(`http://localhost/react-news/backend/api.php?action=get_news&category=${categoryId}&limit=4`);\n      if (!response.ok) {\n        throw new Error(`HTTP error! status: ${response.status}`);\n      }\n      const data = await response.json();\n      if (data.success) {\n        // Filter out current news and limit to 3\n        const filtered = data.data.filter(item => item.id !== parseInt(currentNewsId)).slice(0, 3);\n        setRelatedNews(filtered);\n      }\n    } catch (error) {\n      console.error('Error fetching related news:', error);\n\n      // Fallback to dummy related news\n      const dummyRelatedNews = [{\n        id: parseInt(currentNewsId) + 1,\n        title: 'Berita Terkait 1 - Teknologi Terbaru',\n        image: 'https://via.placeholder.com/150x150/10B981/FFFFFF?text=Tech+1',\n        created_at: new Date(Date.now() - 86400000).toISOString(),\n        // 1 day ago\n        date: new Date(Date.now() - 86400000).toISOString()\n      }, {\n        id: parseInt(currentNewsId) + 2,\n        title: 'Berita Terkait 2 - Inovasi Digital',\n        image: 'https://via.placeholder.com/150x150/F59E0B/FFFFFF?text=Tech+2',\n        created_at: new Date(Date.now() - 172800000).toISOString(),\n        // 2 days ago\n        date: new Date(Date.now() - 172800000).toISOString()\n      }, {\n        id: parseInt(currentNewsId) + 3,\n        title: 'Berita Terkait 3 - Perkembangan AI',\n        image: 'https://via.placeholder.com/150x150/EF4444/FFFFFF?text=Tech+3',\n        created_at: new Date(Date.now() - 259200000).toISOString(),\n        // 3 days ago\n        date: new Date(Date.now() - 259200000).toISOString()\n      }];\n      setRelatedNews(dummyRelatedNews);\n    }\n  };\n  const getImageUrl = imagePath => {\n    if (!imagePath) return 'https://via.placeholder.com/800x400/E5E7EB/6B7280?text=No+Image';\n    if (imagePath.startsWith('http')) return imagePath;\n    if (imagePath.startsWith('/')) return `http://localhost:3000${imagePath}`;\n    return `http://localhost/react-news/backend/uploads/${imagePath}`;\n  };\n  const formatDate = dateString => {\n    const date = new Date(dateString);\n    return date.toLocaleDateString('id-ID', {\n      weekday: 'long',\n      year: 'numeric',\n      month: 'long',\n      day: 'numeric'\n    });\n  };\n  const formatTime = dateString => {\n    const date = new Date(dateString);\n    return date.toLocaleTimeString('id-ID', {\n      hour: '2-digit',\n      minute: '2-digit'\n    });\n  };\n  const handleRelatedNewsClick = newsId => {\n    navigate(`/data-news/${newsId}`);\n  };\n  const handleBackToHome = () => {\n    navigate('/');\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gray-50 flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 147,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600\",\n          children: \"Memuat berita...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 148,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 146,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 145,\n      columnNumber: 13\n    }, this);\n  }\n  if (error) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gray-50 flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-red-100 border border-red-400 text-red-700 px-6 py-4 rounded-lg mb-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"fas fa-exclamation-triangle mr-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 159,\n            columnNumber: 25\n          }, this), error]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 158,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: handleBackToHome,\n          className: \"bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors\",\n          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"fas fa-arrow-left mr-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 166,\n            columnNumber: 25\n          }, this), \"Kembali ke Beranda\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 162,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 157,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 156,\n      columnNumber: 13\n    }, this);\n  }\n  if (!news) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gray-50 flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600\",\n          children: \"Berita tidak ditemukan\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 178,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: handleBackToHome,\n          className: \"mt-4 bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors\",\n          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"fas fa-arrow-left mr-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 183,\n            columnNumber: 25\n          }, this), \"Kembali ke Beranda\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 179,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 177,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 176,\n      columnNumber: 13\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gray-50\",\n    children: [/*#__PURE__*/_jsxDEV(Navbar, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 194,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white shadow-sm border-b\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-6xl mx-auto px-4 py-4\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-2 text-sm text-gray-600\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: handleBackToHome,\n            className: \"hover:text-blue-600 transition-colors\",\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"fas fa-home mr-1\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 204,\n              columnNumber: 29\n            }, this), \"Beranda\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 200,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"fas fa-chevron-right text-xs\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 207,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-gray-800 font-medium\",\n            children: \"Detail Berita\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 208,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 199,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 198,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 197,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-6xl mx-auto px-4 py-8\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 lg:grid-cols-3 gap-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"lg:col-span-2\",\n          children: /*#__PURE__*/_jsxDEV(\"article\", {\n            className: \"bg-white rounded-lg shadow-md overflow-hidden\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"relative\",\n              children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                src: getImageUrl(news.image),\n                alt: news.title,\n                className: \"w-full h-64 md:h-80 object-cover\",\n                onError: e => {\n                  e.target.src = 'https://via.placeholder.com/800x400/E5E7EB/6B7280?text=No+Image';\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 221,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"absolute top-4 left-4\",\n                children: /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"bg-blue-600 text-white px-3 py-1 rounded-full text-sm font-medium\",\n                  children: news.category_name || 'Berita'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 230,\n                  columnNumber: 37\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 229,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 220,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-6 md:p-8\",\n              children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n                className: \"text-2xl md:text-3xl font-bold text-gray-900 mb-4 leading-tight\",\n                children: news.title\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 238,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex flex-wrap items-center gap-4 text-sm text-gray-600 mb-6 pb-6 border-b\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                    className: \"fas fa-calendar-alt mr-2 text-blue-600\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 245,\n                    columnNumber: 41\n                  }, this), formatDate(news.created_at || news.date)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 244,\n                  columnNumber: 37\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                    className: \"fas fa-clock mr-2 text-blue-600\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 249,\n                    columnNumber: 41\n                  }, this), formatTime(news.created_at || news.date)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 248,\n                  columnNumber: 37\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                    className: \"fas fa-eye mr-2 text-blue-600\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 253,\n                    columnNumber: 41\n                  }, this), news.views || 0, \" views\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 252,\n                  columnNumber: 37\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 243,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"prose prose-lg max-w-none\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-gray-700 leading-relaxed whitespace-pre-line\",\n                  dangerouslySetInnerHTML: {\n                    __html: news.content.replace(/\\n/g, '<br>')\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 260,\n                  columnNumber: 37\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 259,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mt-8 pt-6 border-t\",\n                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"text-lg font-semibold text-gray-900 mb-4\",\n                  children: \"Bagikan Artikel\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 270,\n                  columnNumber: 37\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex space-x-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors\",\n                    children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                      className: \"fab fa-facebook-f mr-2\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 273,\n                      columnNumber: 45\n                    }, this), \"Facebook\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 272,\n                    columnNumber: 41\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"bg-blue-400 text-white px-4 py-2 rounded-lg hover:bg-blue-500 transition-colors\",\n                    children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                      className: \"fab fa-twitter mr-2\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 277,\n                      columnNumber: 45\n                    }, this), \"Twitter\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 276,\n                    columnNumber: 41\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors\",\n                    children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                      className: \"fab fa-whatsapp mr-2\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 281,\n                      columnNumber: 45\n                    }, this), \"WhatsApp\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 280,\n                    columnNumber: 41\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 271,\n                  columnNumber: 37\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 269,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 237,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 218,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 217,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"lg:col-span-1\",\n          children: [relatedNews.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white rounded-lg shadow-md p-6 mb-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-lg font-semibold text-gray-900 mb-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"fas fa-newspaper mr-2 text-blue-600\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 296,\n                columnNumber: 37\n              }, this), \"Berita Terkait\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 295,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-4\",\n              children: relatedNews.map(item => /*#__PURE__*/_jsxDEV(\"div\", {\n                onClick: () => handleRelatedNewsClick(item.id),\n                className: \"flex cursor-pointer hover:bg-gray-50 p-2 rounded-lg transition-colors\",\n                children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                  src: getImageUrl(item.image),\n                  alt: item.title,\n                  className: \"w-16 h-16 object-cover rounded-lg mr-3 flex-shrink-0\",\n                  onError: e => {\n                    e.target.src = 'http://localhost:3000/placeholder-news.jpg';\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 306,\n                  columnNumber: 45\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex-1 min-w-0\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                    className: \"text-sm font-medium text-gray-900 line-clamp-2 mb-1\",\n                    children: item.title\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 315,\n                    columnNumber: 49\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-xs text-gray-600\",\n                    children: formatDate(item.created_at || item.date)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 318,\n                    columnNumber: 49\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 314,\n                  columnNumber: 45\n                }, this)]\n              }, item.id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 301,\n                columnNumber: 41\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 299,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 294,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white rounded-lg shadow-md p-6\",\n            children: /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: handleBackToHome,\n              className: \"w-full bg-blue-600 text-white py-3 px-4 rounded-lg hover:bg-blue-700 transition-colors font-medium\",\n              children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"fas fa-arrow-left mr-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 334,\n                columnNumber: 33\n              }, this), \"Kembali ke Beranda\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 330,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 329,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 291,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 215,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 214,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        position: 'fixed',\n        left: 0,\n        right: 0,\n        bottom: 0,\n        zIndex: 1300,\n        display: 'block',\n        backgroundColor: 'white',\n        borderTop: '1px solid #e0e0e0',\n        boxShadow: '0 -2px 10px rgba(0,0,0,0.1)'\n      },\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          justifyContent: 'space-around',\n          alignItems: 'center',\n          height: 64,\n          px: 1\n        },\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          onClick: () => navigate('/'),\n          className: \"bottom-nav-item active\",\n          style: {\n            cursor: 'pointer'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"fas fa-home bottom-nav-icon text-blue-600\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 366,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"caption\",\n            className: \"bottom-nav-label\",\n            sx: {\n              color: 'primary.main'\n            },\n            children: \"Home\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 367,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 361,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          onClick: () => navigate('/'),\n          className: \"bottom-nav-item\",\n          style: {\n            cursor: 'pointer'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"fas fa-search bottom-nav-icon text-gray-500\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 377,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"caption\",\n            className: \"bottom-nav-label\",\n            sx: {\n              color: 'text.secondary'\n            },\n            children: \"Cari\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 378,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 372,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          onClick: () => navigate('/saved'),\n          className: \"bottom-nav-item\",\n          style: {\n            cursor: 'pointer'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"fas fa-bookmark bottom-nav-icon text-gray-500\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 388,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"caption\",\n            className: \"bottom-nav-label\",\n            sx: {\n              color: 'text.secondary'\n            },\n            children: \"Simpan\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 389,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 383,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 354,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 343,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 192,\n    columnNumber: 9\n  }, this);\n};\n_s(DataNews, \"vbFYenLKApjRhtfs+0SH/a4Jyq0=\", false, function () {\n  return [useParams, useNavigate];\n});\n_c = DataNews;\nexport default DataNews;\nvar _c;\n$RefreshReg$(_c, \"DataNews\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useParams", "useNavigate", "<PERSON><PERSON><PERSON>", "Box", "Typography", "jsxDEV", "_jsxDEV", "DataNews", "_s", "id", "navigate", "news", "setNews", "relatedNews", "setRelatedNews", "loading", "setLoading", "error", "setError", "bottomNav", "setBottomNav", "fetchNewsDetail", "newsId", "response", "fetch", "ok", "Error", "status", "data", "json", "success", "fetchRelatedNews", "category_id", "console", "dummyNews", "title", "content", "image", "category_name", "created_at", "Date", "toISOString", "date", "views", "Math", "floor", "random", "categoryId", "currentNewsId", "filtered", "filter", "item", "parseInt", "slice", "dummy<PERSON>elatedNews", "now", "getImageUrl", "imagePath", "startsWith", "formatDate", "dateString", "toLocaleDateString", "weekday", "year", "month", "day", "formatTime", "toLocaleTimeString", "hour", "minute", "handleRelatedNewsClick", "handleBackToHome", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "src", "alt", "onError", "e", "target", "dangerouslySetInnerHTML", "__html", "replace", "length", "map", "sx", "position", "left", "right", "bottom", "zIndex", "display", "backgroundColor", "borderTop", "boxShadow", "justifyContent", "alignItems", "height", "px", "style", "cursor", "variant", "color", "_c", "$RefreshReg$"], "sources": ["C:/laragon/www/react-news/frontend/src/pages/user/data-news.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useParams, useNavigate } from 'react-router-dom';\nimport Navbar from './components/Navbar';\nimport Box from '@mui/material/Box';\nimport Typography from '@mui/material/Typography';\n\nconst DataNews = () => {\n    const { id } = useParams();\n    const navigate = useNavigate();\n    const [news, setNews] = useState(null);\n    const [relatedNews, setRelatedNews] = useState([]);\n    const [loading, setLoading] = useState(true);\n    const [error, setError] = useState(null);\n    const [bottomNav, setBottomNav] = useState(0);\n\n    useEffect(() => {\n        if (id) {\n            fetchNewsDetail(id);\n        }\n    }, [id]);\n\n    const fetchNewsDetail = async (newsId) => {\n        try {\n            setLoading(true);\n            const response = await fetch(`http://localhost/react-news/backend/api.php?action=get_news_by_id&id=${newsId}`);\n\n            if (!response.ok) {\n                throw new Error(`HTTP error! status: ${response.status}`);\n            }\n\n            const data = await response.json();\n\n            if (data.success) {\n                setNews(data.data);\n                // Fetch related news\n                fetchRelatedNews(data.data.category_id, newsId);\n            } else {\n                setError('Berita tidak ditemukan');\n            }\n        } catch (error) {\n            console.error('Error fetching news:', error);\n\n            // Fallback to dummy data for testing\n            const dummyNews = {\n                id: newsId,\n                title: 'Contoh Berita - ' + newsId,\n                content: 'Ini adalah contoh konten berita yang akan ditampilkan ketika API tidak tersedia. Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.\\n\\nUt enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur.\\n\\nExcepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.',\n                image: 'https://via.placeholder.com/800x400/3B82F6/FFFFFF?text=Berita+' + newsId,\n                category_name: 'Teknologi',\n                category_id: 1,\n                created_at: new Date().toISOString(),\n                date: new Date().toISOString(),\n                views: Math.floor(Math.random() * 1000) + 100\n            };\n\n            setNews(dummyNews);\n            fetchRelatedNews(1, newsId); // Use dummy category ID\n        } finally {\n            setLoading(false);\n        }\n    };\n\n    const fetchRelatedNews = async (categoryId, currentNewsId) => {\n        try {\n            const response = await fetch(`http://localhost/react-news/backend/api.php?action=get_news&category=${categoryId}&limit=4`);\n\n            if (!response.ok) {\n                throw new Error(`HTTP error! status: ${response.status}`);\n            }\n\n            const data = await response.json();\n\n            if (data.success) {\n                // Filter out current news and limit to 3\n                const filtered = data.data.filter(item => item.id !== parseInt(currentNewsId)).slice(0, 3);\n                setRelatedNews(filtered);\n            }\n        } catch (error) {\n            console.error('Error fetching related news:', error);\n\n            // Fallback to dummy related news\n            const dummyRelatedNews = [\n                {\n                    id: parseInt(currentNewsId) + 1,\n                    title: 'Berita Terkait 1 - Teknologi Terbaru',\n                    image: 'https://via.placeholder.com/150x150/10B981/FFFFFF?text=Tech+1',\n                    created_at: new Date(Date.now() - 86400000).toISOString(), // 1 day ago\n                    date: new Date(Date.now() - 86400000).toISOString()\n                },\n                {\n                    id: parseInt(currentNewsId) + 2,\n                    title: 'Berita Terkait 2 - Inovasi Digital',\n                    image: 'https://via.placeholder.com/150x150/F59E0B/FFFFFF?text=Tech+2',\n                    created_at: new Date(Date.now() - 172800000).toISOString(), // 2 days ago\n                    date: new Date(Date.now() - 172800000).toISOString()\n                },\n                {\n                    id: parseInt(currentNewsId) + 3,\n                    title: 'Berita Terkait 3 - Perkembangan AI',\n                    image: 'https://via.placeholder.com/150x150/EF4444/FFFFFF?text=Tech+3',\n                    created_at: new Date(Date.now() - 259200000).toISOString(), // 3 days ago\n                    date: new Date(Date.now() - 259200000).toISOString()\n                }\n            ];\n\n            setRelatedNews(dummyRelatedNews);\n        }\n    };\n\n    const getImageUrl = (imagePath) => {\n        if (!imagePath) return 'https://via.placeholder.com/800x400/E5E7EB/6B7280?text=No+Image';\n        if (imagePath.startsWith('http')) return imagePath;\n        if (imagePath.startsWith('/')) return `http://localhost:3000${imagePath}`;\n        return `http://localhost/react-news/backend/uploads/${imagePath}`;\n    };\n\n    const formatDate = (dateString) => {\n        const date = new Date(dateString);\n        return date.toLocaleDateString('id-ID', {\n            weekday: 'long',\n            year: 'numeric',\n            month: 'long',\n            day: 'numeric'\n        });\n    };\n\n    const formatTime = (dateString) => {\n        const date = new Date(dateString);\n        return date.toLocaleTimeString('id-ID', {\n            hour: '2-digit',\n            minute: '2-digit'\n        });\n    };\n\n    const handleRelatedNewsClick = (newsId) => {\n        navigate(`/data-news/${newsId}`);\n    };\n\n    const handleBackToHome = () => {\n        navigate('/');\n    };\n\n    if (loading) {\n        return (\n            <div className=\"min-h-screen bg-gray-50 flex items-center justify-center\">\n                <div className=\"text-center\">\n                    <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4\"></div>\n                    <p className=\"text-gray-600\">Memuat berita...</p>\n                </div>\n            </div>\n        );\n    }\n\n    if (error) {\n        return (\n            <div className=\"min-h-screen bg-gray-50 flex items-center justify-center\">\n                <div className=\"text-center\">\n                    <div className=\"bg-red-100 border border-red-400 text-red-700 px-6 py-4 rounded-lg mb-4\">\n                        <i className=\"fas fa-exclamation-triangle mr-2\"></i>\n                        {error}\n                    </div>\n                    <button \n                        onClick={handleBackToHome}\n                        className=\"bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors\"\n                    >\n                        <i className=\"fas fa-arrow-left mr-2\"></i>\n                        Kembali ke Beranda\n                    </button>\n                </div>\n            </div>\n        );\n    }\n\n    if (!news) {\n        return (\n            <div className=\"min-h-screen bg-gray-50 flex items-center justify-center\">\n                <div className=\"text-center\">\n                    <p className=\"text-gray-600\">Berita tidak ditemukan</p>\n                    <button \n                        onClick={handleBackToHome}\n                        className=\"mt-4 bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors\"\n                    >\n                        <i className=\"fas fa-arrow-left mr-2\"></i>\n                        Kembali ke Beranda\n                    </button>\n                </div>\n            </div>\n        );\n    }\n\n    return (\n        <div className=\"min-h-screen bg-gray-50\">\n            {/* Navbar */}\n            <Navbar />\n\n            {/* Header/Breadcrumb */}\n            <div className=\"bg-white shadow-sm border-b\">\n                <div className=\"max-w-6xl mx-auto px-4 py-4\">\n                    <div className=\"flex items-center space-x-2 text-sm text-gray-600\">\n                        <button \n                            onClick={handleBackToHome}\n                            className=\"hover:text-blue-600 transition-colors\"\n                        >\n                            <i className=\"fas fa-home mr-1\"></i>\n                            Beranda\n                        </button>\n                        <i className=\"fas fa-chevron-right text-xs\"></i>\n                        <span className=\"text-gray-800 font-medium\">Detail Berita</span>\n                    </div>\n                </div>\n            </div>\n\n            {/* Main Content */}\n            <div className=\"max-w-6xl mx-auto px-4 py-8\">\n                <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-8\">\n                    {/* Main Article */}\n                    <div className=\"lg:col-span-2\">\n                        <article className=\"bg-white rounded-lg shadow-md overflow-hidden\">\n                            {/* Article Header */}\n                            <div className=\"relative\">\n                                <img \n                                    src={getImageUrl(news.image)} \n                                    alt={news.title}\n                                    className=\"w-full h-64 md:h-80 object-cover\"\n                                    onError={(e) => {\n                                        e.target.src = 'https://via.placeholder.com/800x400/E5E7EB/6B7280?text=No+Image';\n                                    }}\n                                />\n                                <div className=\"absolute top-4 left-4\">\n                                    <span className=\"bg-blue-600 text-white px-3 py-1 rounded-full text-sm font-medium\">\n                                        {news.category_name || 'Berita'}\n                                    </span>\n                                </div>\n                            </div>\n\n                            {/* Article Content */}\n                            <div className=\"p-6 md:p-8\">\n                                <h1 className=\"text-2xl md:text-3xl font-bold text-gray-900 mb-4 leading-tight\">\n                                    {news.title}\n                                </h1>\n\n                                {/* Meta Information */}\n                                <div className=\"flex flex-wrap items-center gap-4 text-sm text-gray-600 mb-6 pb-6 border-b\">\n                                    <div className=\"flex items-center\">\n                                        <i className=\"fas fa-calendar-alt mr-2 text-blue-600\"></i>\n                                        {formatDate(news.created_at || news.date)}\n                                    </div>\n                                    <div className=\"flex items-center\">\n                                        <i className=\"fas fa-clock mr-2 text-blue-600\"></i>\n                                        {formatTime(news.created_at || news.date)}\n                                    </div>\n                                    <div className=\"flex items-center\">\n                                        <i className=\"fas fa-eye mr-2 text-blue-600\"></i>\n                                        {news.views || 0} views\n                                    </div>\n                                </div>\n\n                                {/* Article Body */}\n                                <div className=\"prose prose-lg max-w-none\">\n                                    <div \n                                        className=\"text-gray-700 leading-relaxed whitespace-pre-line\"\n                                        dangerouslySetInnerHTML={{ \n                                            __html: news.content.replace(/\\n/g, '<br>') \n                                        }}\n                                    />\n                                </div>\n\n                                {/* Share Buttons */}\n                                <div className=\"mt-8 pt-6 border-t\">\n                                    <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">Bagikan Artikel</h3>\n                                    <div className=\"flex space-x-3\">\n                                        <button className=\"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors\">\n                                            <i className=\"fab fa-facebook-f mr-2\"></i>\n                                            Facebook\n                                        </button>\n                                        <button className=\"bg-blue-400 text-white px-4 py-2 rounded-lg hover:bg-blue-500 transition-colors\">\n                                            <i className=\"fab fa-twitter mr-2\"></i>\n                                            Twitter\n                                        </button>\n                                        <button className=\"bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors\">\n                                            <i className=\"fab fa-whatsapp mr-2\"></i>\n                                            WhatsApp\n                                        </button>\n                                    </div>\n                                </div>\n                            </div>\n                        </article>\n                    </div>\n\n                    {/* Sidebar */}\n                    <div className=\"lg:col-span-1\">\n                        {/* Related News */}\n                        {relatedNews.length > 0 && (\n                            <div className=\"bg-white rounded-lg shadow-md p-6 mb-6\">\n                                <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">\n                                    <i className=\"fas fa-newspaper mr-2 text-blue-600\"></i>\n                                    Berita Terkait\n                                </h3>\n                                <div className=\"space-y-4\">\n                                    {relatedNews.map((item) => (\n                                        <div \n                                            key={item.id}\n                                            onClick={() => handleRelatedNewsClick(item.id)}\n                                            className=\"flex cursor-pointer hover:bg-gray-50 p-2 rounded-lg transition-colors\"\n                                        >\n                                            <img \n                                                src={getImageUrl(item.image)} \n                                                alt={item.title}\n                                                className=\"w-16 h-16 object-cover rounded-lg mr-3 flex-shrink-0\"\n                                                onError={(e) => {\n                                                    e.target.src = 'http://localhost:3000/placeholder-news.jpg';\n                                                }}\n                                            />\n                                            <div className=\"flex-1 min-w-0\">\n                                                <h4 className=\"text-sm font-medium text-gray-900 line-clamp-2 mb-1\">\n                                                    {item.title}\n                                                </h4>\n                                                <p className=\"text-xs text-gray-600\">\n                                                    {formatDate(item.created_at || item.date)}\n                                                </p>\n                                            </div>\n                                        </div>\n                                    ))}\n                                </div>\n                            </div>\n                        )}\n\n                        {/* Back to Home Button */}\n                        <div className=\"bg-white rounded-lg shadow-md p-6\">\n                            <button \n                                onClick={handleBackToHome}\n                                className=\"w-full bg-blue-600 text-white py-3 px-4 rounded-lg hover:bg-blue-700 transition-colors font-medium\"\n                            >\n                                <i className=\"fas fa-arrow-left mr-2\"></i>\n                                Kembali ke Beranda\n                            </button>\n                        </div>\n                    </div>\n                </div>\n            </div>\n\n            {/* Custom Bottom Navigation */}\n            <Box sx={{\n                position: 'fixed',\n                left: 0,\n                right: 0,\n                bottom: 0,\n                zIndex: 1300,\n                display: 'block',\n                backgroundColor: 'white',\n                borderTop: '1px solid #e0e0e0',\n                boxShadow: '0 -2px 10px rgba(0,0,0,0.1)'\n            }}>\n                <Box sx={{\n                    display: 'flex',\n                    justifyContent: 'space-around',\n                    alignItems: 'center',\n                    height: 64,\n                    px: 1\n                }}>\n                    <Box\n                        onClick={() => navigate('/')}\n                        className=\"bottom-nav-item active\"\n                        style={{ cursor: 'pointer' }}\n                    >\n                        <i className=\"fas fa-home bottom-nav-icon text-blue-600\"></i>\n                        <Typography variant=\"caption\" className=\"bottom-nav-label\" sx={{ color: 'primary.main' }}>\n                            Home\n                        </Typography>\n                    </Box>\n\n                    <Box\n                        onClick={() => navigate('/')}\n                        className=\"bottom-nav-item\"\n                        style={{ cursor: 'pointer' }}\n                    >\n                        <i className=\"fas fa-search bottom-nav-icon text-gray-500\"></i>\n                        <Typography variant=\"caption\" className=\"bottom-nav-label\" sx={{ color: 'text.secondary' }}>\n                            Cari\n                        </Typography>\n                    </Box>\n\n                    <Box\n                        onClick={() => navigate('/saved')}\n                        className=\"bottom-nav-item\"\n                        style={{ cursor: 'pointer' }}\n                    >\n                        <i className=\"fas fa-bookmark bottom-nav-icon text-gray-500\"></i>\n                        <Typography variant=\"caption\" className=\"bottom-nav-label\" sx={{ color: 'text.secondary' }}>\n                            Simpan\n                        </Typography>\n                    </Box>\n                </Box>\n            </Box>\n        </div>\n    );\n};\n\nexport default DataNews;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,SAAS,EAAEC,WAAW,QAAQ,kBAAkB;AACzD,OAAOC,MAAM,MAAM,qBAAqB;AACxC,OAAOC,GAAG,MAAM,mBAAmB;AACnC,OAAOC,UAAU,MAAM,0BAA0B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElD,MAAMC,QAAQ,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACnB,MAAM;IAAEC;EAAG,CAAC,GAAGT,SAAS,CAAC,CAAC;EAC1B,MAAMU,QAAQ,GAAGT,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACU,IAAI,EAAEC,OAAO,CAAC,GAAGd,QAAQ,CAAC,IAAI,CAAC;EACtC,MAAM,CAACe,WAAW,EAAEC,cAAc,CAAC,GAAGhB,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACiB,OAAO,EAAEC,UAAU,CAAC,GAAGlB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACmB,KAAK,EAAEC,QAAQ,CAAC,GAAGpB,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAACqB,SAAS,EAAEC,YAAY,CAAC,GAAGtB,QAAQ,CAAC,CAAC,CAAC;EAE7CC,SAAS,CAAC,MAAM;IACZ,IAAIU,EAAE,EAAE;MACJY,eAAe,CAACZ,EAAE,CAAC;IACvB;EACJ,CAAC,EAAE,CAACA,EAAE,CAAC,CAAC;EAER,MAAMY,eAAe,GAAG,MAAOC,MAAM,IAAK;IACtC,IAAI;MACAN,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMO,QAAQ,GAAG,MAAMC,KAAK,CAAC,wEAAwEF,MAAM,EAAE,CAAC;MAE9G,IAAI,CAACC,QAAQ,CAACE,EAAE,EAAE;QACd,MAAM,IAAIC,KAAK,CAAC,uBAAuBH,QAAQ,CAACI,MAAM,EAAE,CAAC;MAC7D;MAEA,MAAMC,IAAI,GAAG,MAAML,QAAQ,CAACM,IAAI,CAAC,CAAC;MAElC,IAAID,IAAI,CAACE,OAAO,EAAE;QACdlB,OAAO,CAACgB,IAAI,CAACA,IAAI,CAAC;QAClB;QACAG,gBAAgB,CAACH,IAAI,CAACA,IAAI,CAACI,WAAW,EAAEV,MAAM,CAAC;MACnD,CAAC,MAAM;QACHJ,QAAQ,CAAC,wBAAwB,CAAC;MACtC;IACJ,CAAC,CAAC,OAAOD,KAAK,EAAE;MACZgB,OAAO,CAAChB,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;;MAE5C;MACA,MAAMiB,SAAS,GAAG;QACdzB,EAAE,EAAEa,MAAM;QACVa,KAAK,EAAE,kBAAkB,GAAGb,MAAM;QAClCc,OAAO,EAAE,shBAAshB;QAC/hBC,KAAK,EAAE,gEAAgE,GAAGf,MAAM;QAChFgB,aAAa,EAAE,WAAW;QAC1BN,WAAW,EAAE,CAAC;QACdO,UAAU,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;QACpCC,IAAI,EAAE,IAAIF,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;QAC9BE,KAAK,EAAEC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG;MAC9C,CAAC;MAEDlC,OAAO,CAACsB,SAAS,CAAC;MAClBH,gBAAgB,CAAC,CAAC,EAAET,MAAM,CAAC,CAAC,CAAC;IACjC,CAAC,SAAS;MACNN,UAAU,CAAC,KAAK,CAAC;IACrB;EACJ,CAAC;EAED,MAAMe,gBAAgB,GAAG,MAAAA,CAAOgB,UAAU,EAAEC,aAAa,KAAK;IAC1D,IAAI;MACA,MAAMzB,QAAQ,GAAG,MAAMC,KAAK,CAAC,wEAAwEuB,UAAU,UAAU,CAAC;MAE1H,IAAI,CAACxB,QAAQ,CAACE,EAAE,EAAE;QACd,MAAM,IAAIC,KAAK,CAAC,uBAAuBH,QAAQ,CAACI,MAAM,EAAE,CAAC;MAC7D;MAEA,MAAMC,IAAI,GAAG,MAAML,QAAQ,CAACM,IAAI,CAAC,CAAC;MAElC,IAAID,IAAI,CAACE,OAAO,EAAE;QACd;QACA,MAAMmB,QAAQ,GAAGrB,IAAI,CAACA,IAAI,CAACsB,MAAM,CAACC,IAAI,IAAIA,IAAI,CAAC1C,EAAE,KAAK2C,QAAQ,CAACJ,aAAa,CAAC,CAAC,CAACK,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;QAC1FvC,cAAc,CAACmC,QAAQ,CAAC;MAC5B;IACJ,CAAC,CAAC,OAAOhC,KAAK,EAAE;MACZgB,OAAO,CAAChB,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;;MAEpD;MACA,MAAMqC,gBAAgB,GAAG,CACrB;QACI7C,EAAE,EAAE2C,QAAQ,CAACJ,aAAa,CAAC,GAAG,CAAC;QAC/Bb,KAAK,EAAE,sCAAsC;QAC7CE,KAAK,EAAE,+DAA+D;QACtEE,UAAU,EAAE,IAAIC,IAAI,CAACA,IAAI,CAACe,GAAG,CAAC,CAAC,GAAG,QAAQ,CAAC,CAACd,WAAW,CAAC,CAAC;QAAE;QAC3DC,IAAI,EAAE,IAAIF,IAAI,CAACA,IAAI,CAACe,GAAG,CAAC,CAAC,GAAG,QAAQ,CAAC,CAACd,WAAW,CAAC;MACtD,CAAC,EACD;QACIhC,EAAE,EAAE2C,QAAQ,CAACJ,aAAa,CAAC,GAAG,CAAC;QAC/Bb,KAAK,EAAE,oCAAoC;QAC3CE,KAAK,EAAE,+DAA+D;QACtEE,UAAU,EAAE,IAAIC,IAAI,CAACA,IAAI,CAACe,GAAG,CAAC,CAAC,GAAG,SAAS,CAAC,CAACd,WAAW,CAAC,CAAC;QAAE;QAC5DC,IAAI,EAAE,IAAIF,IAAI,CAACA,IAAI,CAACe,GAAG,CAAC,CAAC,GAAG,SAAS,CAAC,CAACd,WAAW,CAAC;MACvD,CAAC,EACD;QACIhC,EAAE,EAAE2C,QAAQ,CAACJ,aAAa,CAAC,GAAG,CAAC;QAC/Bb,KAAK,EAAE,oCAAoC;QAC3CE,KAAK,EAAE,+DAA+D;QACtEE,UAAU,EAAE,IAAIC,IAAI,CAACA,IAAI,CAACe,GAAG,CAAC,CAAC,GAAG,SAAS,CAAC,CAACd,WAAW,CAAC,CAAC;QAAE;QAC5DC,IAAI,EAAE,IAAIF,IAAI,CAACA,IAAI,CAACe,GAAG,CAAC,CAAC,GAAG,SAAS,CAAC,CAACd,WAAW,CAAC;MACvD,CAAC,CACJ;MAED3B,cAAc,CAACwC,gBAAgB,CAAC;IACpC;EACJ,CAAC;EAED,MAAME,WAAW,GAAIC,SAAS,IAAK;IAC/B,IAAI,CAACA,SAAS,EAAE,OAAO,iEAAiE;IACxF,IAAIA,SAAS,CAACC,UAAU,CAAC,MAAM,CAAC,EAAE,OAAOD,SAAS;IAClD,IAAIA,SAAS,CAACC,UAAU,CAAC,GAAG,CAAC,EAAE,OAAO,wBAAwBD,SAAS,EAAE;IACzE,OAAO,+CAA+CA,SAAS,EAAE;EACrE,CAAC;EAED,MAAME,UAAU,GAAIC,UAAU,IAAK;IAC/B,MAAMlB,IAAI,GAAG,IAAIF,IAAI,CAACoB,UAAU,CAAC;IACjC,OAAOlB,IAAI,CAACmB,kBAAkB,CAAC,OAAO,EAAE;MACpCC,OAAO,EAAE,MAAM;MACfC,IAAI,EAAE,SAAS;MACfC,KAAK,EAAE,MAAM;MACbC,GAAG,EAAE;IACT,CAAC,CAAC;EACN,CAAC;EAED,MAAMC,UAAU,GAAIN,UAAU,IAAK;IAC/B,MAAMlB,IAAI,GAAG,IAAIF,IAAI,CAACoB,UAAU,CAAC;IACjC,OAAOlB,IAAI,CAACyB,kBAAkB,CAAC,OAAO,EAAE;MACpCC,IAAI,EAAE,SAAS;MACfC,MAAM,EAAE;IACZ,CAAC,CAAC;EACN,CAAC;EAED,MAAMC,sBAAsB,GAAIhD,MAAM,IAAK;IACvCZ,QAAQ,CAAC,cAAcY,MAAM,EAAE,CAAC;EACpC,CAAC;EAED,MAAMiD,gBAAgB,GAAGA,CAAA,KAAM;IAC3B7D,QAAQ,CAAC,GAAG,CAAC;EACjB,CAAC;EAED,IAAIK,OAAO,EAAE;IACT,oBACIT,OAAA;MAAKkE,SAAS,EAAC,0DAA0D;MAAAC,QAAA,eACrEnE,OAAA;QAAKkE,SAAS,EAAC,aAAa;QAAAC,QAAA,gBACxBnE,OAAA;UAAKkE,SAAS,EAAC;QAA6E;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACnGvE,OAAA;UAAGkE,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAAgB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAEd;EAEA,IAAI5D,KAAK,EAAE;IACP,oBACIX,OAAA;MAAKkE,SAAS,EAAC,0DAA0D;MAAAC,QAAA,eACrEnE,OAAA;QAAKkE,SAAS,EAAC,aAAa;QAAAC,QAAA,gBACxBnE,OAAA;UAAKkE,SAAS,EAAC,yEAAyE;UAAAC,QAAA,gBACpFnE,OAAA;YAAGkE,SAAS,EAAC;UAAkC;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,EACnD5D,KAAK;QAAA;UAAAyD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eACNvE,OAAA;UACIwE,OAAO,EAAEP,gBAAiB;UAC1BC,SAAS,EAAC,iFAAiF;UAAAC,QAAA,gBAE3FnE,OAAA;YAAGkE,SAAS,EAAC;UAAwB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,sBAE9C;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAEd;EAEA,IAAI,CAAClE,IAAI,EAAE;IACP,oBACIL,OAAA;MAAKkE,SAAS,EAAC,0DAA0D;MAAAC,QAAA,eACrEnE,OAAA;QAAKkE,SAAS,EAAC,aAAa;QAAAC,QAAA,gBACxBnE,OAAA;UAAGkE,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAAsB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACvDvE,OAAA;UACIwE,OAAO,EAAEP,gBAAiB;UAC1BC,SAAS,EAAC,sFAAsF;UAAAC,QAAA,gBAEhGnE,OAAA;YAAGkE,SAAS,EAAC;UAAwB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,sBAE9C;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAEd;EAEA,oBACIvE,OAAA;IAAKkE,SAAS,EAAC,yBAAyB;IAAAC,QAAA,gBAEpCnE,OAAA,CAACJ,MAAM;MAAAwE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAGVvE,OAAA;MAAKkE,SAAS,EAAC,6BAA6B;MAAAC,QAAA,eACxCnE,OAAA;QAAKkE,SAAS,EAAC,6BAA6B;QAAAC,QAAA,eACxCnE,OAAA;UAAKkE,SAAS,EAAC,mDAAmD;UAAAC,QAAA,gBAC9DnE,OAAA;YACIwE,OAAO,EAAEP,gBAAiB;YAC1BC,SAAS,EAAC,uCAAuC;YAAAC,QAAA,gBAEjDnE,OAAA;cAAGkE,SAAS,EAAC;YAAkB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,WAExC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTvE,OAAA;YAAGkE,SAAS,EAAC;UAA8B;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAChDvE,OAAA;YAAMkE,SAAS,EAAC,2BAA2B;YAAAC,QAAA,EAAC;UAAa;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/D;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,eAGNvE,OAAA;MAAKkE,SAAS,EAAC,6BAA6B;MAAAC,QAAA,eACxCnE,OAAA;QAAKkE,SAAS,EAAC,uCAAuC;QAAAC,QAAA,gBAElDnE,OAAA;UAAKkE,SAAS,EAAC,eAAe;UAAAC,QAAA,eAC1BnE,OAAA;YAASkE,SAAS,EAAC,+CAA+C;YAAAC,QAAA,gBAE9DnE,OAAA;cAAKkE,SAAS,EAAC,UAAU;cAAAC,QAAA,gBACrBnE,OAAA;gBACIyE,GAAG,EAAEvB,WAAW,CAAC7C,IAAI,CAAC0B,KAAK,CAAE;gBAC7B2C,GAAG,EAAErE,IAAI,CAACwB,KAAM;gBAChBqC,SAAS,EAAC,kCAAkC;gBAC5CS,OAAO,EAAGC,CAAC,IAAK;kBACZA,CAAC,CAACC,MAAM,CAACJ,GAAG,GAAG,iEAAiE;gBACpF;cAAE;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,eACFvE,OAAA;gBAAKkE,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,eAClCnE,OAAA;kBAAMkE,SAAS,EAAC,mEAAmE;kBAAAC,QAAA,EAC9E9D,IAAI,CAAC2B,aAAa,IAAI;gBAAQ;kBAAAoC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7B;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,eAGNvE,OAAA;cAAKkE,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACvBnE,OAAA;gBAAIkE,SAAS,EAAC,iEAAiE;gBAAAC,QAAA,EAC1E9D,IAAI,CAACwB;cAAK;gBAAAuC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACX,CAAC,eAGLvE,OAAA;gBAAKkE,SAAS,EAAC,4EAA4E;gBAAAC,QAAA,gBACvFnE,OAAA;kBAAKkE,SAAS,EAAC,mBAAmB;kBAAAC,QAAA,gBAC9BnE,OAAA;oBAAGkE,SAAS,EAAC;kBAAwC;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,EACzDlB,UAAU,CAAChD,IAAI,CAAC4B,UAAU,IAAI5B,IAAI,CAAC+B,IAAI,CAAC;gBAAA;kBAAAgC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxC,CAAC,eACNvE,OAAA;kBAAKkE,SAAS,EAAC,mBAAmB;kBAAAC,QAAA,gBAC9BnE,OAAA;oBAAGkE,SAAS,EAAC;kBAAiC;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,EAClDX,UAAU,CAACvD,IAAI,CAAC4B,UAAU,IAAI5B,IAAI,CAAC+B,IAAI,CAAC;gBAAA;kBAAAgC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxC,CAAC,eACNvE,OAAA;kBAAKkE,SAAS,EAAC,mBAAmB;kBAAAC,QAAA,gBAC9BnE,OAAA;oBAAGkE,SAAS,EAAC;kBAA+B;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,EAChDlE,IAAI,CAACgC,KAAK,IAAI,CAAC,EAAC,QACrB;gBAAA;kBAAA+B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,eAGNvE,OAAA;gBAAKkE,SAAS,EAAC,2BAA2B;gBAAAC,QAAA,eACtCnE,OAAA;kBACIkE,SAAS,EAAC,mDAAmD;kBAC7DY,uBAAuB,EAAE;oBACrBC,MAAM,EAAE1E,IAAI,CAACyB,OAAO,CAACkD,OAAO,CAAC,KAAK,EAAE,MAAM;kBAC9C;gBAAE;kBAAAZ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC,eAGNvE,OAAA;gBAAKkE,SAAS,EAAC,oBAAoB;gBAAAC,QAAA,gBAC/BnE,OAAA;kBAAIkE,SAAS,EAAC,0CAA0C;kBAAAC,QAAA,EAAC;gBAAe;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC7EvE,OAAA;kBAAKkE,SAAS,EAAC,gBAAgB;kBAAAC,QAAA,gBAC3BnE,OAAA;oBAAQkE,SAAS,EAAC,iFAAiF;oBAAAC,QAAA,gBAC/FnE,OAAA;sBAAGkE,SAAS,EAAC;oBAAwB;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,YAE9C;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACTvE,OAAA;oBAAQkE,SAAS,EAAC,iFAAiF;oBAAAC,QAAA,gBAC/FnE,OAAA;sBAAGkE,SAAS,EAAC;oBAAqB;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,WAE3C;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACTvE,OAAA;oBAAQkE,SAAS,EAAC,mFAAmF;oBAAAC,QAAA,gBACjGnE,OAAA;sBAAGkE,SAAS,EAAC;oBAAsB;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,YAE5C;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACR,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eAGNvE,OAAA;UAAKkE,SAAS,EAAC,eAAe;UAAAC,QAAA,GAEzB5D,WAAW,CAAC0E,MAAM,GAAG,CAAC,iBACnBjF,OAAA;YAAKkE,SAAS,EAAC,wCAAwC;YAAAC,QAAA,gBACnDnE,OAAA;cAAIkE,SAAS,EAAC,0CAA0C;cAAAC,QAAA,gBACpDnE,OAAA;gBAAGkE,SAAS,EAAC;cAAqC;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,kBAE3D;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACLvE,OAAA;cAAKkE,SAAS,EAAC,WAAW;cAAAC,QAAA,EACrB5D,WAAW,CAAC2E,GAAG,CAAErC,IAAI,iBAClB7C,OAAA;gBAEIwE,OAAO,EAAEA,CAAA,KAAMR,sBAAsB,CAACnB,IAAI,CAAC1C,EAAE,CAAE;gBAC/C+D,SAAS,EAAC,uEAAuE;gBAAAC,QAAA,gBAEjFnE,OAAA;kBACIyE,GAAG,EAAEvB,WAAW,CAACL,IAAI,CAACd,KAAK,CAAE;kBAC7B2C,GAAG,EAAE7B,IAAI,CAAChB,KAAM;kBAChBqC,SAAS,EAAC,sDAAsD;kBAChES,OAAO,EAAGC,CAAC,IAAK;oBACZA,CAAC,CAACC,MAAM,CAACJ,GAAG,GAAG,4CAA4C;kBAC/D;gBAAE;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC,eACFvE,OAAA;kBAAKkE,SAAS,EAAC,gBAAgB;kBAAAC,QAAA,gBAC3BnE,OAAA;oBAAIkE,SAAS,EAAC,qDAAqD;oBAAAC,QAAA,EAC9DtB,IAAI,CAAChB;kBAAK;oBAAAuC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACX,CAAC,eACLvE,OAAA;oBAAGkE,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,EAC/Bd,UAAU,CAACR,IAAI,CAACZ,UAAU,IAAIY,IAAI,CAACT,IAAI;kBAAC;oBAAAgC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1C,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA,GAnBD1B,IAAI,CAAC1C,EAAE;gBAAAiE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAoBX,CACR;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CACR,eAGDvE,OAAA;YAAKkE,SAAS,EAAC,mCAAmC;YAAAC,QAAA,eAC9CnE,OAAA;cACIwE,OAAO,EAAEP,gBAAiB;cAC1BC,SAAS,EAAC,oGAAoG;cAAAC,QAAA,gBAE9GnE,OAAA;gBAAGkE,SAAS,EAAC;cAAwB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,sBAE9C;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,eAGNvE,OAAA,CAACH,GAAG;MAACsF,EAAE,EAAE;QACLC,QAAQ,EAAE,OAAO;QACjBC,IAAI,EAAE,CAAC;QACPC,KAAK,EAAE,CAAC;QACRC,MAAM,EAAE,CAAC;QACTC,MAAM,EAAE,IAAI;QACZC,OAAO,EAAE,OAAO;QAChBC,eAAe,EAAE,OAAO;QACxBC,SAAS,EAAE,mBAAmB;QAC9BC,SAAS,EAAE;MACf,CAAE;MAAAzB,QAAA,eACEnE,OAAA,CAACH,GAAG;QAACsF,EAAE,EAAE;UACLM,OAAO,EAAE,MAAM;UACfI,cAAc,EAAE,cAAc;UAC9BC,UAAU,EAAE,QAAQ;UACpBC,MAAM,EAAE,EAAE;UACVC,EAAE,EAAE;QACR,CAAE;QAAA7B,QAAA,gBACEnE,OAAA,CAACH,GAAG;UACA2E,OAAO,EAAEA,CAAA,KAAMpE,QAAQ,CAAC,GAAG,CAAE;UAC7B8D,SAAS,EAAC,wBAAwB;UAClC+B,KAAK,EAAE;YAAEC,MAAM,EAAE;UAAU,CAAE;UAAA/B,QAAA,gBAE7BnE,OAAA;YAAGkE,SAAS,EAAC;UAA2C;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC7DvE,OAAA,CAACF,UAAU;YAACqG,OAAO,EAAC,SAAS;YAACjC,SAAS,EAAC,kBAAkB;YAACiB,EAAE,EAAE;cAAEiB,KAAK,EAAE;YAAe,CAAE;YAAAjC,QAAA,EAAC;UAE1F;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACZ,CAAC,eAENvE,OAAA,CAACH,GAAG;UACA2E,OAAO,EAAEA,CAAA,KAAMpE,QAAQ,CAAC,GAAG,CAAE;UAC7B8D,SAAS,EAAC,iBAAiB;UAC3B+B,KAAK,EAAE;YAAEC,MAAM,EAAE;UAAU,CAAE;UAAA/B,QAAA,gBAE7BnE,OAAA;YAAGkE,SAAS,EAAC;UAA6C;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC/DvE,OAAA,CAACF,UAAU;YAACqG,OAAO,EAAC,SAAS;YAACjC,SAAS,EAAC,kBAAkB;YAACiB,EAAE,EAAE;cAAEiB,KAAK,EAAE;YAAiB,CAAE;YAAAjC,QAAA,EAAC;UAE5F;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACZ,CAAC,eAENvE,OAAA,CAACH,GAAG;UACA2E,OAAO,EAAEA,CAAA,KAAMpE,QAAQ,CAAC,QAAQ,CAAE;UAClC8D,SAAS,EAAC,iBAAiB;UAC3B+B,KAAK,EAAE;YAAEC,MAAM,EAAE;UAAU,CAAE;UAAA/B,QAAA,gBAE7BnE,OAAA;YAAGkE,SAAS,EAAC;UAA+C;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACjEvE,OAAA,CAACF,UAAU;YAACqG,OAAO,EAAC,SAAS;YAACjC,SAAS,EAAC,kBAAkB;YAACiB,EAAE,EAAE;cAAEiB,KAAK,EAAE;YAAiB,CAAE;YAAAjC,QAAA,EAAC;UAE5F;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACZ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEd,CAAC;AAACrE,EAAA,CAtYID,QAAQ;EAAA,QACKP,SAAS,EACPC,WAAW;AAAA;AAAA0G,EAAA,GAF1BpG,QAAQ;AAwYd,eAAeA,QAAQ;AAAC,IAAAoG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}