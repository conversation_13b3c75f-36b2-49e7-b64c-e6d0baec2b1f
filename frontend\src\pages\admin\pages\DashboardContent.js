import React from 'react';

const DashboardContent = ({ onNavigate }) => {
  return (
    <div>
      {/* Dashboard Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 lg:gap-6 mb-8">
        {/* Total Berita */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="p-3 rounded-full bg-blue-100">
              <i className="fas fa-newspaper text-blue-600 text-xl"></i>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Total Berita</p>
              <p className="text-2xl font-bold text-gray-900">156</p>
              <p className="text-xs text-green-600 mt-1">
                <i className="fas fa-arrow-up mr-1"></i>+12% dari bulan lalu
              </p>
            </div>
          </div>
        </div>
        
        {/* Total Views */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="p-3 rounded-full bg-green-100">
              <i className="fas fa-eye text-green-600 text-xl"></i>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Total Views</p>
              <p className="text-2xl font-bold text-gray-900">45.2K</p>
              <p className="text-xs text-green-600 mt-1">
                <i className="fas fa-arrow-up mr-1"></i>+8% dari bulan lalu
              </p>
            </div>
          </div>
        </div>
        
        {/* Berita Hari Ini */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="p-3 rounded-full bg-yellow-100">
              <i className="fas fa-calendar-day text-yellow-600 text-xl"></i>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Berita Hari Ini</p>
              <p className="text-2xl font-bold text-gray-900">8</p>
              <p className="text-xs text-blue-600 mt-1">
                <i className="fas fa-plus mr-1"></i>3 baru dipublikasi
              </p>
            </div>
          </div>
        </div>
        
        {/* Admin Aktif */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="p-3 rounded-full bg-purple-100">
              <i className="fas fa-users text-purple-600 text-xl"></i>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Admin Aktif</p>
              <p className="text-2xl font-bold text-gray-900">3</p>
              <p className="text-xs text-gray-500 mt-1">
                <i className="fas fa-circle text-green-500 mr-1"></i>Online sekarang
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Recent Activity & Quick Actions */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        {/* Recent Activity */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-900">Aktivitas Terbaru</h3>
            <button className="text-sm text-blue-600 hover:text-blue-700">Lihat Semua</button>
          </div>
          
          <div className="space-y-4">
            <div className="flex items-start space-x-3">
              <div className="w-2 h-2 bg-green-500 rounded-full mt-2"></div>
              <div className="flex-1">
                <p className="text-sm font-medium text-gray-900">Berita baru dipublikasi</p>
                <p className="text-xs text-gray-500">"Teknologi AI Terbaru 2024" • 2 menit yang lalu</p>
              </div>
            </div>
            
            <div className="flex items-start space-x-3">
              <div className="w-2 h-2 bg-blue-500 rounded-full mt-2"></div>
              <div className="flex-1">
                <p className="text-sm font-medium text-gray-900">Admin login</p>
                <p className="text-xs text-gray-500">John Doe masuk ke sistem • 15 menit yang lalu</p>
              </div>
            </div>
            
            <div className="flex items-start space-x-3">
              <div className="w-2 h-2 bg-yellow-500 rounded-full mt-2"></div>
              <div className="flex-1">
                <p className="text-sm font-medium text-gray-900">Berita diperbarui</p>
                <p className="text-xs text-gray-500">"Update Ekonomi Indonesia" • 1 jam yang lalu</p>
              </div>
            </div>
            
            <div className="flex items-start space-x-3">
              <div className="w-2 h-2 bg-red-500 rounded-full mt-2"></div>
              <div className="flex-1">
                <p className="text-sm font-medium text-gray-900">Berita dihapus</p>
                <p className="text-xs text-gray-500">"Draft artikel lama" • 2 jam yang lalu</p>
              </div>
            </div>
          </div>
        </div>
        
        {/* Quick Actions */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Aksi Cepat</h3>
          
          <div className="grid grid-cols-2 gap-4">
            <button
              onClick={() => onNavigate('berita')}
              className="flex flex-col items-center p-4 border-2 border-dashed border-gray-300 rounded-lg hover:border-blue-500 hover:bg-blue-50 transition-colors group"
            >
              <i className="fas fa-plus text-2xl text-gray-400 group-hover:text-blue-600 mb-2"></i>
              <span className="text-sm font-medium text-gray-600 group-hover:text-blue-700">Tambah Berita</span>
            </button>
            
            <button className="flex flex-col items-center p-4 border-2 border-dashed border-gray-300 rounded-lg hover:border-green-500 hover:bg-green-50 transition-colors group">
              <i className="fas fa-upload text-2xl text-gray-400 group-hover:text-green-600 mb-2"></i>
              <span className="text-sm font-medium text-gray-600 group-hover:text-green-700">Upload Media</span>
            </button>
            
            <button
              onClick={() => onNavigate('pengaturan')}
              className="flex flex-col items-center p-4 border-2 border-dashed border-gray-300 rounded-lg hover:border-purple-500 hover:bg-purple-50 transition-colors group"
            >
              <i className="fas fa-cog text-2xl text-gray-400 group-hover:text-purple-600 mb-2"></i>
              <span className="text-sm font-medium text-gray-600 group-hover:text-purple-700">Pengaturan</span>
            </button>
            
            <button className="flex flex-col items-center p-4 border-2 border-dashed border-gray-300 rounded-lg hover:border-orange-500 hover:bg-orange-50 transition-colors group">
              <i className="fas fa-chart-bar text-2xl text-gray-400 group-hover:text-orange-600 mb-2"></i>
              <span className="text-sm font-medium text-gray-600 group-hover:text-orange-700">Laporan</span>
            </button>
          </div>
        </div>
      </div>

      {/* Popular Articles */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-gray-900">Berita Populer Minggu Ini</h3>
          <button
            onClick={() => onNavigate('berita')}
            className="text-sm text-blue-600 hover:text-blue-700"
          >
            Kelola Berita
          </button>
        </div>
        
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead>
              <tr className="border-b border-gray-200">
                <th className="text-left py-3 px-4 font-medium text-gray-600 text-sm">Judul</th>
                <th className="text-left py-3 px-4 font-medium text-gray-600 text-sm">Views</th>
                <th className="text-left py-3 px-4 font-medium text-gray-600 text-sm">Status</th>
                <th className="text-left py-3 px-4 font-medium text-gray-600 text-sm">Tanggal</th>
              </tr>
            </thead>
            <tbody>
              <tr className="border-b border-gray-100 hover:bg-gray-50">
                <td className="py-3 px-4">
                  <div className="flex items-center">
                    <img src="https://placehold.co/40x40?text=1" alt="News" className="w-10 h-10 rounded object-cover mr-3" />
                    <div>
                      <p className="font-medium text-gray-900 text-sm">Teknologi AI Mengubah Dunia</p>
                      <p className="text-xs text-gray-500">Kategori: Teknologi</p>
                    </div>
                  </div>
                </td>
                <td className="py-3 px-4 text-sm text-gray-600">12.5K</td>
                <td className="py-3 px-4">
                  <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
                    Published
                  </span>
                </td>
                <td className="py-3 px-4 text-sm text-gray-600">15 Jan 2024</td>
              </tr>
              
              <tr className="border-b border-gray-100 hover:bg-gray-50">
                <td className="py-3 px-4">
                  <div className="flex items-center">
                    <img src="https://placehold.co/40x40?text=2" alt="News" className="w-10 h-10 rounded object-cover mr-3" />
                    <div>
                      <p className="font-medium text-gray-900 text-sm">Ekonomi Indonesia Tumbuh Pesat</p>
                      <p className="text-xs text-gray-500">Kategori: Ekonomi</p>
                    </div>
                  </div>
                </td>
                <td className="py-3 px-4 text-sm text-gray-600">8.9K</td>
                <td className="py-3 px-4">
                  <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
                    Published
                  </span>
                </td>
                <td className="py-3 px-4 text-sm text-gray-600">14 Jan 2024</td>
              </tr>
              
              <tr className="hover:bg-gray-50">
                <td className="py-3 px-4">
                  <div className="flex items-center">
                    <img src="https://placehold.co/40x40?text=3" alt="News" className="w-10 h-10 rounded object-cover mr-3" />
                    <div>
                      <p className="font-medium text-gray-900 text-sm">Olahraga Nasional Berprestasi</p>
                      <p className="text-xs text-gray-500">Kategori: Olahraga</p>
                    </div>
                  </div>
                </td>
                <td className="py-3 px-4 text-sm text-gray-600">6.7K</td>
                <td className="py-3 px-4">
                  <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800">
                    Draft
                  </span>
                </td>
                <td className="py-3 px-4 text-sm text-gray-600">13 Jan 2024</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};

export default DashboardContent;
