# Instruksi Setup React News Portal

## 1. Setup Database

### Menggunakan XAMPP/Laragon:
1. Buka phpMyAdmin (http://localhost/phpmyadmin)
2. Import file `backend/database.sql` atau jalankan script di `frontend/src/pages/admin/setup_database.sql`
3. Database `react_news` akan otomatis dibuat dengan semua tabel yang diperlukan

### Data Default:
- **Admin Login**: username `admin`, password `admin123`
- **Kategori**: Umum, Teknologi, Bisnis, Olahraga, Hiburan, Politik, Kesehatan
- **Sample Posts**: 3 berita contoh sudah tersedia

## 2. Setup Backend (Node.js)

```bash
cd backend
npm install
npm start
```

Backend akan berjalan di `http://localhost:5000`

## 3. Setup Frontend (React)

```bash
cd frontend
npm install
npm start
```

Frontend akan berjalan di `http://localhost:3000`

## 4. Setup Admin Dashboard (PHP)

Admin dashboard tersedia di: `frontend/src/pages/admin/DashboardTable.php`

### Akses Admin:
1. <PERSON>uka `http://localhost/react-news/frontend/src/pages/admin/DashboardTable.php`
2. Login dengan username: `admin`, password: `admin123`

### Fitur Admin Dashboard:
- ✅ **Dashboard**: Statistik lengkap (total berita, views, shares, likes)
- ✅ **CRUD Berita**: Tambah, edit, hapus berita dengan upload gambar
- ✅ **Kategori**: Manajemen kategori dengan warna custom
- ✅ **Settings**: Pengaturan website (nama, logo, warna theme)
- ✅ **Responsive**: Mobile-friendly design
- ✅ **Real-time Stats**: Data statistik real-time dari database

## 5. Fitur Landing Page

### User Features:
- ✅ **Browse Berita**: Filter berdasarkan kategori
- ✅ **Search**: Pencarian berita
- ✅ **Bookmark**: Simpan berita favorit (terintegrasi database)
- ✅ **Share**: Bagikan berita dengan tracking
- ✅ **View Tracking**: Otomatis tracking views
- ✅ **Responsive**: Mobile dan desktop friendly
- ✅ **Popular Posts**: Berita populer berdasarkan views+shares+likes

### Saved Posts:
- Akses di `http://localhost:3000/saved` atau melalui bottom navigation
- Terintegrasi dengan database `saved` table
- Real-time add/remove bookmark

## 6. API Endpoints

### Admin API (PHP):
```
GET  api.php?action=get_news           - Daftar berita
GET  api.php?action=get_stats          - Statistik dashboard  
GET  api.php?action=get_categories     - Daftar kategori
POST api.php (action=add_news)         - Tambah berita
POST api.php (action=update_news)      - Update berita
POST api.php (action=delete_news)      - Hapus berita
GET  api.php?action=get_saved_posts    - Berita tersimpan
POST api.php (action=save_post)        - Simpan berita
POST api.php (action=unsave_post)      - Hapus dari simpan
POST api.php (action=update_post_share) - Update share count
POST api.php (action=update_post_views) - Update view count
```

### Node.js API (Proxy):
```
GET  /api/posts/saved                  - Berita tersimpan
POST /api/posts/:id/save               - Simpan berita
DELETE /api/posts/:id/save             - Hapus dari simpan  
POST /api/posts/:id/share              - Update share count
POST /api/posts/:id/view               - Update view count
```

## 7. Database Schema

### Tabel Utama:
- `admin` - User management
- `categories` - Kategori berita dengan warna
- `posts` - Berita dengan kolom lengkap (views, shares, likes, etc)
- `settings` - Pengaturan website
- `saved` - Bookmark berita
- `page_views` - Tracking detail views

### Kolom Penting Posts:
- `views` - Jumlah views
- `share` - Jumlah shares  
- `likes` - Jumlah likes
- `status` - draft/published/archived
- `featured` - Berita unggulan
- `category_id` - Relasi ke categories
- `reading_time` - Estimasi waktu baca

## 8. File Structure

```
react-news/
├── backend/
│   ├── routes/savedRoutes.js     - API routes untuk saved posts
│   ├── app.js                    - Main backend app
│   └── database.sql              - Database schema
├── frontend/
│   ├── src/pages/admin/
│   │   ├── DashboardTable.php    - Admin dashboard
│   │   ├── api.php               - PHP API endpoints
│   │   └── config.php            - Database config
│   └── src/pages/user/
│       ├── LandingPage.js        - Main landing page
│       └── components/Saved.js   - Saved posts page
└── DATABASE_COLUMNS.md           - Database documentation
```

## 9. Troubleshooting

### Database Connection Error:
- Pastikan MySQL/MariaDB running
- Check config di `frontend/src/pages/admin/config.php`
- Sesuaikan DB_HOST, DB_USER, DB_PASS, DB_NAME

### CORS Error:
- Pastikan backend Node.js running di port 5000
- Check CORS settings di `backend/app.js`

### PHP API Error:
- Pastikan Apache/Nginx running
- Check path ke `api.php` di frontend
- Enable error reporting untuk debugging

### Upload Error:
- Pastikan folder `frontend/src/pages/admin/assets/news/` writable
- Check file permissions (755 atau 777)

## 10. Customization

### Warna Theme:
- Login ke admin dashboard
- Masuk ke Settings
- Ubah Primary Color, Secondary Color, Accent Color
- Perubahan akan langsung terlihat di landing page

### Logo Website:
- Upload logo baru melalui admin settings
- Format: JPG, PNG, GIF, WebP (max 2MB)

### Kategori:
- Tambah/edit kategori melalui database atau bisa dikembangkan UI admin
- Setiap kategori punya warna custom untuk UI

Sistem sudah terintegrasi penuh antara admin dashboard (PHP) dan landing page (React) dengan database MySQL yang sama.
