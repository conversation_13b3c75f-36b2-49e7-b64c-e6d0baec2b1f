<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

require_once __DIR__ . '/../config.php';

$method = $_SERVER['REQUEST_METHOD'];
$action = $_GET['action'] ?? '';

try {
    $pdo = getConnection();
    
    switch ($method) {
        case 'GET':
            if ($action === 'get' || empty($action)) {
                getSettings($pdo);
            }
            break;
            
        case 'POST':
            if ($action === 'update') {
                updateSettings($pdo);
            }
            break;
            
        default:
            http_response_code(405);
            echo json_encode(['success' => false, 'message' => 'Method not allowed']);
            break;
    }
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Server error: ' . $e->getMessage()]);
}

function getSettings($pdo) {
    try {
        // Get all settings from key-value structure
        $stmt = $pdo->query("SELECT setting_key, setting_value FROM settings WHERE is_public = TRUE OR category IN ('general', 'appearance')");
        $rows = $stmt->fetchAll(PDO::FETCH_ASSOC);

        $settings = [];
        foreach ($rows as $row) {
            $settings[$row['setting_key']] = $row['setting_value'];
        }

        // Add defaults for missing settings
        $defaults = [
            'website_name' => 'React News Portal',
            'website_logo' => '/logo192.png',
            'website_description' => 'Portal berita terkini dan terpercaya',
            'primary_color' => '#3B82F6',
            'secondary_color' => '#10B981',
            'accent_color' => '#F59E0B',
            'footer_text' => '© 2024 React News Portal. All rights reserved.',
            'contact_email' => '<EMAIL>',
            'social_facebook' => '',
            'social_twitter' => '',
            'social_instagram' => '',
            'social_youtube' => '',
            'meta_keywords' => 'berita, news, portal, react',
            'meta_description' => 'Portal berita terkini dengan teknologi React'
        ];

        // Merge with defaults
        $finalSettings = array_merge($defaults, $settings);

        echo json_encode($finalSettings);
    } catch (PDOException $e) {
        error_log("Settings API error: " . $e->getMessage());

        // Return defaults if database error
        $defaultSettings = [
            'website_name' => 'React News Portal',
            'website_logo' => '/logo192.png',
            'website_description' => 'Portal berita terkini dan terpercaya',
            'primary_color' => '#3B82F6',
            'secondary_color' => '#10B981',
            'accent_color' => '#F59E0B',
            'footer_text' => '© 2024 React News Portal. All rights reserved.',
            'contact_email' => '<EMAIL>',
            'social_facebook' => '',
            'social_twitter' => '',
            'social_instagram' => '',
            'social_youtube' => '',
            'meta_keywords' => 'berita, news, portal, react',
            'meta_description' => 'Portal berita terkini dengan teknologi React'
        ];

        echo json_encode($defaultSettings);
    }
}

function updateSettings($pdo) {
    try {
        $input = json_decode(file_get_contents('php://input'), true);

        if (!$input) {
            http_response_code(400);
            echo json_encode(['success' => false, 'message' => 'Invalid JSON input']);
            return;
        }

        $success = true;
        $messages = [];
        $updatedData = [];

        // Update each setting individually using key-value structure
        foreach ($input as $key => $value) {
            if (!empty($value) || $value === '') { // Allow empty strings
                try {
                    $stmt = $pdo->prepare("INSERT INTO settings (setting_key, setting_value, setting_type, category, is_public, created_at, updated_at)
                                          VALUES (?, ?, 'text', 'general', TRUE, NOW(), NOW())
                                          ON DUPLICATE KEY UPDATE setting_value = ?, updated_at = NOW()");

                    $result = $stmt->execute([$key, $value, $value]);

                    if ($result) {
                        $updatedData[$key] = $value;
                    } else {
                        $success = false;
                        $messages[] = "Failed to update $key";
                    }
                } catch (PDOException $e) {
                    $success = false;
                    $messages[] = "Error updating $key: " . $e->getMessage();
                    error_log("Settings update error for $key: " . $e->getMessage());
                }
            }
        }

        if ($success) {
            echo json_encode([
                'success' => true,
                'message' => 'Settings updated successfully',
                'data' => $updatedData
            ]);
        } else {
            http_response_code(500);
            echo json_encode([
                'success' => false,
                'message' => 'Some settings failed to update: ' . implode(', ', $messages),
                'data' => $updatedData
            ]);
        }

    } catch (Exception $e) {
        error_log("Settings update exception: " . $e->getMessage());
        http_response_code(500);
        echo json_encode(['success' => false, 'message' => 'Server error: ' . $e->getMessage()]);
    }
}

function createSettingsTableIfNotExists($pdo) {
    $sql = "CREATE TABLE IF NOT EXISTS settings (
        id INT AUTO_INCREMENT PRIMARY KEY,
        website_name VARCHAR(255) DEFAULT 'React News Portal',
        website_logo VARCHAR(500) DEFAULT '/logo192.png',
        website_description TEXT,
        primary_color VARCHAR(7) DEFAULT '#3B82F6',
        secondary_color VARCHAR(7) DEFAULT '#10B981',
        accent_color VARCHAR(7) DEFAULT '#F59E0B',
        footer_text TEXT,
        contact_email VARCHAR(255),
        social_facebook VARCHAR(255),
        social_twitter VARCHAR(255),
        social_instagram VARCHAR(255),
        social_youtube VARCHAR(255),
        meta_keywords TEXT,
        meta_description TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    )";
    
    $pdo->exec($sql);
}
?>
