// Authentication Service using localStorage
class AuthService {
  constructor() {
    this.storageKey = 'admin_auth';
    this.usersKey = 'admin_users';
    this.initializeDefaultUser();
  }

  // Initialize default admin user
  initializeDefaultUser() {
    const users = this.getUsers();
    if (users.length === 0) {
      // Create default admin user
      const defaultUser = {
        id: 1,
        username: 'admin',
        password: 'admin123', // In real app, this should be hashed
        email: '<EMAIL>',
        role: 'admin',
        createdAt: new Date().toISOString()
      };
      this.saveUser(defaultUser);
    }
  }

  // Get all users from localStorage
  getUsers() {
    const users = localStorage.getItem(this.usersKey);
    return users ? JSON.parse(users) : [];
  }

  // Save user to localStorage
  saveUser(user) {
    const users = this.getUsers();
    const existingIndex = users.findIndex(u => u.username === user.username);
    
    if (existingIndex >= 0) {
      users[existingIndex] = user;
    } else {
      user.id = users.length + 1;
      users.push(user);
    }
    
    localStorage.setItem(this.usersKey, JSON.stringify(users));
    return user;
  }

  // Login function
  login(username, password) {
    return new Promise((resolve, reject) => {
      setTimeout(() => { // Simulate API delay
        const users = this.getUsers();
        const user = users.find(u => u.username === username && u.password === password);
        
        if (user) {
          const authData = {
            isAuthenticated: true,
            user: {
              id: user.id,
              username: user.username,
              email: user.email,
              role: user.role
            },
            loginTime: new Date().toISOString(),
            token: this.generateToken(user)
          };
          
          localStorage.setItem(this.storageKey, JSON.stringify(authData));
          resolve({
            success: true,
            message: 'Login berhasil!',
            user: authData.user,
            token: authData.token
          });
        } else {
          reject({
            success: false,
            message: 'Username atau password salah!'
          });
        }
      }, 1000);
    });
  }

  // Register function
  register(username, password, email) {
    return new Promise((resolve, reject) => {
      setTimeout(() => { // Simulate API delay
        const users = this.getUsers();
        const existingUser = users.find(u => u.username === username || u.email === email);
        
        if (existingUser) {
          reject({
            success: false,
            message: 'Username atau email sudah digunakan!'
          });
          return;
        }

        if (password.length < 6) {
          reject({
            success: false,
            message: 'Password minimal 6 karakter!'
          });
          return;
        }

        const newUser = {
          username,
          password, // In real app, hash this
          email,
          role: 'admin',
          createdAt: new Date().toISOString()
        };

        this.saveUser(newUser);
        resolve({
          success: true,
          message: 'Registrasi berhasil! Silakan login.',
          user: {
            id: newUser.id,
            username: newUser.username,
            email: newUser.email
          }
        });
      }, 1000);
    });
  }

  // Check if user is authenticated
  isAuthenticated() {
    const authData = localStorage.getItem(this.storageKey);
    if (!authData) return false;
    
    try {
      const parsed = JSON.parse(authData);
      return parsed.isAuthenticated === true;
    } catch {
      return false;
    }
  }

  // Get current user
  getCurrentUser() {
    const authData = localStorage.getItem(this.storageKey);
    if (!authData) return null;
    
    try {
      const parsed = JSON.parse(authData);
      return parsed.user || null;
    } catch {
      return null;
    }
  }

  // Logout function
  logout() {
    localStorage.removeItem(this.storageKey);
    return {
      success: true,
      message: 'Logout berhasil!'
    };
  }

  // Generate simple token (in real app, use JWT)
  generateToken(user) {
    const payload = {
      id: user.id,
      username: user.username,
      timestamp: Date.now()
    };
    return btoa(JSON.stringify(payload));
  }

  // Verify token
  verifyToken(token) {
    try {
      const payload = JSON.parse(atob(token));
      const users = this.getUsers();
      const user = users.find(u => u.id === payload.id);
      return user ? true : false;
    } catch {
      return false;
    }
  }
}

// Export singleton instance
const authService = new AuthService();
export default authService;
