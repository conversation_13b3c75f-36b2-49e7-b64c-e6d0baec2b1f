<?php
// Database configuration
define('DB_HOST', 'localhost');
define('DB_USER', 'root');
define('DB_PASS', '');
define('DB_NAME', 'react_news');

// Website settings
$website_settings = [
    'name' => 'React News Portal',
    'logo' => 'assets/logo.png',
    'primary_color' => '#3B82F6',
    'secondary_color' => '#1E40AF',
    'accent_color' => '#F59E0B'
];

// Database connection
function getConnection() {
    try {
        $pdo = new PDO("mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4", DB_USER, DB_PASS);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        $pdo->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
        $pdo->setAttribute(PDO::ATTR_EMULATE_PREPARES, false);
        return $pdo;
    } catch(PDOException $e) {
        die("Connection failed: " . $e->getMessage());
    }
}

// Create tables if they don't exist
function createTables() {
    $pdo = getConnection();
    
    // Posts table (matching backend structure)
    $sql = "CREATE TABLE IF NOT EXISTS posts (
        id INT AUTO_INCREMENT PRIMARY KEY,
        title VARCHAR(255) NOT NULL,
        description TEXT NOT NULL,
        image VARCHAR(255) NULL,
        category VARCHAR(100) NOT NULL DEFAULT 'umum',
        share INT DEFAULT 0,
        views INT DEFAULT 0,
        date DATETIME DEFAULT CURRENT_TIMESTAMP,
        user INT NULL,
        status ENUM('draft', 'published') DEFAULT 'draft',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    )";
    $pdo->exec($sql);
    
    // Admin table
    $sql = "CREATE TABLE IF NOT EXISTS admin (
        id INT AUTO_INCREMENT PRIMARY KEY,
        username VARCHAR(50) NOT NULL UNIQUE,
        password VARCHAR(255) NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )";
    $pdo->exec($sql);
    
    // Settings table
    $sql = "CREATE TABLE IF NOT EXISTS settings (
        id INT AUTO_INCREMENT PRIMARY KEY,
        setting_key VARCHAR(100) UNIQUE NOT NULL,
        setting_value TEXT NOT NULL,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    )";
    $pdo->exec($sql);
    
    // Saved posts table
    $sql = "CREATE TABLE IF NOT EXISTS saved (
        id INT AUTO_INCREMENT PRIMARY KEY,
        post_id INT NOT NULL,
        saved_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (post_id) REFERENCES posts(id) ON DELETE CASCADE
    )";
    $pdo->exec($sql);
    
    // Insert default settings if not exist
    $settings = [
        ['website_name', 'React News Portal'],
        ['website_logo', 'assets/logo.png'],
        ['primary_color', '#3B82F6'],
        ['secondary_color', '#1E40AF'],
        ['accent_color', '#F59E0B']
    ];
    
    $stmt = $pdo->prepare("INSERT IGNORE INTO settings (setting_key, setting_value) VALUES (?, ?)");
    foreach ($settings as $setting) {
        $stmt->execute($setting);
    }
    
    // Insert default admin if not exist
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM admin");
    $stmt->execute();
    $adminCount = $stmt->fetchColumn();
    
    if ($adminCount == 0) {
        $defaultPassword = password_hash('admin123', PASSWORD_DEFAULT);
        $stmt = $pdo->prepare("INSERT INTO admin (username, password) VALUES (?, ?)");
        $stmt->execute(['admin', $defaultPassword]);
    }
}

// Initialize database
createTables();

// Helper functions for settings
function getSetting($key) {
    try {
        $pdo = getConnection();

        // Use key-value structure as per database.sql
        $stmt = $pdo->prepare("SELECT setting_value FROM settings WHERE setting_key = ?");
        $stmt->execute([$key]);
        $result = $stmt->fetch();

        if ($result) {
            return $result['setting_value'];
        }

        // Return defaults for common settings if not found
        $defaults = [
            'website_name' => 'React News Portal',
            'website_logo' => '/logo192.png',
            'website_description' => 'Portal berita terkini dan terpercaya',
            'primary_color' => '#3B82F6',
            'secondary_color' => '#10B981',
            'accent_color' => '#F59E0B'
        ];

        return $defaults[$key] ?? null;
    } catch (PDOException $e) {
        error_log("Error getting setting $key: " . $e->getMessage());

        // Return defaults if database error
        $defaults = [
            'website_name' => 'React News Portal',
            'website_logo' => '/logo192.png',
            'website_description' => 'Portal berita terkini dan terpercaya',
            'primary_color' => '#3B82F6',
            'secondary_color' => '#10B981',
            'accent_color' => '#F59E0B'
        ];

        return $defaults[$key] ?? null;
    }
}

function updateSetting($key, $value) {
    try {
        $pdo = getConnection();

        // Use key-value structure as per database.sql
        $stmt = $pdo->prepare("INSERT INTO settings (setting_key, setting_value, setting_type, category, is_public, created_at, updated_at)
                              VALUES (?, ?, 'text', 'general', FALSE, NOW(), NOW())
                              ON DUPLICATE KEY UPDATE setting_value = ?, updated_at = NOW()");

        $result = $stmt->execute([$key, $value, $value]);

        if (!$result) {
            error_log("Failed to update setting $key: " . implode(', ', $stmt->errorInfo()));
            return false;
        }

        return true;
    } catch (PDOException $e) {
        error_log("Error updating setting $key: " . $e->getMessage());
        return false;
    }
}

// Helper functions for posts/news
function getNews($limit = null) {
    $pdo = getConnection();
    $sql = "SELECT
                p.id,
                p.title,
                p.description as content,
                p.image,
                p.status,
                p.featured,
                p.views,
                p.share,
                p.likes,
                p.published_at,
                p.created_at,
                p.updated_at,
                c.name as category_name,
                c.color as category_color,
                a.username as author_name
            FROM posts p
            LEFT JOIN categories c ON p.category_id = c.id
            LEFT JOIN admin a ON p.user_id = a.id
            ORDER BY p.created_at DESC";
    if ($limit) {
        $sql .= " LIMIT " . intval($limit);
    }
    $stmt = $pdo->query($sql);
    return $stmt->fetchAll();
}

function getNewsById($id) {
    $pdo = getConnection();
    $stmt = $pdo->prepare("SELECT
                p.id,
                p.title,
                p.description as content,
                p.content as full_content,
                p.image,
                p.status,
                p.featured,
                p.category_id,
                p.views,
                p.share,
                p.likes,
                p.tags,
                p.meta_title,
                p.meta_description,
                p.published_at,
                p.created_at,
                p.updated_at,
                c.name as category_name,
                a.username as author_name
            FROM posts p
            LEFT JOIN categories c ON p.category_id = c.id
            LEFT JOIN admin a ON p.user_id = a.id
            WHERE p.id = ?");
    $stmt->execute([$id]);
    return $stmt->fetch();
}

function addNews($title, $content, $status = 'draft', $image = null, $category_id = 1) {
    $pdo = getConnection();

    // Ensure title is not empty
    if (empty($title)) {
        $title = 'Untitled Post';
    }

    // Generate slug from title
    $slug = generateSlug($title);

    // Ensure slug is not empty
    if (empty($slug)) {
        $slug = 'untitled-post-' . time();
    }

    // Ensure category_id is not null
    if ($category_id === null || $category_id === '') {
        $category_id = 1; // Default to 'Umum' category
    }

    // Calculate reading time (average 200 words per minute)
    $wordCount = str_word_count(strip_tags($content));
    $readingTime = max(1, ceil($wordCount / 200));

    $publishedAt = ($status === 'published') ? date('Y-m-d H:i:s') : null;

    $stmt = $pdo->prepare("INSERT INTO posts (title, slug, description, content, image, status, category_id, reading_time, published_at, user_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");
    return $stmt->execute([$title, $slug, $content, $content, $image, $status, $category_id, $readingTime, $publishedAt, 1]);
}

function updateNews($id, $title, $content, $status = 'draft', $image = null, $category_id = null) {
    $pdo = getConnection();

    // Ensure title is not empty
    if (empty($title)) {
        $title = 'Untitled Post';
    }

    // Generate new slug if title changed
    $currentNews = getNewsById($id);
    $slug = ($currentNews['title'] !== $title) ? generateSlug($title) : $currentNews['slug'];

    // Ensure slug is not null
    if (empty($slug)) {
        $slug = generateSlug($title);
    }

    // Ensure category_id is not null
    if ($category_id === null || $category_id === '') {
        $category_id = $currentNews['category_id'] ?? 1;
    }

    // Calculate reading time
    $wordCount = str_word_count(strip_tags($content));
    $readingTime = max(1, ceil($wordCount / 200));

    // Set published_at if status changed to published
    $publishedAt = null;
    if ($status === 'published' && $currentNews['status'] !== 'published') {
        $publishedAt = date('Y-m-d H:i:s');
    } elseif ($currentNews['published_at']) {
        $publishedAt = $currentNews['published_at'];
    }

    if ($image && $category_id) {
        $stmt = $pdo->prepare("UPDATE posts SET title = ?, slug = ?, description = ?, content = ?, image = ?, status = ?, category_id = ?, reading_time = ?, published_at = ? WHERE id = ?");
        return $stmt->execute([$title, $slug, $content, $content, $image, $status, $category_id, $readingTime, $publishedAt, $id]);
    } elseif ($image) {
        $stmt = $pdo->prepare("UPDATE posts SET title = ?, slug = ?, description = ?, content = ?, image = ?, status = ?, reading_time = ?, published_at = ? WHERE id = ?");
        return $stmt->execute([$title, $slug, $content, $content, $image, $status, $readingTime, $publishedAt, $id]);
    } elseif ($category_id) {
        $stmt = $pdo->prepare("UPDATE posts SET title = ?, slug = ?, description = ?, content = ?, status = ?, category_id = ?, reading_time = ?, published_at = ? WHERE id = ?");
        return $stmt->execute([$title, $slug, $content, $content, $status, $category_id, $readingTime, $publishedAt, $id]);
    } else {
        $stmt = $pdo->prepare("UPDATE posts SET title = ?, slug = ?, description = ?, content = ?, status = ?, reading_time = ?, published_at = ? WHERE id = ?");
        return $stmt->execute([$title, $slug, $content, $content, $status, $readingTime, $publishedAt, $id]);
    }
}

function deleteNews($id) {
    $pdo = getConnection();

    // Get image path before deleting
    $news = getNewsById($id);
    if ($news && $news['image'] && file_exists(__DIR__ . '/' . $news['image'])) {
        unlink(__DIR__ . '/' . $news['image']);
    }

    $stmt = $pdo->prepare("DELETE FROM posts WHERE id = ?");
    return $stmt->execute([$id]);
}

// Saved news functions
function getSavedNews() {
    try {
        $pdo = getConnection();
        $stmt = $pdo->prepare("
            SELECT p.*, c.name as category_name
            FROM posts p
            JOIN saved s ON p.id = s.post_id
            LEFT JOIN categories c ON p.category_id = c.id
            ORDER BY s.saved_at DESC
        ");
        $stmt->execute();
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch (PDOException $e) {
        error_log("Error getting saved news: " . $e->getMessage());
        return [];
    }
}

function addSavedNews($postId) {
    try {
        $pdo = getConnection();
        $stmt = $pdo->prepare("INSERT IGNORE INTO saved (post_id, saved_at) VALUES (?, NOW())");
        return $stmt->execute([$postId]);
    } catch (PDOException $e) {
        error_log("Error adding saved news: " . $e->getMessage());
        return false;
    }
}

function removeSavedNews($postId) {
    try {
        $pdo = getConnection();
        $stmt = $pdo->prepare("DELETE FROM saved WHERE post_id = ?");
        return $stmt->execute([$postId]);
    } catch (PDOException $e) {
        error_log("Error removing saved news: " . $e->getMessage());
        return false;
    }
}

function isSavedNews($postId) {
    try {
        $pdo = getConnection();
        $stmt = $pdo->prepare("SELECT COUNT(*) FROM saved WHERE post_id = ?");
        $stmt->execute([$postId]);
        return $stmt->fetchColumn() > 0;
    } catch (PDOException $e) {
        error_log("Error checking saved news: " . $e->getMessage());
        return false;
    }
}

// Helper function to generate slug
function generateSlug($text) {
    // Handle empty or null text
    if (empty($text)) {
        $text = 'untitled-post';
    }

    // Replace non-alphanumeric characters with hyphens
    $slug = preg_replace('/[^a-zA-Z0-9\s]/', '', $text);
    $slug = preg_replace('/\s+/', '-', trim($slug));
    $slug = strtolower($slug);

    // If slug is empty after processing, use default
    if (empty($slug)) {
        $slug = 'untitled-post';
    }

    // Ensure uniqueness
    $pdo = getConnection();
    $originalSlug = $slug;
    $counter = 1;

    while (true) {
        $stmt = $pdo->prepare("SELECT COUNT(*) FROM posts WHERE slug = ?");
        $stmt->execute([$slug]);
        if ($stmt->fetchColumn() == 0) {
            break;
        }
        $slug = $originalSlug . '-' . $counter;
        $counter++;
    }

    return $slug;
}

// Helper functions for categories
function getCategories() {
    $pdo = getConnection();
    $stmt = $pdo->query("SELECT * FROM categories WHERE is_active = 1 ORDER BY name ASC");
    return $stmt->fetchAll();
}

function getCategoryById($id) {
    $pdo = getConnection();
    $stmt = $pdo->prepare("SELECT * FROM categories WHERE id = ?");
    $stmt->execute([$id]);
    return $stmt->fetch();
}

// Helper function for statistics
function getStats() {
    $pdo = getConnection();

    // Get total users (admin)
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM admin WHERE is_active = 1");
    $totalUsers = $stmt->fetch()['count'];

    // Get total news
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM posts");
    $totalNews = $stmt->fetch()['count'];

    // Get published news
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM posts WHERE status = 'published'");
    $publishedNews = $stmt->fetch()['count'];

    // Get draft news
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM posts WHERE status = 'draft'");
    $draftNews = $stmt->fetch()['count'];

    // Get total views from page_views table
    $stmt = $pdo->query("SELECT COUNT(*) as total FROM page_views");
    $pageViews = $stmt->fetch()['total'] ?: 0;

    // Get total views from posts table
    $stmt = $pdo->query("SELECT SUM(views) as total FROM posts");
    $postViews = $stmt->fetch()['total'] ?: 0;

    // Get total shares
    $stmt = $pdo->query("SELECT SUM(share) as total FROM posts");
    $totalShares = $stmt->fetch()['total'] ?: 0;

    // Get total likes
    $stmt = $pdo->query("SELECT SUM(likes) as total FROM posts");
    $totalLikes = $stmt->fetch()['total'] ?: 0;

    // Get total comments (with error handling)
    try {
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM comments WHERE status = 'approved'");
        $totalComments = $stmt->fetch()['count'];
    } catch (PDOException $e) {
        // Table doesn't exist, use default value
        $totalComments = 0;
    }

    // Get total categories (with error handling)
    try {
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM categories WHERE is_active = 1");
        $totalCategories = $stmt->fetch()['count'];
    } catch (PDOException $e) {
        // Table doesn't exist, use default value
        $totalCategories = 7; // Default categories count
    }

    // Get today's views (with error handling)
    try {
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM page_views WHERE DATE(viewed_at) = CURDATE()");
        $todayViews = $stmt->fetch()['count'];
    } catch (PDOException $e) {
        // Table doesn't exist, use default value
        $todayViews = 0;
    }

    // Get this month's posts
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM posts WHERE MONTH(created_at) = MONTH(CURRENT_DATE()) AND YEAR(created_at) = YEAR(CURRENT_DATE())");
    $monthlyPosts = $stmt->fetch()['count'];

    // Get total saved posts (with error handling)
    try {
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM saved");
        $savedPosts = $stmt->fetch()['count'];
    } catch (PDOException $e) {
        // Table doesn't exist, use default value
        $savedPosts = 0;
    }

    return [
        'total_users' => $totalUsers,
        'total_news' => $totalNews,
        'published_news' => $publishedNews,
        'draft_news' => $draftNews,
        'page_views' => max($pageViews, $postViews),
        'total_shares' => $totalShares,
        'total_likes' => $totalLikes,
        'total_comments' => $totalComments,
        'total_categories' => $totalCategories,
        'today_views' => $todayViews,
        'monthly_posts' => $monthlyPosts,
        'saved_posts' => $savedPosts,
        'revenue' => $totalShares * 100 // Placeholder calculation
    ];
}

// Helper function for recent activities
function getRecentActivities($limit = 10) {
    $pdo = getConnection();
    $stmt = $pdo->prepare("
        SELECT
            'post' as type,
            p.title as title,
            p.status as status,
            p.created_at as date,
            a.username as user
        FROM posts p
        LEFT JOIN admin a ON p.user_id = a.id
        ORDER BY p.created_at DESC
        LIMIT ?
    ");
    $stmt->execute([$limit]);
    return $stmt->fetchAll();
}

// Helper function for popular posts
function getPopularPosts($limit = 5) {
    $pdo = getConnection();
    $stmt = $pdo->prepare("
        SELECT
            id, title, views, share, likes,
            created_at
        FROM posts
        WHERE status = 'published'
        ORDER BY (views + share * 2 + likes * 3) DESC
        LIMIT ?
    ");
    $stmt->execute([$limit]);
    return $stmt->fetchAll();
}
?>
