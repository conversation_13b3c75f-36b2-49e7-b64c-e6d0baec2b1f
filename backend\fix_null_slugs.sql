-- Fix NULL slugs in posts table
-- Run this in your MySQL database

USE react_news;

-- Update posts with NULL or empty slugs
UPDATE posts 
SET slug = CONCAT('post-', id, '-', UNIX_TIMESTAMP(NOW()))
WHERE slug IS NULL OR slug = '';

-- Update posts with duplicate slugs
UPDATE posts p1
JOIN (
    SELECT slug, MIN(id) as min_id
    FROM posts 
    WHERE slug IS NOT NULL AND slug != ''
    GROUP BY slug 
    HAVING COUNT(*) > 1
) p2 ON p1.slug = p2.slug AND p1.id != p2.min_id
SET p1.slug = CONCAT(p1.slug, '-', p1.id);

-- Ensure all posts have proper slugs based on title
UPDATE posts 
SET slug = CONCAT(
    LOWER(
        REPLACE(
            REPLACE(
                REPLACE(
                    REPLACE(
                        REPLACE(title, ' ', '-'),
                        '?', ''
                    ),
                    '!', ''
                ),
                '.', ''
            ),
            ',', ''
        )
    ),
    '-', id
)
WHERE slug IS NULL OR slug = '' OR slug LIKE 'post-%';

-- Clean up slugs to remove special characters
UPDATE posts 
SET slug = LOWER(
    REPLACE(
        REPLACE(
            REPLACE(
                REPLACE(
                    REPLACE(
                        REPLACE(
                            REPLACE(
                                REPLACE(
                                    REPLACE(
                                        REPLACE(slug, 'á', 'a'),
                                        'é', 'e'
                                    ),
                                    'í', 'i'
                                ),
                                'ó', 'o'
                            ),
                            'ú', 'u'
                        ),
                        'ñ', 'n'
                    ),
                    '--', '-'
                ),
                '---', '-'
            ),
            '----', '-'
        ),
        ' ', '-'
    )
);

-- Remove leading/trailing hyphens
UPDATE posts 
SET slug = TRIM(BOTH '-' FROM slug);

-- Ensure no empty slugs after cleanup
UPDATE posts 
SET slug = CONCAT('untitled-post-', id)
WHERE slug IS NULL OR slug = '';

-- Add unique constraint to prevent future duplicates (optional)
-- ALTER TABLE posts ADD UNIQUE KEY unique_slug (slug);

SELECT 'Slug cleanup completed!' as message;

-- Show posts with their new slugs
SELECT id, title, slug FROM posts ORDER BY id;
