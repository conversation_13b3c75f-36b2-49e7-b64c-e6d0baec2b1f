[{"C:\\laragon\\www\\react-news\\frontend\\src\\index.js": "1", "C:\\laragon\\www\\react-news\\frontend\\src\\App.js": "2", "C:\\laragon\\www\\react-news\\frontend\\src\\reportWebVitals.js": "3", "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\user\\LandingPage.js": "4", "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\user\\WebsitePage.js": "5", "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\user\\components\\Navbar.js": "6", "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\user\\components\\Footer.js": "7", "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\user\\components\\CategorySlider.js": "8", "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\user\\components\\NewsCard.js": "9", "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\user\\components\\PopularNews.js": "10", "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\admin\\auth\\Login.js": "11", "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\admin\\auth\\Register.js": "12", "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\admin\\swal.js": "13", "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\user\\components\\MainNewsCards.js": "14", "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\user\\components\\Saved.js": "15", "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\admin\\views.js": "16", "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\admin\\pages\\DashboardContent.js": "17", "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\admin\\pages\\BeritaContent.js": "18", "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\admin\\pages\\PengaturanContent.js": "19", "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\admin\\AdminDashboard.js": "20", "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\admin\\auth\\authService.js": "21", "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\user\\data-news.js": "22"}, {"size": 535, "mtime": 1752195971363, "results": "23", "hashOfConfig": "24"}, {"size": 2642, "mtime": 1752460017363, "results": "25", "hashOfConfig": "24"}, {"size": 362, "mtime": 1752195971670, "results": "26", "hashOfConfig": "24"}, {"size": 65511, "mtime": 1752461664010, "results": "27", "hashOfConfig": "24"}, {"size": 243, "mtime": 1752201497899, "results": "28", "hashOfConfig": "24"}, {"size": 662, "mtime": 1752201773682, "results": "29", "hashOfConfig": "24"}, {"size": 313, "mtime": 1752201773682, "results": "30", "hashOfConfig": "24"}, {"size": 822, "mtime": 1752201773678, "results": "31", "hashOfConfig": "24"}, {"size": 688, "mtime": 1752201773679, "results": "32", "hashOfConfig": "24"}, {"size": 749, "mtime": 1752201773681, "results": "33", "hashOfConfig": "24"}, {"size": 3534, "mtime": 1752368607654, "results": "34", "hashOfConfig": "24"}, {"size": 5407, "mtime": 1752367525610, "results": "35", "hashOfConfig": "24"}, {"size": 218, "mtime": 1752206025063, "results": "36", "hashOfConfig": "24"}, {"size": 4157, "mtime": 1752220101050, "results": "37", "hashOfConfig": "24"}, {"size": 15123, "mtime": 1752455676235, "results": "38", "hashOfConfig": "24"}, {"size": 0, "mtime": 1752306675896, "results": "39", "hashOfConfig": "24"}, {"size": 11682, "mtime": 1752310168602, "results": "40", "hashOfConfig": "24"}, {"size": 13390, "mtime": 1752310218662, "results": "41", "hashOfConfig": "24"}, {"size": 16930, "mtime": 1752310279691, "results": "42", "hashOfConfig": "24"}, {"size": 10964, "mtime": 1752312540670, "results": "43", "hashOfConfig": "24"}, {"size": 4742, "mtime": 1752366365655, "results": "44", "hashOfConfig": "24"}, {"size": 20692, "mtime": 1752461795130, "results": "45", "hashOfConfig": "24"}, {"filePath": "46", "messages": "47", "suppressedMessages": "48", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1umod7j", {"filePath": "49", "messages": "50", "suppressedMessages": "51", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "52", "messages": "53", "suppressedMessages": "54", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "55", "messages": "56", "suppressedMessages": "57", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "67", "messages": "68", "suppressedMessages": "69", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "70", "messages": "71", "suppressedMessages": "72", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "73", "messages": "74", "suppressedMessages": "75", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "82", "messages": "83", "suppressedMessages": "84", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "85", "messages": "86", "suppressedMessages": "87", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "88", "messages": "89", "suppressedMessages": "90", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "91", "messages": "92", "suppressedMessages": "93", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "97", "messages": "98", "suppressedMessages": "99", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "100", "messages": "101", "suppressedMessages": "102", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "103", "messages": "104", "suppressedMessages": "105", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "106", "messages": "107", "suppressedMessages": "108", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "109", "messages": "110", "suppressedMessages": "111", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "C:\\laragon\\www\\react-news\\frontend\\src\\index.js", [], [], "C:\\laragon\\www\\react-news\\frontend\\src\\App.js", [], [], "C:\\laragon\\www\\react-news\\frontend\\src\\reportWebVitals.js", [], [], "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\user\\LandingPage.js", ["112"], [], "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\user\\WebsitePage.js", [], [], "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\user\\components\\Navbar.js", [], [], "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\user\\components\\Footer.js", [], [], "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\user\\components\\CategorySlider.js", [], [], "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\user\\components\\NewsCard.js", [], [], "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\user\\components\\PopularNews.js", [], [], "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\admin\\auth\\Login.js", ["113"], [], "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\admin\\auth\\Register.js", [], [], "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\admin\\swal.js", [], [], "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\user\\components\\MainNewsCards.js", [], [], "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\user\\components\\Saved.js", [], [], "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\admin\\views.js", [], [], "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\admin\\pages\\DashboardContent.js", [], [], "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\admin\\pages\\BeritaContent.js", [], [], "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\admin\\pages\\PengaturanContent.js", [], [], "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\admin\\AdminDashboard.js", [], [], "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\admin\\auth\\authService.js", [], [], "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\user\\data-news.js", ["114", "115", "116", "117"], [], {"ruleId": "118", "severity": 1, "message": "119", "line": 17, "column": 8, "nodeType": "120", "messageId": "121", "endLine": 17, "endColumn": 14}, {"ruleId": "118", "severity": 1, "message": "122", "line": 2, "column": 53, "nodeType": "120", "messageId": "121", "endLine": 2, "endColumn": 69}, {"ruleId": "118", "severity": 1, "message": "123", "line": 18, "column": 11, "nodeType": "120", "messageId": "121", "endLine": 18, "endColumn": 20}, {"ruleId": "118", "severity": 1, "message": "124", "line": 24, "column": 12, "nodeType": "120", "messageId": "121", "endLine": 24, "endColumn": 21}, {"ruleId": "118", "severity": 1, "message": "125", "line": 24, "column": 23, "nodeType": "120", "messageId": "121", "endLine": 24, "endColumn": 35}, {"ruleId": "126", "severity": 1, "message": "127", "line": 32, "column": 8, "nodeType": "128", "endLine": 32, "endColumn": 12, "suggestions": "129"}, "no-unused-vars", "'Footer' is defined but never used.", "Identifier", "unusedVar", "'CircularProgress' is defined but never used.", "'isDesktop' is assigned a value but never used.", "'bottomNav' is assigned a value but never used.", "'setBottomNav' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fetchNewsDetail'. Either include it or remove the dependency array.", "ArrayExpression", ["130"], {"desc": "131", "fix": "132"}, "Update the dependencies array to be: [fetchNewsDetail, id]", {"range": "133", "text": "134"}, [1246, 1250], "[fetchNewsDetail, id]"]