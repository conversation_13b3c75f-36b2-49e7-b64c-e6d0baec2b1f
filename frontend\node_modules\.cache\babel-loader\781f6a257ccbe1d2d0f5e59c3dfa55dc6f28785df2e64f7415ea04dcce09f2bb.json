{"ast": null, "code": "var _jsxFileName = \"C:\\\\laragon\\\\www\\\\react-news\\\\frontend\\\\src\\\\pages\\\\user\\\\data-news.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useParams, useNavigate } from 'react-router-dom';\nimport Navbar from './components/Navbar';\nimport Box from '@mui/material/Box';\nimport Typography from '@mui/material/Typography';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst DataNews = () => {\n  _s();\n  const {\n    id\n  } = useParams();\n  const navigate = useNavigate();\n  const [news, setNews] = useState(null);\n  const [relatedNews, setRelatedNews] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [bottomNav, setBottomNav] = useState(0);\n  useEffect(() => {\n    if (id) {\n      fetchNewsDetail(id);\n    }\n  }, [id]);\n  const fetchNewsDetail = async newsId => {\n    try {\n      setLoading(true);\n\n      // First, increment view count\n      try {\n        await fetch(`http://localhost/react-news/backend/api.php?action=increment_views&id=${newsId}`, {\n          method: 'POST'\n        });\n      } catch (viewError) {\n        console.log('Could not increment views:', viewError);\n      }\n\n      // Then fetch news detail\n      const response = await fetch(`http://localhost/react-news/backend/api.php?action=get_news_by_id&id=${newsId}`);\n      if (!response.ok) {\n        throw new Error(`HTTP error! status: ${response.status}`);\n      }\n      const data = await response.json();\n      if (data.success && data.data) {\n        setNews(data.data);\n        // Fetch related news\n        if (data.data.category_id) {\n          fetchRelatedNews(data.data.category_id, newsId);\n        }\n      } else {\n        setError('Berita tidak ditemukan');\n      }\n    } catch (error) {\n      console.error('Error fetching news:', error);\n      setError('Gagal memuat berita. Silakan coba lagi.');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const fetchRelatedNews = async (categoryId, currentNewsId) => {\n    try {\n      const response = await fetch(`http://localhost/react-news/backend/api.php?action=get_news&category=${categoryId}&limit=4`);\n      if (!response.ok) {\n        throw new Error(`HTTP error! status: ${response.status}`);\n      }\n      const data = await response.json();\n      if (data.success && Array.isArray(data.data)) {\n        // Filter out current news and limit to 3\n        const filtered = data.data.filter(item => item.id !== parseInt(currentNewsId)).slice(0, 3);\n        setRelatedNews(filtered);\n      } else {\n        setRelatedNews([]);\n      }\n    } catch (error) {\n      console.error('Error fetching related news:', error);\n      setRelatedNews([]);\n    }\n  };\n  const getImageUrl = imagePath => {\n    if (!imagePath) return 'https://via.placeholder.com/800x400/E5E7EB/6B7280?text=No+Image';\n    if (imagePath.startsWith('http')) return imagePath;\n    if (imagePath.startsWith('/')) return `http://localhost:3000${imagePath}`;\n    return `http://localhost/react-news/backend/uploads/${imagePath}`;\n  };\n  const formatDate = dateString => {\n    const date = new Date(dateString);\n    return date.toLocaleDateString('id-ID', {\n      weekday: 'long',\n      year: 'numeric',\n      month: 'long',\n      day: 'numeric'\n    });\n  };\n  const formatTime = dateString => {\n    const date = new Date(dateString);\n    return date.toLocaleTimeString('id-ID', {\n      hour: '2-digit',\n      minute: '2-digit'\n    });\n  };\n  const handleRelatedNewsClick = newsId => {\n    navigate(`/data-news/${newsId}`);\n  };\n  const handleBackToHome = () => {\n    navigate('/');\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gray-50 flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 123,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600\",\n          children: \"Memuat berita...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 124,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 122,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 121,\n      columnNumber: 13\n    }, this);\n  }\n  if (error) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gray-50 flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-red-100 border border-red-400 text-red-700 px-6 py-4 rounded-lg mb-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"fas fa-exclamation-triangle mr-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 135,\n            columnNumber: 25\n          }, this), error]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 134,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: handleBackToHome,\n          className: \"bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors\",\n          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"fas fa-arrow-left mr-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 142,\n            columnNumber: 25\n          }, this), \"Kembali ke Beranda\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 138,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 133,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 132,\n      columnNumber: 13\n    }, this);\n  }\n  if (!news) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gray-50 flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600\",\n          children: \"Berita tidak ditemukan\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 154,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: handleBackToHome,\n          className: \"mt-4 bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors\",\n          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"fas fa-arrow-left mr-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 159,\n            columnNumber: 25\n          }, this), \"Kembali ke Beranda\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 155,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 153,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 152,\n      columnNumber: 13\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gray-50\",\n    children: [/*#__PURE__*/_jsxDEV(Navbar, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 170,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white shadow-sm border-b\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-6xl mx-auto px-4 py-4\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-2 text-sm text-gray-600\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: handleBackToHome,\n            className: \"hover:text-blue-600 transition-colors\",\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"fas fa-home mr-1\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 180,\n              columnNumber: 29\n            }, this), \"Beranda\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 176,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"fas fa-chevron-right text-xs\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 183,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-gray-800 font-medium\",\n            children: \"Detail Berita\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 184,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 175,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 174,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 173,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-6xl mx-auto px-4 py-8\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 lg:grid-cols-3 gap-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"lg:col-span-2\",\n          children: /*#__PURE__*/_jsxDEV(\"article\", {\n            className: \"bg-white rounded-lg shadow-md overflow-hidden\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"relative\",\n              children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                src: getImageUrl(news.image),\n                alt: news.title,\n                className: \"w-full h-64 md:h-80 object-cover\",\n                onError: e => {\n                  e.target.src = 'https://via.placeholder.com/800x400/E5E7EB/6B7280?text=No+Image';\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 197,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"absolute top-4 left-4\",\n                children: /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"bg-blue-600 text-white px-3 py-1 rounded-full text-sm font-medium\",\n                  children: news.category_name || 'Berita'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 206,\n                  columnNumber: 37\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 205,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 196,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-6 md:p-8\",\n              children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n                className: \"text-2xl md:text-3xl font-bold text-gray-900 mb-4 leading-tight\",\n                children: news.title\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 214,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex flex-wrap items-center gap-4 text-sm text-gray-600 mb-6 pb-6 border-b\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                    className: \"fas fa-calendar-alt mr-2 text-blue-600\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 221,\n                    columnNumber: 41\n                  }, this), formatDate(news.created_at || news.date)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 220,\n                  columnNumber: 37\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                    className: \"fas fa-clock mr-2 text-blue-600\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 225,\n                    columnNumber: 41\n                  }, this), formatTime(news.created_at || news.date)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 224,\n                  columnNumber: 37\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                    className: \"fas fa-eye mr-2 text-blue-600\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 229,\n                    columnNumber: 41\n                  }, this), news.views || 0, \" views\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 228,\n                  columnNumber: 37\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 219,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"prose prose-lg max-w-none\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-gray-700 leading-relaxed whitespace-pre-line\",\n                  dangerouslySetInnerHTML: {\n                    __html: news.content.replace(/\\n/g, '<br>')\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 236,\n                  columnNumber: 37\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 235,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mt-8 pt-6 border-t\",\n                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"text-lg font-semibold text-gray-900 mb-4\",\n                  children: \"Bagikan Artikel\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 246,\n                  columnNumber: 37\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex space-x-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors\",\n                    children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                      className: \"fab fa-facebook-f mr-2\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 249,\n                      columnNumber: 45\n                    }, this), \"Facebook\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 248,\n                    columnNumber: 41\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"bg-blue-400 text-white px-4 py-2 rounded-lg hover:bg-blue-500 transition-colors\",\n                    children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                      className: \"fab fa-twitter mr-2\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 253,\n                      columnNumber: 45\n                    }, this), \"Twitter\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 252,\n                    columnNumber: 41\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors\",\n                    children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                      className: \"fab fa-whatsapp mr-2\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 257,\n                      columnNumber: 45\n                    }, this), \"WhatsApp\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 256,\n                    columnNumber: 41\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 247,\n                  columnNumber: 37\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 245,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 213,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 194,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 193,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"lg:col-span-1\",\n          children: [relatedNews.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white rounded-lg shadow-md p-6 mb-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-lg font-semibold text-gray-900 mb-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"fas fa-newspaper mr-2 text-blue-600\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 272,\n                columnNumber: 37\n              }, this), \"Berita Terkait\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 271,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-4\",\n              children: relatedNews.map(item => /*#__PURE__*/_jsxDEV(\"div\", {\n                onClick: () => handleRelatedNewsClick(item.id),\n                className: \"flex cursor-pointer hover:bg-gray-50 p-2 rounded-lg transition-colors\",\n                children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                  src: getImageUrl(item.image),\n                  alt: item.title,\n                  className: \"w-16 h-16 object-cover rounded-lg mr-3 flex-shrink-0\",\n                  onError: e => {\n                    e.target.src = 'https://via.placeholder.com/150x150/E5E7EB/6B7280?text=No+Image';\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 282,\n                  columnNumber: 45\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex-1 min-w-0\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                    className: \"text-sm font-medium text-gray-900 line-clamp-2 mb-1\",\n                    children: item.title\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 291,\n                    columnNumber: 49\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-xs text-gray-600\",\n                    children: formatDate(item.created_at || item.date)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 294,\n                    columnNumber: 49\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 290,\n                  columnNumber: 45\n                }, this)]\n              }, item.id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 277,\n                columnNumber: 41\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 275,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 270,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white rounded-lg shadow-md p-6\",\n            children: /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: handleBackToHome,\n              className: \"w-full bg-blue-600 text-white py-3 px-4 rounded-lg hover:bg-blue-700 transition-colors font-medium\",\n              children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"fas fa-arrow-left mr-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 310,\n                columnNumber: 33\n              }, this), \"Kembali ke Beranda\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 306,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 305,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 267,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 191,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 190,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        position: 'fixed',\n        left: 0,\n        right: 0,\n        bottom: 0,\n        zIndex: 1300,\n        display: 'block',\n        backgroundColor: 'white',\n        borderTop: '1px solid #e0e0e0',\n        boxShadow: '0 -2px 10px rgba(0,0,0,0.1)'\n      },\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          justifyContent: 'space-around',\n          alignItems: 'center',\n          height: 64,\n          px: 1\n        },\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          onClick: () => navigate('/'),\n          className: \"bottom-nav-item active\",\n          style: {\n            cursor: 'pointer'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"fas fa-home bottom-nav-icon text-blue-600\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 342,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"caption\",\n            className: \"bottom-nav-label\",\n            sx: {\n              color: 'primary.main'\n            },\n            children: \"Home\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 343,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 337,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          onClick: () => navigate('/'),\n          className: \"bottom-nav-item\",\n          style: {\n            cursor: 'pointer'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"fas fa-search bottom-nav-icon text-gray-500\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 353,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"caption\",\n            className: \"bottom-nav-label\",\n            sx: {\n              color: 'text.secondary'\n            },\n            children: \"Cari\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 354,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 348,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          onClick: () => navigate('/saved'),\n          className: \"bottom-nav-item\",\n          style: {\n            cursor: 'pointer'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"fas fa-bookmark bottom-nav-icon text-gray-500\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 364,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"caption\",\n            className: \"bottom-nav-label\",\n            sx: {\n              color: 'text.secondary'\n            },\n            children: \"Simpan\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 365,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 359,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 330,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 319,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 168,\n    columnNumber: 9\n  }, this);\n};\n_s(DataNews, \"vbFYenLKApjRhtfs+0SH/a4Jyq0=\", false, function () {\n  return [useParams, useNavigate];\n});\n_c = DataNews;\nexport default DataNews;\nvar _c;\n$RefreshReg$(_c, \"DataNews\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useParams", "useNavigate", "<PERSON><PERSON><PERSON>", "Box", "Typography", "jsxDEV", "_jsxDEV", "DataNews", "_s", "id", "navigate", "news", "setNews", "relatedNews", "setRelatedNews", "loading", "setLoading", "error", "setError", "bottomNav", "setBottomNav", "fetchNewsDetail", "newsId", "fetch", "method", "viewError", "console", "log", "response", "ok", "Error", "status", "data", "json", "success", "category_id", "fetchRelatedNews", "categoryId", "currentNewsId", "Array", "isArray", "filtered", "filter", "item", "parseInt", "slice", "getImageUrl", "imagePath", "startsWith", "formatDate", "dateString", "date", "Date", "toLocaleDateString", "weekday", "year", "month", "day", "formatTime", "toLocaleTimeString", "hour", "minute", "handleRelatedNewsClick", "handleBackToHome", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "src", "image", "alt", "title", "onError", "e", "target", "category_name", "created_at", "views", "dangerouslySetInnerHTML", "__html", "content", "replace", "length", "map", "sx", "position", "left", "right", "bottom", "zIndex", "display", "backgroundColor", "borderTop", "boxShadow", "justifyContent", "alignItems", "height", "px", "style", "cursor", "variant", "color", "_c", "$RefreshReg$"], "sources": ["C:/laragon/www/react-news/frontend/src/pages/user/data-news.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useParams, useNavigate } from 'react-router-dom';\nimport Navbar from './components/Navbar';\nimport Box from '@mui/material/Box';\nimport Typography from '@mui/material/Typography';\n\nconst DataNews = () => {\n    const { id } = useParams();\n    const navigate = useNavigate();\n    const [news, setNews] = useState(null);\n    const [relatedNews, setRelatedNews] = useState([]);\n    const [loading, setLoading] = useState(true);\n    const [error, setError] = useState(null);\n    const [bottomNav, setBottomNav] = useState(0);\n\n    useEffect(() => {\n        if (id) {\n            fetchNewsDetail(id);\n        }\n    }, [id]);\n\n    const fetchNewsDetail = async (newsId) => {\n        try {\n            setLoading(true);\n\n            // First, increment view count\n            try {\n                await fetch(`http://localhost/react-news/backend/api.php?action=increment_views&id=${newsId}`, {\n                    method: 'POST'\n                });\n            } catch (viewError) {\n                console.log('Could not increment views:', viewError);\n            }\n\n            // Then fetch news detail\n            const response = await fetch(`http://localhost/react-news/backend/api.php?action=get_news_by_id&id=${newsId}`);\n\n            if (!response.ok) {\n                throw new Error(`HTTP error! status: ${response.status}`);\n            }\n\n            const data = await response.json();\n\n            if (data.success && data.data) {\n                setNews(data.data);\n                // Fetch related news\n                if (data.data.category_id) {\n                    fetchRelatedNews(data.data.category_id, newsId);\n                }\n            } else {\n                setError('Berita tidak ditemukan');\n            }\n        } catch (error) {\n            console.error('Error fetching news:', error);\n            setError('Gagal memuat berita. Silakan coba lagi.');\n        } finally {\n            setLoading(false);\n        }\n    };\n\n    const fetchRelatedNews = async (categoryId, currentNewsId) => {\n        try {\n            const response = await fetch(`http://localhost/react-news/backend/api.php?action=get_news&category=${categoryId}&limit=4`);\n\n            if (!response.ok) {\n                throw new Error(`HTTP error! status: ${response.status}`);\n            }\n\n            const data = await response.json();\n\n            if (data.success && Array.isArray(data.data)) {\n                // Filter out current news and limit to 3\n                const filtered = data.data\n                    .filter(item => item.id !== parseInt(currentNewsId))\n                    .slice(0, 3);\n                setRelatedNews(filtered);\n            } else {\n                setRelatedNews([]);\n            }\n        } catch (error) {\n            console.error('Error fetching related news:', error);\n            setRelatedNews([]);\n        }\n    };\n\n    const getImageUrl = (imagePath) => {\n        if (!imagePath) return 'https://via.placeholder.com/800x400/E5E7EB/6B7280?text=No+Image';\n        if (imagePath.startsWith('http')) return imagePath;\n        if (imagePath.startsWith('/')) return `http://localhost:3000${imagePath}`;\n        return `http://localhost/react-news/backend/uploads/${imagePath}`;\n    };\n\n    const formatDate = (dateString) => {\n        const date = new Date(dateString);\n        return date.toLocaleDateString('id-ID', {\n            weekday: 'long',\n            year: 'numeric',\n            month: 'long',\n            day: 'numeric'\n        });\n    };\n\n    const formatTime = (dateString) => {\n        const date = new Date(dateString);\n        return date.toLocaleTimeString('id-ID', {\n            hour: '2-digit',\n            minute: '2-digit'\n        });\n    };\n\n    const handleRelatedNewsClick = (newsId) => {\n        navigate(`/data-news/${newsId}`);\n    };\n\n    const handleBackToHome = () => {\n        navigate('/');\n    };\n\n    if (loading) {\n        return (\n            <div className=\"min-h-screen bg-gray-50 flex items-center justify-center\">\n                <div className=\"text-center\">\n                    <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4\"></div>\n                    <p className=\"text-gray-600\">Memuat berita...</p>\n                </div>\n            </div>\n        );\n    }\n\n    if (error) {\n        return (\n            <div className=\"min-h-screen bg-gray-50 flex items-center justify-center\">\n                <div className=\"text-center\">\n                    <div className=\"bg-red-100 border border-red-400 text-red-700 px-6 py-4 rounded-lg mb-4\">\n                        <i className=\"fas fa-exclamation-triangle mr-2\"></i>\n                        {error}\n                    </div>\n                    <button \n                        onClick={handleBackToHome}\n                        className=\"bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors\"\n                    >\n                        <i className=\"fas fa-arrow-left mr-2\"></i>\n                        Kembali ke Beranda\n                    </button>\n                </div>\n            </div>\n        );\n    }\n\n    if (!news) {\n        return (\n            <div className=\"min-h-screen bg-gray-50 flex items-center justify-center\">\n                <div className=\"text-center\">\n                    <p className=\"text-gray-600\">Berita tidak ditemukan</p>\n                    <button \n                        onClick={handleBackToHome}\n                        className=\"mt-4 bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors\"\n                    >\n                        <i className=\"fas fa-arrow-left mr-2\"></i>\n                        Kembali ke Beranda\n                    </button>\n                </div>\n            </div>\n        );\n    }\n\n    return (\n        <div className=\"min-h-screen bg-gray-50\">\n            {/* Navbar */}\n            <Navbar />\n\n            {/* Header/Breadcrumb */}\n            <div className=\"bg-white shadow-sm border-b\">\n                <div className=\"max-w-6xl mx-auto px-4 py-4\">\n                    <div className=\"flex items-center space-x-2 text-sm text-gray-600\">\n                        <button \n                            onClick={handleBackToHome}\n                            className=\"hover:text-blue-600 transition-colors\"\n                        >\n                            <i className=\"fas fa-home mr-1\"></i>\n                            Beranda\n                        </button>\n                        <i className=\"fas fa-chevron-right text-xs\"></i>\n                        <span className=\"text-gray-800 font-medium\">Detail Berita</span>\n                    </div>\n                </div>\n            </div>\n\n            {/* Main Content */}\n            <div className=\"max-w-6xl mx-auto px-4 py-8\">\n                <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-8\">\n                    {/* Main Article */}\n                    <div className=\"lg:col-span-2\">\n                        <article className=\"bg-white rounded-lg shadow-md overflow-hidden\">\n                            {/* Article Header */}\n                            <div className=\"relative\">\n                                <img \n                                    src={getImageUrl(news.image)} \n                                    alt={news.title}\n                                    className=\"w-full h-64 md:h-80 object-cover\"\n                                    onError={(e) => {\n                                        e.target.src = 'https://via.placeholder.com/800x400/E5E7EB/6B7280?text=No+Image';\n                                    }}\n                                />\n                                <div className=\"absolute top-4 left-4\">\n                                    <span className=\"bg-blue-600 text-white px-3 py-1 rounded-full text-sm font-medium\">\n                                        {news.category_name || 'Berita'}\n                                    </span>\n                                </div>\n                            </div>\n\n                            {/* Article Content */}\n                            <div className=\"p-6 md:p-8\">\n                                <h1 className=\"text-2xl md:text-3xl font-bold text-gray-900 mb-4 leading-tight\">\n                                    {news.title}\n                                </h1>\n\n                                {/* Meta Information */}\n                                <div className=\"flex flex-wrap items-center gap-4 text-sm text-gray-600 mb-6 pb-6 border-b\">\n                                    <div className=\"flex items-center\">\n                                        <i className=\"fas fa-calendar-alt mr-2 text-blue-600\"></i>\n                                        {formatDate(news.created_at || news.date)}\n                                    </div>\n                                    <div className=\"flex items-center\">\n                                        <i className=\"fas fa-clock mr-2 text-blue-600\"></i>\n                                        {formatTime(news.created_at || news.date)}\n                                    </div>\n                                    <div className=\"flex items-center\">\n                                        <i className=\"fas fa-eye mr-2 text-blue-600\"></i>\n                                        {news.views || 0} views\n                                    </div>\n                                </div>\n\n                                {/* Article Body */}\n                                <div className=\"prose prose-lg max-w-none\">\n                                    <div \n                                        className=\"text-gray-700 leading-relaxed whitespace-pre-line\"\n                                        dangerouslySetInnerHTML={{ \n                                            __html: news.content.replace(/\\n/g, '<br>') \n                                        }}\n                                    />\n                                </div>\n\n                                {/* Share Buttons */}\n                                <div className=\"mt-8 pt-6 border-t\">\n                                    <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">Bagikan Artikel</h3>\n                                    <div className=\"flex space-x-3\">\n                                        <button className=\"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors\">\n                                            <i className=\"fab fa-facebook-f mr-2\"></i>\n                                            Facebook\n                                        </button>\n                                        <button className=\"bg-blue-400 text-white px-4 py-2 rounded-lg hover:bg-blue-500 transition-colors\">\n                                            <i className=\"fab fa-twitter mr-2\"></i>\n                                            Twitter\n                                        </button>\n                                        <button className=\"bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors\">\n                                            <i className=\"fab fa-whatsapp mr-2\"></i>\n                                            WhatsApp\n                                        </button>\n                                    </div>\n                                </div>\n                            </div>\n                        </article>\n                    </div>\n\n                    {/* Sidebar */}\n                    <div className=\"lg:col-span-1\">\n                        {/* Related News */}\n                        {relatedNews.length > 0 && (\n                            <div className=\"bg-white rounded-lg shadow-md p-6 mb-6\">\n                                <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">\n                                    <i className=\"fas fa-newspaper mr-2 text-blue-600\"></i>\n                                    Berita Terkait\n                                </h3>\n                                <div className=\"space-y-4\">\n                                    {relatedNews.map((item) => (\n                                        <div \n                                            key={item.id}\n                                            onClick={() => handleRelatedNewsClick(item.id)}\n                                            className=\"flex cursor-pointer hover:bg-gray-50 p-2 rounded-lg transition-colors\"\n                                        >\n                                            <img \n                                                src={getImageUrl(item.image)} \n                                                alt={item.title}\n                                                className=\"w-16 h-16 object-cover rounded-lg mr-3 flex-shrink-0\"\n                                                onError={(e) => {\n                                                    e.target.src = 'https://via.placeholder.com/150x150/E5E7EB/6B7280?text=No+Image';\n                                                }}\n                                            />\n                                            <div className=\"flex-1 min-w-0\">\n                                                <h4 className=\"text-sm font-medium text-gray-900 line-clamp-2 mb-1\">\n                                                    {item.title}\n                                                </h4>\n                                                <p className=\"text-xs text-gray-600\">\n                                                    {formatDate(item.created_at || item.date)}\n                                                </p>\n                                            </div>\n                                        </div>\n                                    ))}\n                                </div>\n                            </div>\n                        )}\n\n                        {/* Back to Home Button */}\n                        <div className=\"bg-white rounded-lg shadow-md p-6\">\n                            <button \n                                onClick={handleBackToHome}\n                                className=\"w-full bg-blue-600 text-white py-3 px-4 rounded-lg hover:bg-blue-700 transition-colors font-medium\"\n                            >\n                                <i className=\"fas fa-arrow-left mr-2\"></i>\n                                Kembali ke Beranda\n                            </button>\n                        </div>\n                    </div>\n                </div>\n            </div>\n\n            {/* Custom Bottom Navigation */}\n            <Box sx={{\n                position: 'fixed',\n                left: 0,\n                right: 0,\n                bottom: 0,\n                zIndex: 1300,\n                display: 'block',\n                backgroundColor: 'white',\n                borderTop: '1px solid #e0e0e0',\n                boxShadow: '0 -2px 10px rgba(0,0,0,0.1)'\n            }}>\n                <Box sx={{\n                    display: 'flex',\n                    justifyContent: 'space-around',\n                    alignItems: 'center',\n                    height: 64,\n                    px: 1\n                }}>\n                    <Box\n                        onClick={() => navigate('/')}\n                        className=\"bottom-nav-item active\"\n                        style={{ cursor: 'pointer' }}\n                    >\n                        <i className=\"fas fa-home bottom-nav-icon text-blue-600\"></i>\n                        <Typography variant=\"caption\" className=\"bottom-nav-label\" sx={{ color: 'primary.main' }}>\n                            Home\n                        </Typography>\n                    </Box>\n\n                    <Box\n                        onClick={() => navigate('/')}\n                        className=\"bottom-nav-item\"\n                        style={{ cursor: 'pointer' }}\n                    >\n                        <i className=\"fas fa-search bottom-nav-icon text-gray-500\"></i>\n                        <Typography variant=\"caption\" className=\"bottom-nav-label\" sx={{ color: 'text.secondary' }}>\n                            Cari\n                        </Typography>\n                    </Box>\n\n                    <Box\n                        onClick={() => navigate('/saved')}\n                        className=\"bottom-nav-item\"\n                        style={{ cursor: 'pointer' }}\n                    >\n                        <i className=\"fas fa-bookmark bottom-nav-icon text-gray-500\"></i>\n                        <Typography variant=\"caption\" className=\"bottom-nav-label\" sx={{ color: 'text.secondary' }}>\n                            Simpan\n                        </Typography>\n                    </Box>\n                </Box>\n            </Box>\n        </div>\n    );\n};\n\nexport default DataNews;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,SAAS,EAAEC,WAAW,QAAQ,kBAAkB;AACzD,OAAOC,MAAM,MAAM,qBAAqB;AACxC,OAAOC,GAAG,MAAM,mBAAmB;AACnC,OAAOC,UAAU,MAAM,0BAA0B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElD,MAAMC,QAAQ,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACnB,MAAM;IAAEC;EAAG,CAAC,GAAGT,SAAS,CAAC,CAAC;EAC1B,MAAMU,QAAQ,GAAGT,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACU,IAAI,EAAEC,OAAO,CAAC,GAAGd,QAAQ,CAAC,IAAI,CAAC;EACtC,MAAM,CAACe,WAAW,EAAEC,cAAc,CAAC,GAAGhB,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACiB,OAAO,EAAEC,UAAU,CAAC,GAAGlB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACmB,KAAK,EAAEC,QAAQ,CAAC,GAAGpB,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAACqB,SAAS,EAAEC,YAAY,CAAC,GAAGtB,QAAQ,CAAC,CAAC,CAAC;EAE7CC,SAAS,CAAC,MAAM;IACZ,IAAIU,EAAE,EAAE;MACJY,eAAe,CAACZ,EAAE,CAAC;IACvB;EACJ,CAAC,EAAE,CAACA,EAAE,CAAC,CAAC;EAER,MAAMY,eAAe,GAAG,MAAOC,MAAM,IAAK;IACtC,IAAI;MACAN,UAAU,CAAC,IAAI,CAAC;;MAEhB;MACA,IAAI;QACA,MAAMO,KAAK,CAAC,yEAAyED,MAAM,EAAE,EAAE;UAC3FE,MAAM,EAAE;QACZ,CAAC,CAAC;MACN,CAAC,CAAC,OAAOC,SAAS,EAAE;QAChBC,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEF,SAAS,CAAC;MACxD;;MAEA;MACA,MAAMG,QAAQ,GAAG,MAAML,KAAK,CAAC,wEAAwED,MAAM,EAAE,CAAC;MAE9G,IAAI,CAACM,QAAQ,CAACC,EAAE,EAAE;QACd,MAAM,IAAIC,KAAK,CAAC,uBAAuBF,QAAQ,CAACG,MAAM,EAAE,CAAC;MAC7D;MAEA,MAAMC,IAAI,GAAG,MAAMJ,QAAQ,CAACK,IAAI,CAAC,CAAC;MAElC,IAAID,IAAI,CAACE,OAAO,IAAIF,IAAI,CAACA,IAAI,EAAE;QAC3BpB,OAAO,CAACoB,IAAI,CAACA,IAAI,CAAC;QAClB;QACA,IAAIA,IAAI,CAACA,IAAI,CAACG,WAAW,EAAE;UACvBC,gBAAgB,CAACJ,IAAI,CAACA,IAAI,CAACG,WAAW,EAAEb,MAAM,CAAC;QACnD;MACJ,CAAC,MAAM;QACHJ,QAAQ,CAAC,wBAAwB,CAAC;MACtC;IACJ,CAAC,CAAC,OAAOD,KAAK,EAAE;MACZS,OAAO,CAACT,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC5CC,QAAQ,CAAC,yCAAyC,CAAC;IACvD,CAAC,SAAS;MACNF,UAAU,CAAC,KAAK,CAAC;IACrB;EACJ,CAAC;EAED,MAAMoB,gBAAgB,GAAG,MAAAA,CAAOC,UAAU,EAAEC,aAAa,KAAK;IAC1D,IAAI;MACA,MAAMV,QAAQ,GAAG,MAAML,KAAK,CAAC,wEAAwEc,UAAU,UAAU,CAAC;MAE1H,IAAI,CAACT,QAAQ,CAACC,EAAE,EAAE;QACd,MAAM,IAAIC,KAAK,CAAC,uBAAuBF,QAAQ,CAACG,MAAM,EAAE,CAAC;MAC7D;MAEA,MAAMC,IAAI,GAAG,MAAMJ,QAAQ,CAACK,IAAI,CAAC,CAAC;MAElC,IAAID,IAAI,CAACE,OAAO,IAAIK,KAAK,CAACC,OAAO,CAACR,IAAI,CAACA,IAAI,CAAC,EAAE;QAC1C;QACA,MAAMS,QAAQ,GAAGT,IAAI,CAACA,IAAI,CACrBU,MAAM,CAACC,IAAI,IAAIA,IAAI,CAAClC,EAAE,KAAKmC,QAAQ,CAACN,aAAa,CAAC,CAAC,CACnDO,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;QAChB/B,cAAc,CAAC2B,QAAQ,CAAC;MAC5B,CAAC,MAAM;QACH3B,cAAc,CAAC,EAAE,CAAC;MACtB;IACJ,CAAC,CAAC,OAAOG,KAAK,EAAE;MACZS,OAAO,CAACT,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACpDH,cAAc,CAAC,EAAE,CAAC;IACtB;EACJ,CAAC;EAED,MAAMgC,WAAW,GAAIC,SAAS,IAAK;IAC/B,IAAI,CAACA,SAAS,EAAE,OAAO,iEAAiE;IACxF,IAAIA,SAAS,CAACC,UAAU,CAAC,MAAM,CAAC,EAAE,OAAOD,SAAS;IAClD,IAAIA,SAAS,CAACC,UAAU,CAAC,GAAG,CAAC,EAAE,OAAO,wBAAwBD,SAAS,EAAE;IACzE,OAAO,+CAA+CA,SAAS,EAAE;EACrE,CAAC;EAED,MAAME,UAAU,GAAIC,UAAU,IAAK;IAC/B,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAACF,UAAU,CAAC;IACjC,OAAOC,IAAI,CAACE,kBAAkB,CAAC,OAAO,EAAE;MACpCC,OAAO,EAAE,MAAM;MACfC,IAAI,EAAE,SAAS;MACfC,KAAK,EAAE,MAAM;MACbC,GAAG,EAAE;IACT,CAAC,CAAC;EACN,CAAC;EAED,MAAMC,UAAU,GAAIR,UAAU,IAAK;IAC/B,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAACF,UAAU,CAAC;IACjC,OAAOC,IAAI,CAACQ,kBAAkB,CAAC,OAAO,EAAE;MACpCC,IAAI,EAAE,SAAS;MACfC,MAAM,EAAE;IACZ,CAAC,CAAC;EACN,CAAC;EAED,MAAMC,sBAAsB,GAAIxC,MAAM,IAAK;IACvCZ,QAAQ,CAAC,cAAcY,MAAM,EAAE,CAAC;EACpC,CAAC;EAED,MAAMyC,gBAAgB,GAAGA,CAAA,KAAM;IAC3BrD,QAAQ,CAAC,GAAG,CAAC;EACjB,CAAC;EAED,IAAIK,OAAO,EAAE;IACT,oBACIT,OAAA;MAAK0D,SAAS,EAAC,0DAA0D;MAAAC,QAAA,eACrE3D,OAAA;QAAK0D,SAAS,EAAC,aAAa;QAAAC,QAAA,gBACxB3D,OAAA;UAAK0D,SAAS,EAAC;QAA6E;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACnG/D,OAAA;UAAG0D,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAAgB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAEd;EAEA,IAAIpD,KAAK,EAAE;IACP,oBACIX,OAAA;MAAK0D,SAAS,EAAC,0DAA0D;MAAAC,QAAA,eACrE3D,OAAA;QAAK0D,SAAS,EAAC,aAAa;QAAAC,QAAA,gBACxB3D,OAAA;UAAK0D,SAAS,EAAC,yEAAyE;UAAAC,QAAA,gBACpF3D,OAAA;YAAG0D,SAAS,EAAC;UAAkC;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,EACnDpD,KAAK;QAAA;UAAAiD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eACN/D,OAAA;UACIgE,OAAO,EAAEP,gBAAiB;UAC1BC,SAAS,EAAC,iFAAiF;UAAAC,QAAA,gBAE3F3D,OAAA;YAAG0D,SAAS,EAAC;UAAwB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,sBAE9C;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAEd;EAEA,IAAI,CAAC1D,IAAI,EAAE;IACP,oBACIL,OAAA;MAAK0D,SAAS,EAAC,0DAA0D;MAAAC,QAAA,eACrE3D,OAAA;QAAK0D,SAAS,EAAC,aAAa;QAAAC,QAAA,gBACxB3D,OAAA;UAAG0D,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAAsB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACvD/D,OAAA;UACIgE,OAAO,EAAEP,gBAAiB;UAC1BC,SAAS,EAAC,sFAAsF;UAAAC,QAAA,gBAEhG3D,OAAA;YAAG0D,SAAS,EAAC;UAAwB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,sBAE9C;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAEd;EAEA,oBACI/D,OAAA;IAAK0D,SAAS,EAAC,yBAAyB;IAAAC,QAAA,gBAEpC3D,OAAA,CAACJ,MAAM;MAAAgE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAGV/D,OAAA;MAAK0D,SAAS,EAAC,6BAA6B;MAAAC,QAAA,eACxC3D,OAAA;QAAK0D,SAAS,EAAC,6BAA6B;QAAAC,QAAA,eACxC3D,OAAA;UAAK0D,SAAS,EAAC,mDAAmD;UAAAC,QAAA,gBAC9D3D,OAAA;YACIgE,OAAO,EAAEP,gBAAiB;YAC1BC,SAAS,EAAC,uCAAuC;YAAAC,QAAA,gBAEjD3D,OAAA;cAAG0D,SAAS,EAAC;YAAkB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,WAExC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACT/D,OAAA;YAAG0D,SAAS,EAAC;UAA8B;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAChD/D,OAAA;YAAM0D,SAAS,EAAC,2BAA2B;YAAAC,QAAA,EAAC;UAAa;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/D;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,eAGN/D,OAAA;MAAK0D,SAAS,EAAC,6BAA6B;MAAAC,QAAA,eACxC3D,OAAA;QAAK0D,SAAS,EAAC,uCAAuC;QAAAC,QAAA,gBAElD3D,OAAA;UAAK0D,SAAS,EAAC,eAAe;UAAAC,QAAA,eAC1B3D,OAAA;YAAS0D,SAAS,EAAC,+CAA+C;YAAAC,QAAA,gBAE9D3D,OAAA;cAAK0D,SAAS,EAAC,UAAU;cAAAC,QAAA,gBACrB3D,OAAA;gBACIiE,GAAG,EAAEzB,WAAW,CAACnC,IAAI,CAAC6D,KAAK,CAAE;gBAC7BC,GAAG,EAAE9D,IAAI,CAAC+D,KAAM;gBAChBV,SAAS,EAAC,kCAAkC;gBAC5CW,OAAO,EAAGC,CAAC,IAAK;kBACZA,CAAC,CAACC,MAAM,CAACN,GAAG,GAAG,iEAAiE;gBACpF;cAAE;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,eACF/D,OAAA;gBAAK0D,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,eAClC3D,OAAA;kBAAM0D,SAAS,EAAC,mEAAmE;kBAAAC,QAAA,EAC9EtD,IAAI,CAACmE,aAAa,IAAI;gBAAQ;kBAAAZ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7B;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,eAGN/D,OAAA;cAAK0D,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACvB3D,OAAA;gBAAI0D,SAAS,EAAC,iEAAiE;gBAAAC,QAAA,EAC1EtD,IAAI,CAAC+D;cAAK;gBAAAR,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACX,CAAC,eAGL/D,OAAA;gBAAK0D,SAAS,EAAC,4EAA4E;gBAAAC,QAAA,gBACvF3D,OAAA;kBAAK0D,SAAS,EAAC,mBAAmB;kBAAAC,QAAA,gBAC9B3D,OAAA;oBAAG0D,SAAS,EAAC;kBAAwC;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,EACzDpB,UAAU,CAACtC,IAAI,CAACoE,UAAU,IAAIpE,IAAI,CAACwC,IAAI,CAAC;gBAAA;kBAAAe,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxC,CAAC,eACN/D,OAAA;kBAAK0D,SAAS,EAAC,mBAAmB;kBAAAC,QAAA,gBAC9B3D,OAAA;oBAAG0D,SAAS,EAAC;kBAAiC;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,EAClDX,UAAU,CAAC/C,IAAI,CAACoE,UAAU,IAAIpE,IAAI,CAACwC,IAAI,CAAC;gBAAA;kBAAAe,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxC,CAAC,eACN/D,OAAA;kBAAK0D,SAAS,EAAC,mBAAmB;kBAAAC,QAAA,gBAC9B3D,OAAA;oBAAG0D,SAAS,EAAC;kBAA+B;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,EAChD1D,IAAI,CAACqE,KAAK,IAAI,CAAC,EAAC,QACrB;gBAAA;kBAAAd,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,eAGN/D,OAAA;gBAAK0D,SAAS,EAAC,2BAA2B;gBAAAC,QAAA,eACtC3D,OAAA;kBACI0D,SAAS,EAAC,mDAAmD;kBAC7DiB,uBAAuB,EAAE;oBACrBC,MAAM,EAAEvE,IAAI,CAACwE,OAAO,CAACC,OAAO,CAAC,KAAK,EAAE,MAAM;kBAC9C;gBAAE;kBAAAlB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC,eAGN/D,OAAA;gBAAK0D,SAAS,EAAC,oBAAoB;gBAAAC,QAAA,gBAC/B3D,OAAA;kBAAI0D,SAAS,EAAC,0CAA0C;kBAAAC,QAAA,EAAC;gBAAe;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC7E/D,OAAA;kBAAK0D,SAAS,EAAC,gBAAgB;kBAAAC,QAAA,gBAC3B3D,OAAA;oBAAQ0D,SAAS,EAAC,iFAAiF;oBAAAC,QAAA,gBAC/F3D,OAAA;sBAAG0D,SAAS,EAAC;oBAAwB;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,YAE9C;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACT/D,OAAA;oBAAQ0D,SAAS,EAAC,iFAAiF;oBAAAC,QAAA,gBAC/F3D,OAAA;sBAAG0D,SAAS,EAAC;oBAAqB;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,WAE3C;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACT/D,OAAA;oBAAQ0D,SAAS,EAAC,mFAAmF;oBAAAC,QAAA,gBACjG3D,OAAA;sBAAG0D,SAAS,EAAC;oBAAsB;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,YAE5C;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACR,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eAGN/D,OAAA;UAAK0D,SAAS,EAAC,eAAe;UAAAC,QAAA,GAEzBpD,WAAW,CAACwE,MAAM,GAAG,CAAC,iBACnB/E,OAAA;YAAK0D,SAAS,EAAC,wCAAwC;YAAAC,QAAA,gBACnD3D,OAAA;cAAI0D,SAAS,EAAC,0CAA0C;cAAAC,QAAA,gBACpD3D,OAAA;gBAAG0D,SAAS,EAAC;cAAqC;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,kBAE3D;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACL/D,OAAA;cAAK0D,SAAS,EAAC,WAAW;cAAAC,QAAA,EACrBpD,WAAW,CAACyE,GAAG,CAAE3C,IAAI,iBAClBrC,OAAA;gBAEIgE,OAAO,EAAEA,CAAA,KAAMR,sBAAsB,CAACnB,IAAI,CAAClC,EAAE,CAAE;gBAC/CuD,SAAS,EAAC,uEAAuE;gBAAAC,QAAA,gBAEjF3D,OAAA;kBACIiE,GAAG,EAAEzB,WAAW,CAACH,IAAI,CAAC6B,KAAK,CAAE;kBAC7BC,GAAG,EAAE9B,IAAI,CAAC+B,KAAM;kBAChBV,SAAS,EAAC,sDAAsD;kBAChEW,OAAO,EAAGC,CAAC,IAAK;oBACZA,CAAC,CAACC,MAAM,CAACN,GAAG,GAAG,iEAAiE;kBACpF;gBAAE;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC,eACF/D,OAAA;kBAAK0D,SAAS,EAAC,gBAAgB;kBAAAC,QAAA,gBAC3B3D,OAAA;oBAAI0D,SAAS,EAAC,qDAAqD;oBAAAC,QAAA,EAC9DtB,IAAI,CAAC+B;kBAAK;oBAAAR,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACX,CAAC,eACL/D,OAAA;oBAAG0D,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,EAC/BhB,UAAU,CAACN,IAAI,CAACoC,UAAU,IAAIpC,IAAI,CAACQ,IAAI;kBAAC;oBAAAe,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1C,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA,GAnBD1B,IAAI,CAAClC,EAAE;gBAAAyD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAoBX,CACR;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CACR,eAGD/D,OAAA;YAAK0D,SAAS,EAAC,mCAAmC;YAAAC,QAAA,eAC9C3D,OAAA;cACIgE,OAAO,EAAEP,gBAAiB;cAC1BC,SAAS,EAAC,oGAAoG;cAAAC,QAAA,gBAE9G3D,OAAA;gBAAG0D,SAAS,EAAC;cAAwB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,sBAE9C;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,eAGN/D,OAAA,CAACH,GAAG;MAACoF,EAAE,EAAE;QACLC,QAAQ,EAAE,OAAO;QACjBC,IAAI,EAAE,CAAC;QACPC,KAAK,EAAE,CAAC;QACRC,MAAM,EAAE,CAAC;QACTC,MAAM,EAAE,IAAI;QACZC,OAAO,EAAE,OAAO;QAChBC,eAAe,EAAE,OAAO;QACxBC,SAAS,EAAE,mBAAmB;QAC9BC,SAAS,EAAE;MACf,CAAE;MAAA/B,QAAA,eACE3D,OAAA,CAACH,GAAG;QAACoF,EAAE,EAAE;UACLM,OAAO,EAAE,MAAM;UACfI,cAAc,EAAE,cAAc;UAC9BC,UAAU,EAAE,QAAQ;UACpBC,MAAM,EAAE,EAAE;UACVC,EAAE,EAAE;QACR,CAAE;QAAAnC,QAAA,gBACE3D,OAAA,CAACH,GAAG;UACAmE,OAAO,EAAEA,CAAA,KAAM5D,QAAQ,CAAC,GAAG,CAAE;UAC7BsD,SAAS,EAAC,wBAAwB;UAClCqC,KAAK,EAAE;YAAEC,MAAM,EAAE;UAAU,CAAE;UAAArC,QAAA,gBAE7B3D,OAAA;YAAG0D,SAAS,EAAC;UAA2C;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC7D/D,OAAA,CAACF,UAAU;YAACmG,OAAO,EAAC,SAAS;YAACvC,SAAS,EAAC,kBAAkB;YAACuB,EAAE,EAAE;cAAEiB,KAAK,EAAE;YAAe,CAAE;YAAAvC,QAAA,EAAC;UAE1F;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACZ,CAAC,eAEN/D,OAAA,CAACH,GAAG;UACAmE,OAAO,EAAEA,CAAA,KAAM5D,QAAQ,CAAC,GAAG,CAAE;UAC7BsD,SAAS,EAAC,iBAAiB;UAC3BqC,KAAK,EAAE;YAAEC,MAAM,EAAE;UAAU,CAAE;UAAArC,QAAA,gBAE7B3D,OAAA;YAAG0D,SAAS,EAAC;UAA6C;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC/D/D,OAAA,CAACF,UAAU;YAACmG,OAAO,EAAC,SAAS;YAACvC,SAAS,EAAC,kBAAkB;YAACuB,EAAE,EAAE;cAAEiB,KAAK,EAAE;YAAiB,CAAE;YAAAvC,QAAA,EAAC;UAE5F;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACZ,CAAC,eAEN/D,OAAA,CAACH,GAAG;UACAmE,OAAO,EAAEA,CAAA,KAAM5D,QAAQ,CAAC,QAAQ,CAAE;UAClCsD,SAAS,EAAC,iBAAiB;UAC3BqC,KAAK,EAAE;YAAEC,MAAM,EAAE;UAAU,CAAE;UAAArC,QAAA,gBAE7B3D,OAAA;YAAG0D,SAAS,EAAC;UAA+C;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACjE/D,OAAA,CAACF,UAAU;YAACmG,OAAO,EAAC,SAAS;YAACvC,SAAS,EAAC,kBAAkB;YAACuB,EAAE,EAAE;cAAEiB,KAAK,EAAE;YAAiB,CAAE;YAAAvC,QAAA,EAAC;UAE5F;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACZ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEd,CAAC;AAAC7D,EAAA,CA9WID,QAAQ;EAAA,QACKP,SAAS,EACPC,WAAW;AAAA;AAAAwG,EAAA,GAF1BlG,QAAQ;AAgXd,eAAeA,QAAQ;AAAC,IAAAkG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}