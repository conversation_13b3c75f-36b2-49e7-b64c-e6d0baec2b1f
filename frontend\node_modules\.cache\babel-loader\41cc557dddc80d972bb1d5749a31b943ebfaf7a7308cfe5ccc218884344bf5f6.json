{"ast": null, "code": "var _jsxFileName = \"C:\\\\laragon\\\\www\\\\react-news\\\\frontend\\\\src\\\\pages\\\\user\\\\data-news.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useParams, useNavigate } from 'react-router-dom';\nimport Box from '@mui/material/Box';\nimport Typography from '@mui/material/Typography';\nimport AppBar from '@mui/material/AppBar';\nimport Toolbar from '@mui/material/Toolbar';\nimport Avatar from '@mui/material/Avatar';\nimport IconButton from '@mui/material/IconButton';\nimport MenuIcon from '@mui/icons-material/Menu';\nimport useMediaQuery from '@mui/material/useMediaQuery';\nimport { useTheme } from '@mui/material/styles';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst DataNews = () => {\n  _s();\n  // Data News Component - Fixed Version\n  const {\n    id\n  } = useParams();\n  const navigate = useNavigate();\n  const theme = useTheme();\n  const isDesktop = useMediaQuery(theme.breakpoints.up('md'));\n  const [news, setNews] = useState(null);\n  const [relatedNews, setRelatedNews] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [kostum, setKostum] = useState({\n    logo: '',\n    title: 'React News Portal'\n  });\n  const [bottomNav, setBottomNav] = useState(0);\n  useEffect(() => {\n    if (id) {\n      fetchNewsDetail(id);\n    }\n    // Fetch kostum data\n    fetchKostumData();\n  }, [id]);\n  const fetchKostumData = async () => {\n    try {\n      const response = await fetch('http://localhost:5000/api/kostum');\n      const data = await response.json();\n      if (data.logo || data.title) {\n        setKostum({\n          logo: data.logo || '',\n          title: data.title || 'React News Portal'\n        });\n      }\n    } catch (error) {\n      console.log('Could not fetch kostum data:', error);\n    }\n  };\n  const fetchNewsDetail = async newsId => {\n    try {\n      setLoading(true);\n\n      // Fetch news detail (views already incremented from landing page)\n      const response = await fetch(`http://localhost/react-news/frontend/src/pages/admin/api.php?action=get_news_by_id&id=${newsId}`);\n      if (!response.ok) {\n        throw new Error(`HTTP error! status: ${response.status}`);\n      }\n      const data = await response.json();\n      if (data.success && data.data) {\n        setNews(data.data);\n        // Fetch related news\n        if (data.data.category_id) {\n          fetchRelatedNews(data.data.category_id, newsId);\n        }\n      } else {\n        setError('Berita tidak ditemukan');\n      }\n    } catch (error) {\n      console.error('Error fetching news:', error);\n      setError('Gagal memuat berita. Silakan coba lagi.');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const fetchRelatedNews = async (categoryId, currentNewsId) => {\n    try {\n      const response = await fetch(`http://localhost/react-news/frontend/src/pages/admin/api.php?action=get_news&category=${categoryId}&limit=4`);\n      if (!response.ok) {\n        throw new Error(`HTTP error! status: ${response.status}`);\n      }\n      const data = await response.json();\n      if (data.success && Array.isArray(data.data)) {\n        // Filter out current news and limit to 3\n        const filtered = data.data.filter(item => item.id !== parseInt(currentNewsId)).slice(0, 3);\n        setRelatedNews(filtered);\n      } else {\n        setRelatedNews([]);\n      }\n    } catch (error) {\n      console.error('Error fetching related news:', error);\n      setRelatedNews([]);\n    }\n  };\n  const getImageUrl = imagePath => {\n    if (!imagePath) return 'https://picsum.photos/800/400?random=1';\n    // Jika sudah URL lengkap, gunakan langsung\n    if (imagePath.startsWith('http')) return imagePath;\n    // Jika path relatif, gabungkan dengan base URL\n    if (imagePath.startsWith('/')) return `http://localhost:3000${imagePath}`;\n    // Path gambar dari database posts - ambil dari URL database\n    return `http://localhost/react-news/frontend/src/pages/admin/assets/news/${imagePath}`;\n  };\n  const formatDate = dateString => {\n    const date = new Date(dateString);\n    return date.toLocaleDateString('id-ID', {\n      weekday: 'long',\n      year: 'numeric',\n      month: 'long',\n      day: 'numeric'\n    });\n  };\n  const formatTime = dateString => {\n    const date = new Date(dateString);\n    return date.toLocaleTimeString('id-ID', {\n      hour: '2-digit',\n      minute: '2-digit'\n    });\n  };\n  const handleRelatedNewsClick = newsId => {\n    navigate(`/data-news/${newsId}`);\n  };\n  const handleBackToHome = () => {\n    navigate('/');\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gray-50 flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 144,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600\",\n          children: \"Memuat berita...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 145,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 143,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 142,\n      columnNumber: 13\n    }, this);\n  }\n  if (error) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gray-50 flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-red-100 border border-red-400 text-red-700 px-6 py-4 rounded-lg mb-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"fas fa-exclamation-triangle mr-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 156,\n            columnNumber: 25\n          }, this), error]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 155,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: handleBackToHome,\n          className: \"bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors\",\n          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"fas fa-arrow-left mr-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 163,\n            columnNumber: 25\n          }, this), \"Kembali ke Beranda\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 159,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 154,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 153,\n      columnNumber: 13\n    }, this);\n  }\n  if (!news) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gray-50 flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600\",\n          children: \"Berita tidak ditemukan\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 175,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: handleBackToHome,\n          className: \"mt-4 bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors\",\n          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"fas fa-arrow-left mr-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 180,\n            columnNumber: 25\n          }, this), \"Kembali ke Beranda\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 176,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 174,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 173,\n      columnNumber: 13\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      minHeight: '100vh',\n      bgcolor: 'gray.50',\n      width: '100vw',\n      overflow: 'hidden'\n    },\n    children: [/*#__PURE__*/_jsxDEV(AppBar, {\n      position: \"fixed\",\n      color: \"inherit\",\n      elevation: 1,\n      sx: {\n        bgcolor: '#fff',\n        borderBottom: 1,\n        borderColor: 'grey.200',\n        zIndex: 1301\n      },\n      children: /*#__PURE__*/_jsxDEV(Toolbar, {\n        sx: {\n          minHeight: {\n            xs: 70,\n            md: 80\n          },\n          px: {\n            xs: 2,\n            md: 6\n          }\n        },\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            alignItems: 'center',\n            flexGrow: 1\n          },\n          children: [/*#__PURE__*/_jsxDEV(Avatar, {\n            src: kostum.logo,\n            alt: \"Logo\",\n            sx: {\n              width: 48,\n              height: 48,\n              mr: 2\n            },\n            onError: e => {\n              e.target.src = '/logo192.png';\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 194,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            sx: {\n              fontWeight: 700,\n              color: 'primary.main',\n              fontSize: {\n                xs: 22,\n                md: 28\n              }\n            },\n            children: kostum.title\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 200,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 193,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n          edge: \"end\",\n          color: \"primary\",\n          onClick: () => navigate('/'),\n          sx: {\n            mr: 1\n          },\n          children: /*#__PURE__*/_jsxDEV(MenuIcon, {\n            fontSize: \"large\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 210,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 204,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 192,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 191,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        bgcolor: 'white',\n        boxShadow: 1,\n        borderBottom: 1,\n        borderColor: 'grey.200',\n        mt: {\n          xs: '70px',\n          md: '80px'\n        }\n      },\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          maxWidth: '1200px',\n          mx: 'auto',\n          px: 2,\n          py: 2\n        },\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            alignItems: 'center',\n            gap: 1,\n            fontSize: '14px',\n            color: 'text.secondary'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Box, {\n            component: \"button\",\n            onClick: handleBackToHome,\n            sx: {\n              display: 'flex',\n              alignItems: 'center',\n              color: 'text.secondary',\n              '&:hover': {\n                color: 'primary.main'\n              },\n              transition: 'color 0.2s',\n              border: 'none',\n              background: 'none',\n              cursor: 'pointer'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"fas fa-home\",\n              style: {\n                marginRight: '4px'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 233,\n              columnNumber: 29\n            }, this), \"Beranda\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 219,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"fas fa-chevron-right\",\n            style: {\n              fontSize: '12px'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 236,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            sx: {\n              fontWeight: 500,\n              color: 'text.primary'\n            },\n            children: \"Detail Berita\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 237,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 218,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 217,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 216,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        maxWidth: '1200px',\n        mx: 'auto',\n        px: 2,\n        py: 4,\n        pb: {\n          xs: 12,\n          md: 4\n        } // More padding on mobile for bottom nav\n      },\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'grid',\n          gridTemplateColumns: {\n            xs: '1fr',\n            lg: '2fr 1fr'\n          },\n          gap: 4\n        },\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          children: /*#__PURE__*/_jsxDEV(\"article\", {\n            className: \"bg-white rounded-lg shadow-md overflow-hidden\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"relative\",\n              children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                src: getImageUrl(news.image),\n                alt: news.title,\n                className: \"w-full h-64 md:h-80 object-cover\",\n                onError: e => {\n                  e.target.src = 'https://picsum.photos/800/400?random=2';\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 258,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"absolute top-4 left-4\",\n                children: /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"bg-blue-600 text-white px-3 py-1 rounded-full text-sm font-medium\",\n                  children: news.category_name || 'Berita'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 267,\n                  columnNumber: 37\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 266,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 257,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-6 md:p-8\",\n              children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n                className: \"text-2xl md:text-3xl font-bold text-gray-900 mb-4 leading-tight\",\n                children: news.title\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 275,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex flex-wrap items-center gap-4 text-sm text-gray-600 mb-6 pb-6 border-b\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                    className: \"fas fa-calendar-alt mr-2 text-blue-600\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 282,\n                    columnNumber: 41\n                  }, this), formatDate(news.created_at || news.date)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 281,\n                  columnNumber: 37\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                    className: \"fas fa-clock mr-2 text-blue-600\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 286,\n                    columnNumber: 41\n                  }, this), formatTime(news.created_at || news.date)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 285,\n                  columnNumber: 37\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                    className: \"fas fa-eye mr-2 text-blue-600\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 290,\n                    columnNumber: 41\n                  }, this), news.views || 0, \" views\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 289,\n                  columnNumber: 37\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 280,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"prose prose-lg max-w-none\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-gray-700 leading-relaxed whitespace-pre-line\",\n                  dangerouslySetInnerHTML: {\n                    __html: news.content.replace(/\\n/g, '<br>')\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 297,\n                  columnNumber: 37\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 296,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mt-8 pt-6 border-t\",\n                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"text-lg font-semibold text-gray-900 mb-4\",\n                  children: \"Bagikan Artikel\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 307,\n                  columnNumber: 37\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex space-x-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors\",\n                    children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                      className: \"fab fa-facebook-f mr-2\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 310,\n                      columnNumber: 45\n                    }, this), \"Facebook\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 309,\n                    columnNumber: 41\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"bg-blue-400 text-white px-4 py-2 rounded-lg hover:bg-blue-500 transition-colors\",\n                    children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                      className: \"fab fa-twitter mr-2\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 314,\n                      columnNumber: 45\n                    }, this), \"Twitter\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 313,\n                    columnNumber: 41\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors\",\n                    children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                      className: \"fab fa-whatsapp mr-2\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 318,\n                      columnNumber: 45\n                    }, this), \"WhatsApp\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 317,\n                    columnNumber: 41\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 308,\n                  columnNumber: 37\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 306,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 274,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 255,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 254,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          children: [relatedNews.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white rounded-lg shadow-md p-6 mb-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-lg font-semibold text-gray-900 mb-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"fas fa-newspaper mr-2 text-blue-600\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 333,\n                columnNumber: 37\n              }, this), \"Berita Terkait\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 332,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-4\",\n              children: relatedNews.map(item => /*#__PURE__*/_jsxDEV(\"div\", {\n                onClick: () => handleRelatedNewsClick(item.id),\n                className: \"flex cursor-pointer hover:bg-gray-50 p-2 rounded-lg transition-colors\",\n                children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                  src: getImageUrl(item.image),\n                  alt: item.title,\n                  className: \"w-16 h-16 object-cover rounded-lg mr-3 flex-shrink-0\",\n                  onError: e => {\n                    e.target.src = 'https://picsum.photos/150/150?random=3';\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 343,\n                  columnNumber: 45\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex-1 min-w-0\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                    className: \"text-sm font-medium text-gray-900 line-clamp-2 mb-1\",\n                    children: item.title\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 352,\n                    columnNumber: 49\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-xs text-gray-600\",\n                    children: formatDate(item.created_at || item.date)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 355,\n                    columnNumber: 49\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 351,\n                  columnNumber: 45\n                }, this)]\n              }, item.id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 338,\n                columnNumber: 41\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 336,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 331,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              bgcolor: 'white',\n              borderRadius: 2,\n              boxShadow: 2,\n              p: 3\n            },\n            children: /*#__PURE__*/_jsxDEV(Box, {\n              component: \"button\",\n              onClick: handleBackToHome,\n              sx: {\n                width: '100%',\n                bgcolor: 'primary.main',\n                color: 'white',\n                py: 1.5,\n                px: 2,\n                borderRadius: 2,\n                border: 'none',\n                cursor: 'pointer',\n                fontWeight: 500,\n                transition: 'background-color 0.2s',\n                '&:hover': {\n                  bgcolor: 'primary.dark'\n                }\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"fas fa-arrow-left\",\n                style: {\n                  marginRight: '8px'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 386,\n                columnNumber: 33\n              }, this), \"Kembali ke Beranda\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 367,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 366,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 328,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 252,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 245,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        position: 'fixed',\n        left: 0,\n        right: 0,\n        bottom: 0,\n        zIndex: 1300,\n        display: {\n          xs: 'block',\n          md: 'none'\n        },\n        // Hide on desktop\n        backgroundColor: 'white',\n        borderTop: '1px solid #e0e0e0',\n        boxShadow: '0 -2px 10px rgba(0,0,0,0.1)'\n      },\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          justifyContent: 'space-around',\n          alignItems: 'center',\n          height: 64,\n          px: 1\n        },\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          onClick: () => navigate('/'),\n          className: \"bottom-nav-item active\",\n          style: {\n            cursor: 'pointer'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"fas fa-home bottom-nav-icon text-blue-600\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 418,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"caption\",\n            className: \"bottom-nav-label\",\n            sx: {\n              color: 'primary.main'\n            },\n            children: \"Home\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 419,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 413,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          onClick: () => navigate('/'),\n          className: \"bottom-nav-item\",\n          style: {\n            cursor: 'pointer'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"fas fa-search bottom-nav-icon text-gray-500\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 429,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"caption\",\n            className: \"bottom-nav-label\",\n            sx: {\n              color: 'text.secondary'\n            },\n            children: \"Cari\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 430,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 424,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          onClick: () => navigate('/saved'),\n          className: \"bottom-nav-item\",\n          style: {\n            cursor: 'pointer'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"fas fa-bookmark bottom-nav-icon text-gray-500\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 440,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"caption\",\n            className: \"bottom-nav-label\",\n            sx: {\n              color: 'text.secondary'\n            },\n            children: \"Simpan\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 441,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 435,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 406,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 395,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 189,\n    columnNumber: 9\n  }, this);\n};\n_s(DataNews, \"b9Urwv16UwQ8sxy2aM1IgkkR+Js=\", false, function () {\n  return [useParams, useNavigate, useTheme, useMediaQuery];\n});\n_c = DataNews;\nexport default DataNews;\nvar _c;\n$RefreshReg$(_c, \"DataNews\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useParams", "useNavigate", "Box", "Typography", "AppBar", "<PERSON><PERSON><PERSON>", "Avatar", "IconButton", "MenuIcon", "useMediaQuery", "useTheme", "jsxDEV", "_jsxDEV", "DataNews", "_s", "id", "navigate", "theme", "isDesktop", "breakpoints", "up", "news", "setNews", "relatedNews", "setRelatedNews", "loading", "setLoading", "error", "setError", "kostum", "setKostum", "logo", "title", "bottomNav", "setBottomNav", "fetchNewsDetail", "fetchKostumData", "response", "fetch", "data", "json", "console", "log", "newsId", "ok", "Error", "status", "success", "category_id", "fetchRelatedNews", "categoryId", "currentNewsId", "Array", "isArray", "filtered", "filter", "item", "parseInt", "slice", "getImageUrl", "imagePath", "startsWith", "formatDate", "dateString", "date", "Date", "toLocaleDateString", "weekday", "year", "month", "day", "formatTime", "toLocaleTimeString", "hour", "minute", "handleRelatedNewsClick", "handleBackToHome", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "sx", "minHeight", "bgcolor", "width", "overflow", "position", "color", "elevation", "borderBottom", "borderColor", "zIndex", "xs", "md", "px", "display", "alignItems", "flexGrow", "src", "alt", "height", "mr", "onError", "e", "target", "variant", "fontWeight", "fontSize", "edge", "boxShadow", "mt", "max<PERSON><PERSON><PERSON>", "mx", "py", "gap", "component", "transition", "border", "background", "cursor", "style", "marginRight", "pb", "gridTemplateColumns", "lg", "image", "category_name", "created_at", "views", "dangerouslySetInnerHTML", "__html", "content", "replace", "length", "map", "borderRadius", "p", "left", "right", "bottom", "backgroundColor", "borderTop", "justifyContent", "_c", "$RefreshReg$"], "sources": ["C:/laragon/www/react-news/frontend/src/pages/user/data-news.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useParams, useNavigate } from 'react-router-dom';\nimport Box from '@mui/material/Box';\nimport Typography from '@mui/material/Typography';\nimport AppBar from '@mui/material/AppBar';\nimport Toolbar from '@mui/material/Toolbar';\nimport Avatar from '@mui/material/Avatar';\nimport IconButton from '@mui/material/IconButton';\nimport MenuIcon from '@mui/icons-material/Menu';\nimport useMediaQuery from '@mui/material/useMediaQuery';\nimport { useTheme } from '@mui/material/styles';\n\nconst DataNews = () => {\n    // Data News Component - Fixed Version\n    const { id } = useParams();\n    const navigate = useNavigate();\n    const theme = useTheme();\n    const isDesktop = useMediaQuery(theme.breakpoints.up('md'));\n    const [news, setNews] = useState(null);\n    const [relatedNews, setRelatedNews] = useState([]);\n    const [loading, setLoading] = useState(true);\n    const [error, setError] = useState(null);\n    const [kostum, setKostum] = useState({ logo: '', title: 'React News Portal' });\n    const [bottomNav, setBottomNav] = useState(0);\n\n    useEffect(() => {\n        if (id) {\n            fetchNewsDetail(id);\n        }\n        // Fetch kostum data\n        fetchKostumData();\n    }, [id]);\n\n    const fetchKostumData = async () => {\n        try {\n            const response = await fetch('http://localhost:5000/api/kostum');\n            const data = await response.json();\n            if (data.logo || data.title) {\n                setKostum({\n                    logo: data.logo || '',\n                    title: data.title || 'React News Portal'\n                });\n            }\n        } catch (error) {\n            console.log('Could not fetch kostum data:', error);\n        }\n    };\n\n    const fetchNewsDetail = async (newsId) => {\n        try {\n            setLoading(true);\n\n            // Fetch news detail (views already incremented from landing page)\n            const response = await fetch(`http://localhost/react-news/frontend/src/pages/admin/api.php?action=get_news_by_id&id=${newsId}`);\n\n            if (!response.ok) {\n                throw new Error(`HTTP error! status: ${response.status}`);\n            }\n\n            const data = await response.json();\n\n            if (data.success && data.data) {\n                setNews(data.data);\n                // Fetch related news\n                if (data.data.category_id) {\n                    fetchRelatedNews(data.data.category_id, newsId);\n                }\n            } else {\n                setError('Berita tidak ditemukan');\n            }\n        } catch (error) {\n            console.error('Error fetching news:', error);\n            setError('Gagal memuat berita. Silakan coba lagi.');\n        } finally {\n            setLoading(false);\n        }\n    };\n\n    const fetchRelatedNews = async (categoryId, currentNewsId) => {\n        try {\n            const response = await fetch(`http://localhost/react-news/frontend/src/pages/admin/api.php?action=get_news&category=${categoryId}&limit=4`);\n\n            if (!response.ok) {\n                throw new Error(`HTTP error! status: ${response.status}`);\n            }\n\n            const data = await response.json();\n\n            if (data.success && Array.isArray(data.data)) {\n                // Filter out current news and limit to 3\n                const filtered = data.data\n                    .filter(item => item.id !== parseInt(currentNewsId))\n                    .slice(0, 3);\n                setRelatedNews(filtered);\n            } else {\n                setRelatedNews([]);\n            }\n        } catch (error) {\n            console.error('Error fetching related news:', error);\n            setRelatedNews([]);\n        }\n    };\n\n    const getImageUrl = (imagePath) => {\n        if (!imagePath) return 'https://picsum.photos/800/400?random=1';\n        // Jika sudah URL lengkap, gunakan langsung\n        if (imagePath.startsWith('http')) return imagePath;\n        // Jika path relatif, gabungkan dengan base URL\n        if (imagePath.startsWith('/')) return `http://localhost:3000${imagePath}`;\n        // Path gambar dari database posts - ambil dari URL database\n        return `http://localhost/react-news/frontend/src/pages/admin/assets/news/${imagePath}`;\n    };\n\n    const formatDate = (dateString) => {\n        const date = new Date(dateString);\n        return date.toLocaleDateString('id-ID', {\n            weekday: 'long',\n            year: 'numeric',\n            month: 'long',\n            day: 'numeric'\n        });\n    };\n\n    const formatTime = (dateString) => {\n        const date = new Date(dateString);\n        return date.toLocaleTimeString('id-ID', {\n            hour: '2-digit',\n            minute: '2-digit'\n        });\n    };\n\n    const handleRelatedNewsClick = (newsId) => {\n        navigate(`/data-news/${newsId}`);\n    };\n\n    const handleBackToHome = () => {\n        navigate('/');\n    };\n\n    if (loading) {\n        return (\n            <div className=\"min-h-screen bg-gray-50 flex items-center justify-center\">\n                <div className=\"text-center\">\n                    <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4\"></div>\n                    <p className=\"text-gray-600\">Memuat berita...</p>\n                </div>\n            </div>\n        );\n    }\n\n    if (error) {\n        return (\n            <div className=\"min-h-screen bg-gray-50 flex items-center justify-center\">\n                <div className=\"text-center\">\n                    <div className=\"bg-red-100 border border-red-400 text-red-700 px-6 py-4 rounded-lg mb-4\">\n                        <i className=\"fas fa-exclamation-triangle mr-2\"></i>\n                        {error}\n                    </div>\n                    <button \n                        onClick={handleBackToHome}\n                        className=\"bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors\"\n                    >\n                        <i className=\"fas fa-arrow-left mr-2\"></i>\n                        Kembali ke Beranda\n                    </button>\n                </div>\n            </div>\n        );\n    }\n\n    if (!news) {\n        return (\n            <div className=\"min-h-screen bg-gray-50 flex items-center justify-center\">\n                <div className=\"text-center\">\n                    <p className=\"text-gray-600\">Berita tidak ditemukan</p>\n                    <button \n                        onClick={handleBackToHome}\n                        className=\"mt-4 bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors\"\n                    >\n                        <i className=\"fas fa-arrow-left mr-2\"></i>\n                        Kembali ke Beranda\n                    </button>\n                </div>\n            </div>\n        );\n    }\n\n    return (\n        <Box sx={{ minHeight: '100vh', bgcolor: 'gray.50', width: '100vw', overflow: 'hidden' }}>\n            {/* Responsive Navigation Bar */}\n            <AppBar position=\"fixed\" color=\"inherit\" elevation={1} sx={{ bgcolor: '#fff', borderBottom: 1, borderColor: 'grey.200', zIndex: 1301 }}>\n                <Toolbar sx={{ minHeight: { xs: 70, md: 80 }, px: { xs: 2, md: 6 } }}>\n                    <Box sx={{ display: 'flex', alignItems: 'center', flexGrow: 1 }}>\n                        <Avatar\n                            src={kostum.logo}\n                            alt=\"Logo\"\n                            sx={{ width: 48, height: 48, mr: 2 }}\n                            onError={(e) => { e.target.src = '/logo192.png'; }}\n                        />\n                        <Typography variant=\"h6\" sx={{ fontWeight: 700, color: 'primary.main', fontSize: { xs: 22, md: 28 } }}>\n                            {kostum.title}\n                        </Typography>\n                    </Box>\n                    <IconButton\n                        edge=\"end\"\n                        color=\"primary\"\n                        onClick={() => navigate('/')}\n                        sx={{ mr: 1 }}\n                    >\n                        <MenuIcon fontSize=\"large\" />\n                    </IconButton>\n                </Toolbar>\n            </AppBar>\n\n            {/* Header/Breadcrumb */}\n            <Box sx={{ bgcolor: 'white', boxShadow: 1, borderBottom: 1, borderColor: 'grey.200', mt: { xs: '70px', md: '80px' } }}>\n                <Box sx={{ maxWidth: '1200px', mx: 'auto', px: 2, py: 2 }}>\n                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, fontSize: '14px', color: 'text.secondary' }}>\n                        <Box\n                            component=\"button\"\n                            onClick={handleBackToHome}\n                            sx={{\n                                display: 'flex',\n                                alignItems: 'center',\n                                color: 'text.secondary',\n                                '&:hover': { color: 'primary.main' },\n                                transition: 'color 0.2s',\n                                border: 'none',\n                                background: 'none',\n                                cursor: 'pointer'\n                            }}\n                        >\n                            <i className=\"fas fa-home\" style={{ marginRight: '4px' }}></i>\n                            Beranda\n                        </Box>\n                        <i className=\"fas fa-chevron-right\" style={{ fontSize: '12px' }}></i>\n                        <Typography variant=\"body2\" sx={{ fontWeight: 500, color: 'text.primary' }}>\n                            Detail Berita\n                        </Typography>\n                    </Box>\n                </Box>\n            </Box>\n\n            {/* Main Content */}\n            <Box sx={{\n                maxWidth: '1200px',\n                mx: 'auto',\n                px: 2,\n                py: 4,\n                pb: { xs: 12, md: 4 } // More padding on mobile for bottom nav\n            }}>\n                <Box sx={{ display: 'grid', gridTemplateColumns: { xs: '1fr', lg: '2fr 1fr' }, gap: 4 }}>\n                    {/* Main Article */}\n                    <Box>\n                        <article className=\"bg-white rounded-lg shadow-md overflow-hidden\">\n                            {/* Article Header */}\n                            <div className=\"relative\">\n                                <img \n                                    src={getImageUrl(news.image)} \n                                    alt={news.title}\n                                    className=\"w-full h-64 md:h-80 object-cover\"\n                                    onError={(e) => {\n                                        e.target.src = 'https://picsum.photos/800/400?random=2';\n                                    }}\n                                />\n                                <div className=\"absolute top-4 left-4\">\n                                    <span className=\"bg-blue-600 text-white px-3 py-1 rounded-full text-sm font-medium\">\n                                        {news.category_name || 'Berita'}\n                                    </span>\n                                </div>\n                            </div>\n\n                            {/* Article Content */}\n                            <div className=\"p-6 md:p-8\">\n                                <h1 className=\"text-2xl md:text-3xl font-bold text-gray-900 mb-4 leading-tight\">\n                                    {news.title}\n                                </h1>\n\n                                {/* Meta Information */}\n                                <div className=\"flex flex-wrap items-center gap-4 text-sm text-gray-600 mb-6 pb-6 border-b\">\n                                    <div className=\"flex items-center\">\n                                        <i className=\"fas fa-calendar-alt mr-2 text-blue-600\"></i>\n                                        {formatDate(news.created_at || news.date)}\n                                    </div>\n                                    <div className=\"flex items-center\">\n                                        <i className=\"fas fa-clock mr-2 text-blue-600\"></i>\n                                        {formatTime(news.created_at || news.date)}\n                                    </div>\n                                    <div className=\"flex items-center\">\n                                        <i className=\"fas fa-eye mr-2 text-blue-600\"></i>\n                                        {news.views || 0} views\n                                    </div>\n                                </div>\n\n                                {/* Article Body */}\n                                <div className=\"prose prose-lg max-w-none\">\n                                    <div \n                                        className=\"text-gray-700 leading-relaxed whitespace-pre-line\"\n                                        dangerouslySetInnerHTML={{ \n                                            __html: news.content.replace(/\\n/g, '<br>') \n                                        }}\n                                    />\n                                </div>\n\n                                {/* Share Buttons */}\n                                <div className=\"mt-8 pt-6 border-t\">\n                                    <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">Bagikan Artikel</h3>\n                                    <div className=\"flex space-x-3\">\n                                        <button className=\"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors\">\n                                            <i className=\"fab fa-facebook-f mr-2\"></i>\n                                            Facebook\n                                        </button>\n                                        <button className=\"bg-blue-400 text-white px-4 py-2 rounded-lg hover:bg-blue-500 transition-colors\">\n                                            <i className=\"fab fa-twitter mr-2\"></i>\n                                            Twitter\n                                        </button>\n                                        <button className=\"bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors\">\n                                            <i className=\"fab fa-whatsapp mr-2\"></i>\n                                            WhatsApp\n                                        </button>\n                                    </div>\n                                </div>\n                            </div>\n                        </article>\n                    </Box>\n\n                    {/* Sidebar */}\n                    <Box>\n                        {/* Related News */}\n                        {relatedNews.length > 0 && (\n                            <div className=\"bg-white rounded-lg shadow-md p-6 mb-6\">\n                                <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">\n                                    <i className=\"fas fa-newspaper mr-2 text-blue-600\"></i>\n                                    Berita Terkait\n                                </h3>\n                                <div className=\"space-y-4\">\n                                    {relatedNews.map((item) => (\n                                        <div \n                                            key={item.id}\n                                            onClick={() => handleRelatedNewsClick(item.id)}\n                                            className=\"flex cursor-pointer hover:bg-gray-50 p-2 rounded-lg transition-colors\"\n                                        >\n                                            <img \n                                                src={getImageUrl(item.image)} \n                                                alt={item.title}\n                                                className=\"w-16 h-16 object-cover rounded-lg mr-3 flex-shrink-0\"\n                                                onError={(e) => {\n                                                    e.target.src = 'https://picsum.photos/150/150?random=3';\n                                                }}\n                                            />\n                                            <div className=\"flex-1 min-w-0\">\n                                                <h4 className=\"text-sm font-medium text-gray-900 line-clamp-2 mb-1\">\n                                                    {item.title}\n                                                </h4>\n                                                <p className=\"text-xs text-gray-600\">\n                                                    {formatDate(item.created_at || item.date)}\n                                                </p>\n                                            </div>\n                                        </div>\n                                    ))}\n                                </div>\n                            </div>\n                        )}\n\n                        {/* Back to Home Button */}\n                        <Box sx={{ bgcolor: 'white', borderRadius: 2, boxShadow: 2, p: 3 }}>\n                            <Box\n                                component=\"button\"\n                                onClick={handleBackToHome}\n                                sx={{\n                                    width: '100%',\n                                    bgcolor: 'primary.main',\n                                    color: 'white',\n                                    py: 1.5,\n                                    px: 2,\n                                    borderRadius: 2,\n                                    border: 'none',\n                                    cursor: 'pointer',\n                                    fontWeight: 500,\n                                    transition: 'background-color 0.2s',\n                                    '&:hover': {\n                                        bgcolor: 'primary.dark'\n                                    }\n                                }}\n                            >\n                                <i className=\"fas fa-arrow-left\" style={{ marginRight: '8px' }}></i>\n                                Kembali ke Beranda\n                            </Box>\n                        </Box>\n                    </Box>\n                </Box>\n            </Box>\n\n            {/* Custom Bottom Navigation - Mobile Only */}\n            <Box sx={{\n                position: 'fixed',\n                left: 0,\n                right: 0,\n                bottom: 0,\n                zIndex: 1300,\n                display: { xs: 'block', md: 'none' }, // Hide on desktop\n                backgroundColor: 'white',\n                borderTop: '1px solid #e0e0e0',\n                boxShadow: '0 -2px 10px rgba(0,0,0,0.1)'\n            }}>\n                <Box sx={{\n                    display: 'flex',\n                    justifyContent: 'space-around',\n                    alignItems: 'center',\n                    height: 64,\n                    px: 1\n                }}>\n                    <Box\n                        onClick={() => navigate('/')}\n                        className=\"bottom-nav-item active\"\n                        style={{ cursor: 'pointer' }}\n                    >\n                        <i className=\"fas fa-home bottom-nav-icon text-blue-600\"></i>\n                        <Typography variant=\"caption\" className=\"bottom-nav-label\" sx={{ color: 'primary.main' }}>\n                            Home\n                        </Typography>\n                    </Box>\n\n                    <Box\n                        onClick={() => navigate('/')}\n                        className=\"bottom-nav-item\"\n                        style={{ cursor: 'pointer' }}\n                    >\n                        <i className=\"fas fa-search bottom-nav-icon text-gray-500\"></i>\n                        <Typography variant=\"caption\" className=\"bottom-nav-label\" sx={{ color: 'text.secondary' }}>\n                            Cari\n                        </Typography>\n                    </Box>\n\n                    <Box\n                        onClick={() => navigate('/saved')}\n                        className=\"bottom-nav-item\"\n                        style={{ cursor: 'pointer' }}\n                    >\n                        <i className=\"fas fa-bookmark bottom-nav-icon text-gray-500\"></i>\n                        <Typography variant=\"caption\" className=\"bottom-nav-label\" sx={{ color: 'text.secondary' }}>\n                            Simpan\n                        </Typography>\n                    </Box>\n                </Box>\n            </Box>\n        </Box>\n    );\n};\n\nexport default DataNews;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,SAAS,EAAEC,WAAW,QAAQ,kBAAkB;AACzD,OAAOC,GAAG,MAAM,mBAAmB;AACnC,OAAOC,UAAU,MAAM,0BAA0B;AACjD,OAAOC,MAAM,MAAM,sBAAsB;AACzC,OAAOC,OAAO,MAAM,uBAAuB;AAC3C,OAAOC,MAAM,MAAM,sBAAsB;AACzC,OAAOC,UAAU,MAAM,0BAA0B;AACjD,OAAOC,QAAQ,MAAM,0BAA0B;AAC/C,OAAOC,aAAa,MAAM,6BAA6B;AACvD,SAASC,QAAQ,QAAQ,sBAAsB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEhD,MAAMC,QAAQ,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACnB;EACA,MAAM;IAAEC;EAAG,CAAC,GAAGf,SAAS,CAAC,CAAC;EAC1B,MAAMgB,QAAQ,GAAGf,WAAW,CAAC,CAAC;EAC9B,MAAMgB,KAAK,GAAGP,QAAQ,CAAC,CAAC;EACxB,MAAMQ,SAAS,GAAGT,aAAa,CAACQ,KAAK,CAACE,WAAW,CAACC,EAAE,CAAC,IAAI,CAAC,CAAC;EAC3D,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAGxB,QAAQ,CAAC,IAAI,CAAC;EACtC,MAAM,CAACyB,WAAW,EAAEC,cAAc,CAAC,GAAG1B,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAAC2B,OAAO,EAAEC,UAAU,CAAC,GAAG5B,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAAC6B,KAAK,EAAEC,QAAQ,CAAC,GAAG9B,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAAC+B,MAAM,EAAEC,SAAS,CAAC,GAAGhC,QAAQ,CAAC;IAAEiC,IAAI,EAAE,EAAE;IAAEC,KAAK,EAAE;EAAoB,CAAC,CAAC;EAC9E,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGpC,QAAQ,CAAC,CAAC,CAAC;EAE7CC,SAAS,CAAC,MAAM;IACZ,IAAIgB,EAAE,EAAE;MACJoB,eAAe,CAACpB,EAAE,CAAC;IACvB;IACA;IACAqB,eAAe,CAAC,CAAC;EACrB,CAAC,EAAE,CAACrB,EAAE,CAAC,CAAC;EAER,MAAMqB,eAAe,GAAG,MAAAA,CAAA,KAAY;IAChC,IAAI;MACA,MAAMC,QAAQ,GAAG,MAAMC,KAAK,CAAC,kCAAkC,CAAC;MAChE,MAAMC,IAAI,GAAG,MAAMF,QAAQ,CAACG,IAAI,CAAC,CAAC;MAClC,IAAID,IAAI,CAACR,IAAI,IAAIQ,IAAI,CAACP,KAAK,EAAE;QACzBF,SAAS,CAAC;UACNC,IAAI,EAAEQ,IAAI,CAACR,IAAI,IAAI,EAAE;UACrBC,KAAK,EAAEO,IAAI,CAACP,KAAK,IAAI;QACzB,CAAC,CAAC;MACN;IACJ,CAAC,CAAC,OAAOL,KAAK,EAAE;MACZc,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAEf,KAAK,CAAC;IACtD;EACJ,CAAC;EAED,MAAMQ,eAAe,GAAG,MAAOQ,MAAM,IAAK;IACtC,IAAI;MACAjB,UAAU,CAAC,IAAI,CAAC;;MAEhB;MACA,MAAMW,QAAQ,GAAG,MAAMC,KAAK,CAAC,yFAAyFK,MAAM,EAAE,CAAC;MAE/H,IAAI,CAACN,QAAQ,CAACO,EAAE,EAAE;QACd,MAAM,IAAIC,KAAK,CAAC,uBAAuBR,QAAQ,CAACS,MAAM,EAAE,CAAC;MAC7D;MAEA,MAAMP,IAAI,GAAG,MAAMF,QAAQ,CAACG,IAAI,CAAC,CAAC;MAElC,IAAID,IAAI,CAACQ,OAAO,IAAIR,IAAI,CAACA,IAAI,EAAE;QAC3BjB,OAAO,CAACiB,IAAI,CAACA,IAAI,CAAC;QAClB;QACA,IAAIA,IAAI,CAACA,IAAI,CAACS,WAAW,EAAE;UACvBC,gBAAgB,CAACV,IAAI,CAACA,IAAI,CAACS,WAAW,EAAEL,MAAM,CAAC;QACnD;MACJ,CAAC,MAAM;QACHf,QAAQ,CAAC,wBAAwB,CAAC;MACtC;IACJ,CAAC,CAAC,OAAOD,KAAK,EAAE;MACZc,OAAO,CAACd,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC5CC,QAAQ,CAAC,yCAAyC,CAAC;IACvD,CAAC,SAAS;MACNF,UAAU,CAAC,KAAK,CAAC;IACrB;EACJ,CAAC;EAED,MAAMuB,gBAAgB,GAAG,MAAAA,CAAOC,UAAU,EAAEC,aAAa,KAAK;IAC1D,IAAI;MACA,MAAMd,QAAQ,GAAG,MAAMC,KAAK,CAAC,yFAAyFY,UAAU,UAAU,CAAC;MAE3I,IAAI,CAACb,QAAQ,CAACO,EAAE,EAAE;QACd,MAAM,IAAIC,KAAK,CAAC,uBAAuBR,QAAQ,CAACS,MAAM,EAAE,CAAC;MAC7D;MAEA,MAAMP,IAAI,GAAG,MAAMF,QAAQ,CAACG,IAAI,CAAC,CAAC;MAElC,IAAID,IAAI,CAACQ,OAAO,IAAIK,KAAK,CAACC,OAAO,CAACd,IAAI,CAACA,IAAI,CAAC,EAAE;QAC1C;QACA,MAAMe,QAAQ,GAAGf,IAAI,CAACA,IAAI,CACrBgB,MAAM,CAACC,IAAI,IAAIA,IAAI,CAACzC,EAAE,KAAK0C,QAAQ,CAACN,aAAa,CAAC,CAAC,CACnDO,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;QAChBlC,cAAc,CAAC8B,QAAQ,CAAC;MAC5B,CAAC,MAAM;QACH9B,cAAc,CAAC,EAAE,CAAC;MACtB;IACJ,CAAC,CAAC,OAAOG,KAAK,EAAE;MACZc,OAAO,CAACd,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACpDH,cAAc,CAAC,EAAE,CAAC;IACtB;EACJ,CAAC;EAED,MAAMmC,WAAW,GAAIC,SAAS,IAAK;IAC/B,IAAI,CAACA,SAAS,EAAE,OAAO,wCAAwC;IAC/D;IACA,IAAIA,SAAS,CAACC,UAAU,CAAC,MAAM,CAAC,EAAE,OAAOD,SAAS;IAClD;IACA,IAAIA,SAAS,CAACC,UAAU,CAAC,GAAG,CAAC,EAAE,OAAO,wBAAwBD,SAAS,EAAE;IACzE;IACA,OAAO,oEAAoEA,SAAS,EAAE;EAC1F,CAAC;EAED,MAAME,UAAU,GAAIC,UAAU,IAAK;IAC/B,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAACF,UAAU,CAAC;IACjC,OAAOC,IAAI,CAACE,kBAAkB,CAAC,OAAO,EAAE;MACpCC,OAAO,EAAE,MAAM;MACfC,IAAI,EAAE,SAAS;MACfC,KAAK,EAAE,MAAM;MACbC,GAAG,EAAE;IACT,CAAC,CAAC;EACN,CAAC;EAED,MAAMC,UAAU,GAAIR,UAAU,IAAK;IAC/B,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAACF,UAAU,CAAC;IACjC,OAAOC,IAAI,CAACQ,kBAAkB,CAAC,OAAO,EAAE;MACpCC,IAAI,EAAE,SAAS;MACfC,MAAM,EAAE;IACZ,CAAC,CAAC;EACN,CAAC;EAED,MAAMC,sBAAsB,GAAIhC,MAAM,IAAK;IACvC3B,QAAQ,CAAC,cAAc2B,MAAM,EAAE,CAAC;EACpC,CAAC;EAED,MAAMiC,gBAAgB,GAAGA,CAAA,KAAM;IAC3B5D,QAAQ,CAAC,GAAG,CAAC;EACjB,CAAC;EAED,IAAIS,OAAO,EAAE;IACT,oBACIb,OAAA;MAAKiE,SAAS,EAAC,0DAA0D;MAAAC,QAAA,eACrElE,OAAA;QAAKiE,SAAS,EAAC,aAAa;QAAAC,QAAA,gBACxBlE,OAAA;UAAKiE,SAAS,EAAC;QAA6E;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACnGtE,OAAA;UAAGiE,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAAgB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAEd;EAEA,IAAIvD,KAAK,EAAE;IACP,oBACIf,OAAA;MAAKiE,SAAS,EAAC,0DAA0D;MAAAC,QAAA,eACrElE,OAAA;QAAKiE,SAAS,EAAC,aAAa;QAAAC,QAAA,gBACxBlE,OAAA;UAAKiE,SAAS,EAAC,yEAAyE;UAAAC,QAAA,gBACpFlE,OAAA;YAAGiE,SAAS,EAAC;UAAkC;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,EACnDvD,KAAK;QAAA;UAAAoD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eACNtE,OAAA;UACIuE,OAAO,EAAEP,gBAAiB;UAC1BC,SAAS,EAAC,iFAAiF;UAAAC,QAAA,gBAE3FlE,OAAA;YAAGiE,SAAS,EAAC;UAAwB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,sBAE9C;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAEd;EAEA,IAAI,CAAC7D,IAAI,EAAE;IACP,oBACIT,OAAA;MAAKiE,SAAS,EAAC,0DAA0D;MAAAC,QAAA,eACrElE,OAAA;QAAKiE,SAAS,EAAC,aAAa;QAAAC,QAAA,gBACxBlE,OAAA;UAAGiE,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAAsB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACvDtE,OAAA;UACIuE,OAAO,EAAEP,gBAAiB;UAC1BC,SAAS,EAAC,sFAAsF;UAAAC,QAAA,gBAEhGlE,OAAA;YAAGiE,SAAS,EAAC;UAAwB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,sBAE9C;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAEd;EAEA,oBACItE,OAAA,CAACV,GAAG;IAACkF,EAAE,EAAE;MAAEC,SAAS,EAAE,OAAO;MAAEC,OAAO,EAAE,SAAS;MAAEC,KAAK,EAAE,OAAO;MAAEC,QAAQ,EAAE;IAAS,CAAE;IAAAV,QAAA,gBAEpFlE,OAAA,CAACR,MAAM;MAACqF,QAAQ,EAAC,OAAO;MAACC,KAAK,EAAC,SAAS;MAACC,SAAS,EAAE,CAAE;MAACP,EAAE,EAAE;QAAEE,OAAO,EAAE,MAAM;QAAEM,YAAY,EAAE,CAAC;QAAEC,WAAW,EAAE,UAAU;QAAEC,MAAM,EAAE;MAAK,CAAE;MAAAhB,QAAA,eACnIlE,OAAA,CAACP,OAAO;QAAC+E,EAAE,EAAE;UAAEC,SAAS,EAAE;YAAEU,EAAE,EAAE,EAAE;YAAEC,EAAE,EAAE;UAAG,CAAC;UAAEC,EAAE,EAAE;YAAEF,EAAE,EAAE,CAAC;YAAEC,EAAE,EAAE;UAAE;QAAE,CAAE;QAAAlB,QAAA,gBACjElE,OAAA,CAACV,GAAG;UAACkF,EAAE,EAAE;YAAEc,OAAO,EAAE,MAAM;YAAEC,UAAU,EAAE,QAAQ;YAAEC,QAAQ,EAAE;UAAE,CAAE;UAAAtB,QAAA,gBAC5DlE,OAAA,CAACN,MAAM;YACH+F,GAAG,EAAExE,MAAM,CAACE,IAAK;YACjBuE,GAAG,EAAC,MAAM;YACVlB,EAAE,EAAE;cAAEG,KAAK,EAAE,EAAE;cAAEgB,MAAM,EAAE,EAAE;cAAEC,EAAE,EAAE;YAAE,CAAE;YACrCC,OAAO,EAAGC,CAAC,IAAK;cAAEA,CAAC,CAACC,MAAM,CAACN,GAAG,GAAG,cAAc;YAAE;UAAE;YAAAtB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtD,CAAC,eACFtE,OAAA,CAACT,UAAU;YAACyG,OAAO,EAAC,IAAI;YAACxB,EAAE,EAAE;cAAEyB,UAAU,EAAE,GAAG;cAAEnB,KAAK,EAAE,cAAc;cAAEoB,QAAQ,EAAE;gBAAEf,EAAE,EAAE,EAAE;gBAAEC,EAAE,EAAE;cAAG;YAAE,CAAE;YAAAlB,QAAA,EACjGjD,MAAM,CAACG;UAAK;YAAA+C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACZ,CAAC,eACNtE,OAAA,CAACL,UAAU;UACPwG,IAAI,EAAC,KAAK;UACVrB,KAAK,EAAC,SAAS;UACfP,OAAO,EAAEA,CAAA,KAAMnE,QAAQ,CAAC,GAAG,CAAE;UAC7BoE,EAAE,EAAE;YAAEoB,EAAE,EAAE;UAAE,CAAE;UAAA1B,QAAA,eAEdlE,OAAA,CAACJ,QAAQ;YAACsG,QAAQ,EAAC;UAAO;YAAA/B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eAGTtE,OAAA,CAACV,GAAG;MAACkF,EAAE,EAAE;QAAEE,OAAO,EAAE,OAAO;QAAE0B,SAAS,EAAE,CAAC;QAAEpB,YAAY,EAAE,CAAC;QAAEC,WAAW,EAAE,UAAU;QAAEoB,EAAE,EAAE;UAAElB,EAAE,EAAE,MAAM;UAAEC,EAAE,EAAE;QAAO;MAAE,CAAE;MAAAlB,QAAA,eAClHlE,OAAA,CAACV,GAAG;QAACkF,EAAE,EAAE;UAAE8B,QAAQ,EAAE,QAAQ;UAAEC,EAAE,EAAE,MAAM;UAAElB,EAAE,EAAE,CAAC;UAAEmB,EAAE,EAAE;QAAE,CAAE;QAAAtC,QAAA,eACtDlE,OAAA,CAACV,GAAG;UAACkF,EAAE,EAAE;YAAEc,OAAO,EAAE,MAAM;YAAEC,UAAU,EAAE,QAAQ;YAAEkB,GAAG,EAAE,CAAC;YAAEP,QAAQ,EAAE,MAAM;YAAEpB,KAAK,EAAE;UAAiB,CAAE;UAAAZ,QAAA,gBAClGlE,OAAA,CAACV,GAAG;YACAoH,SAAS,EAAC,QAAQ;YAClBnC,OAAO,EAAEP,gBAAiB;YAC1BQ,EAAE,EAAE;cACAc,OAAO,EAAE,MAAM;cACfC,UAAU,EAAE,QAAQ;cACpBT,KAAK,EAAE,gBAAgB;cACvB,SAAS,EAAE;gBAAEA,KAAK,EAAE;cAAe,CAAC;cACpC6B,UAAU,EAAE,YAAY;cACxBC,MAAM,EAAE,MAAM;cACdC,UAAU,EAAE,MAAM;cAClBC,MAAM,EAAE;YACZ,CAAE;YAAA5C,QAAA,gBAEFlE,OAAA;cAAGiE,SAAS,EAAC,aAAa;cAAC8C,KAAK,EAAE;gBAAEC,WAAW,EAAE;cAAM;YAAE;cAAA7C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,WAElE;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACNtE,OAAA;YAAGiE,SAAS,EAAC,sBAAsB;YAAC8C,KAAK,EAAE;cAAEb,QAAQ,EAAE;YAAO;UAAE;YAAA/B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACrEtE,OAAA,CAACT,UAAU;YAACyG,OAAO,EAAC,OAAO;YAACxB,EAAE,EAAE;cAAEyB,UAAU,EAAE,GAAG;cAAEnB,KAAK,EAAE;YAAe,CAAE;YAAAZ,QAAA,EAAC;UAE5E;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACZ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,eAGNtE,OAAA,CAACV,GAAG;MAACkF,EAAE,EAAE;QACL8B,QAAQ,EAAE,QAAQ;QAClBC,EAAE,EAAE,MAAM;QACVlB,EAAE,EAAE,CAAC;QACLmB,EAAE,EAAE,CAAC;QACLS,EAAE,EAAE;UAAE9B,EAAE,EAAE,EAAE;UAAEC,EAAE,EAAE;QAAE,CAAC,CAAC;MAC1B,CAAE;MAAAlB,QAAA,eACElE,OAAA,CAACV,GAAG;QAACkF,EAAE,EAAE;UAAEc,OAAO,EAAE,MAAM;UAAE4B,mBAAmB,EAAE;YAAE/B,EAAE,EAAE,KAAK;YAAEgC,EAAE,EAAE;UAAU,CAAC;UAAEV,GAAG,EAAE;QAAE,CAAE;QAAAvC,QAAA,gBAEpFlE,OAAA,CAACV,GAAG;UAAA4E,QAAA,eACAlE,OAAA;YAASiE,SAAS,EAAC,+CAA+C;YAAAC,QAAA,gBAE9DlE,OAAA;cAAKiE,SAAS,EAAC,UAAU;cAAAC,QAAA,gBACrBlE,OAAA;gBACIyF,GAAG,EAAE1C,WAAW,CAACtC,IAAI,CAAC2G,KAAK,CAAE;gBAC7B1B,GAAG,EAAEjF,IAAI,CAACW,KAAM;gBAChB6C,SAAS,EAAC,kCAAkC;gBAC5C4B,OAAO,EAAGC,CAAC,IAAK;kBACZA,CAAC,CAACC,MAAM,CAACN,GAAG,GAAG,wCAAwC;gBAC3D;cAAE;gBAAAtB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,eACFtE,OAAA;gBAAKiE,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,eAClClE,OAAA;kBAAMiE,SAAS,EAAC,mEAAmE;kBAAAC,QAAA,EAC9EzD,IAAI,CAAC4G,aAAa,IAAI;gBAAQ;kBAAAlD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7B;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,eAGNtE,OAAA;cAAKiE,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACvBlE,OAAA;gBAAIiE,SAAS,EAAC,iEAAiE;gBAAAC,QAAA,EAC1EzD,IAAI,CAACW;cAAK;gBAAA+C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACX,CAAC,eAGLtE,OAAA;gBAAKiE,SAAS,EAAC,4EAA4E;gBAAAC,QAAA,gBACvFlE,OAAA;kBAAKiE,SAAS,EAAC,mBAAmB;kBAAAC,QAAA,gBAC9BlE,OAAA;oBAAGiE,SAAS,EAAC;kBAAwC;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,EACzDpB,UAAU,CAACzC,IAAI,CAAC6G,UAAU,IAAI7G,IAAI,CAAC2C,IAAI,CAAC;gBAAA;kBAAAe,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxC,CAAC,eACNtE,OAAA;kBAAKiE,SAAS,EAAC,mBAAmB;kBAAAC,QAAA,gBAC9BlE,OAAA;oBAAGiE,SAAS,EAAC;kBAAiC;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,EAClDX,UAAU,CAAClD,IAAI,CAAC6G,UAAU,IAAI7G,IAAI,CAAC2C,IAAI,CAAC;gBAAA;kBAAAe,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxC,CAAC,eACNtE,OAAA;kBAAKiE,SAAS,EAAC,mBAAmB;kBAAAC,QAAA,gBAC9BlE,OAAA;oBAAGiE,SAAS,EAAC;kBAA+B;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,EAChD7D,IAAI,CAAC8G,KAAK,IAAI,CAAC,EAAC,QACrB;gBAAA;kBAAApD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,eAGNtE,OAAA;gBAAKiE,SAAS,EAAC,2BAA2B;gBAAAC,QAAA,eACtClE,OAAA;kBACIiE,SAAS,EAAC,mDAAmD;kBAC7DuD,uBAAuB,EAAE;oBACrBC,MAAM,EAAEhH,IAAI,CAACiH,OAAO,CAACC,OAAO,CAAC,KAAK,EAAE,MAAM;kBAC9C;gBAAE;kBAAAxD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC,eAGNtE,OAAA;gBAAKiE,SAAS,EAAC,oBAAoB;gBAAAC,QAAA,gBAC/BlE,OAAA;kBAAIiE,SAAS,EAAC,0CAA0C;kBAAAC,QAAA,EAAC;gBAAe;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC7EtE,OAAA;kBAAKiE,SAAS,EAAC,gBAAgB;kBAAAC,QAAA,gBAC3BlE,OAAA;oBAAQiE,SAAS,EAAC,iFAAiF;oBAAAC,QAAA,gBAC/FlE,OAAA;sBAAGiE,SAAS,EAAC;oBAAwB;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,YAE9C;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACTtE,OAAA;oBAAQiE,SAAS,EAAC,iFAAiF;oBAAAC,QAAA,gBAC/FlE,OAAA;sBAAGiE,SAAS,EAAC;oBAAqB;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,WAE3C;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACTtE,OAAA;oBAAQiE,SAAS,EAAC,mFAAmF;oBAAAC,QAAA,gBACjGlE,OAAA;sBAAGiE,SAAS,EAAC;oBAAsB;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,YAE5C;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACR,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eAGNtE,OAAA,CAACV,GAAG;UAAA4E,QAAA,GAECvD,WAAW,CAACiH,MAAM,GAAG,CAAC,iBACnB5H,OAAA;YAAKiE,SAAS,EAAC,wCAAwC;YAAAC,QAAA,gBACnDlE,OAAA;cAAIiE,SAAS,EAAC,0CAA0C;cAAAC,QAAA,gBACpDlE,OAAA;gBAAGiE,SAAS,EAAC;cAAqC;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,kBAE3D;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACLtE,OAAA;cAAKiE,SAAS,EAAC,WAAW;cAAAC,QAAA,EACrBvD,WAAW,CAACkH,GAAG,CAAEjF,IAAI,iBAClB5C,OAAA;gBAEIuE,OAAO,EAAEA,CAAA,KAAMR,sBAAsB,CAACnB,IAAI,CAACzC,EAAE,CAAE;gBAC/C8D,SAAS,EAAC,uEAAuE;gBAAAC,QAAA,gBAEjFlE,OAAA;kBACIyF,GAAG,EAAE1C,WAAW,CAACH,IAAI,CAACwE,KAAK,CAAE;kBAC7B1B,GAAG,EAAE9C,IAAI,CAACxB,KAAM;kBAChB6C,SAAS,EAAC,sDAAsD;kBAChE4B,OAAO,EAAGC,CAAC,IAAK;oBACZA,CAAC,CAACC,MAAM,CAACN,GAAG,GAAG,wCAAwC;kBAC3D;gBAAE;kBAAAtB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC,eACFtE,OAAA;kBAAKiE,SAAS,EAAC,gBAAgB;kBAAAC,QAAA,gBAC3BlE,OAAA;oBAAIiE,SAAS,EAAC,qDAAqD;oBAAAC,QAAA,EAC9DtB,IAAI,CAACxB;kBAAK;oBAAA+C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACX,CAAC,eACLtE,OAAA;oBAAGiE,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,EAC/BhB,UAAU,CAACN,IAAI,CAAC0E,UAAU,IAAI1E,IAAI,CAACQ,IAAI;kBAAC;oBAAAe,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1C,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA,GAnBD1B,IAAI,CAACzC,EAAE;gBAAAgE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAoBX,CACR;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CACR,eAGDtE,OAAA,CAACV,GAAG;YAACkF,EAAE,EAAE;cAAEE,OAAO,EAAE,OAAO;cAAEoD,YAAY,EAAE,CAAC;cAAE1B,SAAS,EAAE,CAAC;cAAE2B,CAAC,EAAE;YAAE,CAAE;YAAA7D,QAAA,eAC/DlE,OAAA,CAACV,GAAG;cACAoH,SAAS,EAAC,QAAQ;cAClBnC,OAAO,EAAEP,gBAAiB;cAC1BQ,EAAE,EAAE;gBACAG,KAAK,EAAE,MAAM;gBACbD,OAAO,EAAE,cAAc;gBACvBI,KAAK,EAAE,OAAO;gBACd0B,EAAE,EAAE,GAAG;gBACPnB,EAAE,EAAE,CAAC;gBACLyC,YAAY,EAAE,CAAC;gBACflB,MAAM,EAAE,MAAM;gBACdE,MAAM,EAAE,SAAS;gBACjBb,UAAU,EAAE,GAAG;gBACfU,UAAU,EAAE,uBAAuB;gBACnC,SAAS,EAAE;kBACPjC,OAAO,EAAE;gBACb;cACJ,CAAE;cAAAR,QAAA,gBAEFlE,OAAA;gBAAGiE,SAAS,EAAC,mBAAmB;gBAAC8C,KAAK,EAAE;kBAAEC,WAAW,EAAE;gBAAM;cAAE;gBAAA7C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,sBAExE;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,eAGNtE,OAAA,CAACV,GAAG;MAACkF,EAAE,EAAE;QACLK,QAAQ,EAAE,OAAO;QACjBmD,IAAI,EAAE,CAAC;QACPC,KAAK,EAAE,CAAC;QACRC,MAAM,EAAE,CAAC;QACThD,MAAM,EAAE,IAAI;QACZI,OAAO,EAAE;UAAEH,EAAE,EAAE,OAAO;UAAEC,EAAE,EAAE;QAAO,CAAC;QAAE;QACtC+C,eAAe,EAAE,OAAO;QACxBC,SAAS,EAAE,mBAAmB;QAC9BhC,SAAS,EAAE;MACf,CAAE;MAAAlC,QAAA,eACElE,OAAA,CAACV,GAAG;QAACkF,EAAE,EAAE;UACLc,OAAO,EAAE,MAAM;UACf+C,cAAc,EAAE,cAAc;UAC9B9C,UAAU,EAAE,QAAQ;UACpBI,MAAM,EAAE,EAAE;UACVN,EAAE,EAAE;QACR,CAAE;QAAAnB,QAAA,gBACElE,OAAA,CAACV,GAAG;UACAiF,OAAO,EAAEA,CAAA,KAAMnE,QAAQ,CAAC,GAAG,CAAE;UAC7B6D,SAAS,EAAC,wBAAwB;UAClC8C,KAAK,EAAE;YAAED,MAAM,EAAE;UAAU,CAAE;UAAA5C,QAAA,gBAE7BlE,OAAA;YAAGiE,SAAS,EAAC;UAA2C;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC7DtE,OAAA,CAACT,UAAU;YAACyG,OAAO,EAAC,SAAS;YAAC/B,SAAS,EAAC,kBAAkB;YAACO,EAAE,EAAE;cAAEM,KAAK,EAAE;YAAe,CAAE;YAAAZ,QAAA,EAAC;UAE1F;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACZ,CAAC,eAENtE,OAAA,CAACV,GAAG;UACAiF,OAAO,EAAEA,CAAA,KAAMnE,QAAQ,CAAC,GAAG,CAAE;UAC7B6D,SAAS,EAAC,iBAAiB;UAC3B8C,KAAK,EAAE;YAAED,MAAM,EAAE;UAAU,CAAE;UAAA5C,QAAA,gBAE7BlE,OAAA;YAAGiE,SAAS,EAAC;UAA6C;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC/DtE,OAAA,CAACT,UAAU;YAACyG,OAAO,EAAC,SAAS;YAAC/B,SAAS,EAAC,kBAAkB;YAACO,EAAE,EAAE;cAAEM,KAAK,EAAE;YAAiB,CAAE;YAAAZ,QAAA,EAAC;UAE5F;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACZ,CAAC,eAENtE,OAAA,CAACV,GAAG;UACAiF,OAAO,EAAEA,CAAA,KAAMnE,QAAQ,CAAC,QAAQ,CAAE;UAClC6D,SAAS,EAAC,iBAAiB;UAC3B8C,KAAK,EAAE;YAAED,MAAM,EAAE;UAAU,CAAE;UAAA5C,QAAA,gBAE7BlE,OAAA;YAAGiE,SAAS,EAAC;UAA+C;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACjEtE,OAAA,CAACT,UAAU;YAACyG,OAAO,EAAC,SAAS;YAAC/B,SAAS,EAAC,kBAAkB;YAACO,EAAE,EAAE;cAAEM,KAAK,EAAE;YAAiB,CAAE;YAAAZ,QAAA,EAAC;UAE5F;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACZ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEd,CAAC;AAACpE,EAAA,CApbID,QAAQ;EAAA,QAEKb,SAAS,EACPC,WAAW,EACdS,QAAQ,EACJD,aAAa;AAAA;AAAAyI,EAAA,GAL7BrI,QAAQ;AAsbd,eAAeA,QAAQ;AAAC,IAAAqI,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}