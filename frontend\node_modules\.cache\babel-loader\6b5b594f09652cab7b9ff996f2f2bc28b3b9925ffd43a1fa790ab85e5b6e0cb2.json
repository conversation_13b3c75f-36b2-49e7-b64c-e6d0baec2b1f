{"ast": null, "code": "var _jsxFileName = \"C:\\\\laragon\\\\www\\\\react-news\\\\frontend\\\\src\\\\pages\\\\user\\\\data-news.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useParams, useNavigate } from 'react-router-dom';\nimport Navbar from './components/Navbar';\nimport BottomAppBar from './components/BottomAppBar';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst DataNews = () => {\n  _s();\n  const {\n    id\n  } = useParams();\n  const navigate = useNavigate();\n  const [news, setNews] = useState(null);\n  const [relatedNews, setRelatedNews] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  useEffect(() => {\n    if (id) {\n      fetchNewsDetail(id);\n    }\n  }, [id]);\n  const fetchNewsDetail = async newsId => {\n    try {\n      setLoading(true);\n      const response = await fetch(`/react-news/backend/api.php?action=get_news_by_id&id=${newsId}`);\n      const data = await response.json();\n      if (data.success) {\n        setNews(data.data);\n        // Fetch related news\n        fetchRelatedNews(data.data.category_id, newsId);\n      } else {\n        setError('Berita tidak ditemukan');\n      }\n    } catch (error) {\n      console.error('Error fetching news:', error);\n      setError('Gagal memuat berita');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const fetchRelatedNews = async (categoryId, currentNewsId) => {\n    try {\n      const response = await fetch(`/react-news/backend/api.php?action=get_news&category=${categoryId}&limit=4`);\n      const data = await response.json();\n      if (data.success) {\n        // Filter out current news and limit to 3\n        const filtered = data.data.filter(item => item.id !== parseInt(currentNewsId)).slice(0, 3);\n        setRelatedNews(filtered);\n      }\n    } catch (error) {\n      console.error('Error fetching related news:', error);\n    }\n  };\n  const getImageUrl = imagePath => {\n    if (!imagePath) return '/react-news/frontend/public/placeholder-news.jpg';\n    if (imagePath.startsWith('http')) return imagePath;\n    if (imagePath.startsWith('/')) return `http://localhost:3000${imagePath}`;\n    return `/react-news/backend/uploads/${imagePath}`;\n  };\n  const formatDate = dateString => {\n    const date = new Date(dateString);\n    return date.toLocaleDateString('id-ID', {\n      weekday: 'long',\n      year: 'numeric',\n      month: 'long',\n      day: 'numeric'\n    });\n  };\n  const formatTime = dateString => {\n    const date = new Date(dateString);\n    return date.toLocaleTimeString('id-ID', {\n      hour: '2-digit',\n      minute: '2-digit'\n    });\n  };\n  const handleRelatedNewsClick = newsId => {\n    navigate(`/data-news/${newsId}`);\n  };\n  const handleBackToHome = () => {\n    navigate('/');\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gray-50 flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 93,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600\",\n          children: \"Memuat berita...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 94,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 92,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 91,\n      columnNumber: 13\n    }, this);\n  }\n  if (error) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gray-50 flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-red-100 border border-red-400 text-red-700 px-6 py-4 rounded-lg mb-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"fas fa-exclamation-triangle mr-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 105,\n            columnNumber: 25\n          }, this), error]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 104,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: handleBackToHome,\n          className: \"bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors\",\n          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"fas fa-arrow-left mr-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 112,\n            columnNumber: 25\n          }, this), \"Kembali ke Beranda\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 108,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 103,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 102,\n      columnNumber: 13\n    }, this);\n  }\n  if (!news) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gray-50 flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600\",\n          children: \"Berita tidak ditemukan\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 124,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: handleBackToHome,\n          className: \"mt-4 bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors\",\n          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"fas fa-arrow-left mr-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 129,\n            columnNumber: 25\n          }, this), \"Kembali ke Beranda\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 125,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 123,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 122,\n      columnNumber: 13\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gray-50\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white shadow-sm border-b\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-6xl mx-auto px-4 py-4\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-2 text-sm text-gray-600\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: handleBackToHome,\n            className: \"hover:text-blue-600 transition-colors\",\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"fas fa-home mr-1\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 147,\n              columnNumber: 29\n            }, this), \"Beranda\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 143,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"fas fa-chevron-right text-xs\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 150,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-gray-800 font-medium\",\n            children: \"Detail Berita\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 151,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 142,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 141,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 140,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-6xl mx-auto px-4 py-8\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 lg:grid-cols-3 gap-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"lg:col-span-2\",\n          children: /*#__PURE__*/_jsxDEV(\"article\", {\n            className: \"bg-white rounded-lg shadow-md overflow-hidden\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"relative\",\n              children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                src: getImageUrl(news.image),\n                alt: news.title,\n                className: \"w-full h-64 md:h-80 object-cover\",\n                onError: e => {\n                  e.target.src = '/react-news/frontend/public/placeholder-news.jpg';\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 164,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"absolute top-4 left-4\",\n                children: /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"bg-blue-600 text-white px-3 py-1 rounded-full text-sm font-medium\",\n                  children: news.category_name || 'Berita'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 173,\n                  columnNumber: 37\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 172,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 163,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-6 md:p-8\",\n              children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n                className: \"text-2xl md:text-3xl font-bold text-gray-900 mb-4 leading-tight\",\n                children: news.title\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 181,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex flex-wrap items-center gap-4 text-sm text-gray-600 mb-6 pb-6 border-b\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                    className: \"fas fa-calendar-alt mr-2 text-blue-600\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 188,\n                    columnNumber: 41\n                  }, this), formatDate(news.created_at || news.date)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 187,\n                  columnNumber: 37\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                    className: \"fas fa-clock mr-2 text-blue-600\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 192,\n                    columnNumber: 41\n                  }, this), formatTime(news.created_at || news.date)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 191,\n                  columnNumber: 37\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                    className: \"fas fa-eye mr-2 text-blue-600\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 196,\n                    columnNumber: 41\n                  }, this), news.views || 0, \" views\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 195,\n                  columnNumber: 37\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 186,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"prose prose-lg max-w-none\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-gray-700 leading-relaxed whitespace-pre-line\",\n                  dangerouslySetInnerHTML: {\n                    __html: news.content.replace(/\\n/g, '<br>')\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 203,\n                  columnNumber: 37\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 202,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mt-8 pt-6 border-t\",\n                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"text-lg font-semibold text-gray-900 mb-4\",\n                  children: \"Bagikan Artikel\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 213,\n                  columnNumber: 37\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex space-x-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors\",\n                    children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                      className: \"fab fa-facebook-f mr-2\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 216,\n                      columnNumber: 45\n                    }, this), \"Facebook\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 215,\n                    columnNumber: 41\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"bg-blue-400 text-white px-4 py-2 rounded-lg hover:bg-blue-500 transition-colors\",\n                    children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                      className: \"fab fa-twitter mr-2\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 220,\n                      columnNumber: 45\n                    }, this), \"Twitter\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 219,\n                    columnNumber: 41\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors\",\n                    children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                      className: \"fab fa-whatsapp mr-2\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 224,\n                      columnNumber: 45\n                    }, this), \"WhatsApp\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 223,\n                    columnNumber: 41\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 214,\n                  columnNumber: 37\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 212,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 180,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 161,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 160,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"lg:col-span-1\",\n          children: [relatedNews.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white rounded-lg shadow-md p-6 mb-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-lg font-semibold text-gray-900 mb-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"fas fa-newspaper mr-2 text-blue-600\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 239,\n                columnNumber: 37\n              }, this), \"Berita Terkait\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 238,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-4\",\n              children: relatedNews.map(item => /*#__PURE__*/_jsxDEV(\"div\", {\n                onClick: () => handleRelatedNewsClick(item.id),\n                className: \"flex cursor-pointer hover:bg-gray-50 p-2 rounded-lg transition-colors\",\n                children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                  src: getImageUrl(item.image),\n                  alt: item.title,\n                  className: \"w-16 h-16 object-cover rounded-lg mr-3 flex-shrink-0\",\n                  onError: e => {\n                    e.target.src = '/react-news/frontend/public/placeholder-news.jpg';\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 249,\n                  columnNumber: 45\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex-1 min-w-0\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                    className: \"text-sm font-medium text-gray-900 line-clamp-2 mb-1\",\n                    children: item.title\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 258,\n                    columnNumber: 49\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-xs text-gray-600\",\n                    children: formatDate(item.created_at || item.date)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 261,\n                    columnNumber: 49\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 257,\n                  columnNumber: 45\n                }, this)]\n              }, item.id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 244,\n                columnNumber: 41\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 242,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 237,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white rounded-lg shadow-md p-6\",\n            children: /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: handleBackToHome,\n              className: \"w-full bg-blue-600 text-white py-3 px-4 rounded-lg hover:bg-blue-700 transition-colors font-medium\",\n              children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"fas fa-arrow-left mr-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 277,\n                columnNumber: 33\n              }, this), \"Kembali ke Beranda\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 273,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 272,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 234,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 158,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 157,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 138,\n    columnNumber: 9\n  }, this);\n};\n_s(DataNews, \"+TxuMVbdrXeuEeKLbv0GvVWOVVg=\", false, function () {\n  return [useParams, useNavigate];\n});\n_c = DataNews;\nexport default DataNews;\nvar _c;\n$RefreshReg$(_c, \"DataNews\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useParams", "useNavigate", "<PERSON><PERSON><PERSON>", "BottomAppBar", "jsxDEV", "_jsxDEV", "DataNews", "_s", "id", "navigate", "news", "setNews", "relatedNews", "setRelatedNews", "loading", "setLoading", "error", "setError", "fetchNewsDetail", "newsId", "response", "fetch", "data", "json", "success", "fetchRelatedNews", "category_id", "console", "categoryId", "currentNewsId", "filtered", "filter", "item", "parseInt", "slice", "getImageUrl", "imagePath", "startsWith", "formatDate", "dateString", "date", "Date", "toLocaleDateString", "weekday", "year", "month", "day", "formatTime", "toLocaleTimeString", "hour", "minute", "handleRelatedNewsClick", "handleBackToHome", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "src", "image", "alt", "title", "onError", "e", "target", "category_name", "created_at", "views", "dangerouslySetInnerHTML", "__html", "content", "replace", "length", "map", "_c", "$RefreshReg$"], "sources": ["C:/laragon/www/react-news/frontend/src/pages/user/data-news.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useParams, useNavigate } from 'react-router-dom';\nimport Navbar from './components/Navbar';\nimport BottomAppBar from './components/BottomAppBar';\n\nconst DataNews = () => {\n    const { id } = useParams();\n    const navigate = useNavigate();\n    const [news, setNews] = useState(null);\n    const [relatedNews, setRelatedNews] = useState([]);\n    const [loading, setLoading] = useState(true);\n    const [error, setError] = useState(null);\n\n    useEffect(() => {\n        if (id) {\n            fetchNewsDetail(id);\n        }\n    }, [id]);\n\n    const fetchNewsDetail = async (newsId) => {\n        try {\n            setLoading(true);\n            const response = await fetch(`/react-news/backend/api.php?action=get_news_by_id&id=${newsId}`);\n            const data = await response.json();\n            \n            if (data.success) {\n                setNews(data.data);\n                // Fetch related news\n                fetchRelatedNews(data.data.category_id, newsId);\n            } else {\n                setError('Berita tidak ditemukan');\n            }\n        } catch (error) {\n            console.error('Error fetching news:', error);\n            setError('Gagal memuat berita');\n        } finally {\n            setLoading(false);\n        }\n    };\n\n    const fetchRelatedNews = async (categoryId, currentNewsId) => {\n        try {\n            const response = await fetch(`/react-news/backend/api.php?action=get_news&category=${categoryId}&limit=4`);\n            const data = await response.json();\n            \n            if (data.success) {\n                // Filter out current news and limit to 3\n                const filtered = data.data.filter(item => item.id !== parseInt(currentNewsId)).slice(0, 3);\n                setRelatedNews(filtered);\n            }\n        } catch (error) {\n            console.error('Error fetching related news:', error);\n        }\n    };\n\n    const getImageUrl = (imagePath) => {\n        if (!imagePath) return '/react-news/frontend/public/placeholder-news.jpg';\n        if (imagePath.startsWith('http')) return imagePath;\n        if (imagePath.startsWith('/')) return `http://localhost:3000${imagePath}`;\n        return `/react-news/backend/uploads/${imagePath}`;\n    };\n\n    const formatDate = (dateString) => {\n        const date = new Date(dateString);\n        return date.toLocaleDateString('id-ID', {\n            weekday: 'long',\n            year: 'numeric',\n            month: 'long',\n            day: 'numeric'\n        });\n    };\n\n    const formatTime = (dateString) => {\n        const date = new Date(dateString);\n        return date.toLocaleTimeString('id-ID', {\n            hour: '2-digit',\n            minute: '2-digit'\n        });\n    };\n\n    const handleRelatedNewsClick = (newsId) => {\n        navigate(`/data-news/${newsId}`);\n    };\n\n    const handleBackToHome = () => {\n        navigate('/');\n    };\n\n    if (loading) {\n        return (\n            <div className=\"min-h-screen bg-gray-50 flex items-center justify-center\">\n                <div className=\"text-center\">\n                    <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4\"></div>\n                    <p className=\"text-gray-600\">Memuat berita...</p>\n                </div>\n            </div>\n        );\n    }\n\n    if (error) {\n        return (\n            <div className=\"min-h-screen bg-gray-50 flex items-center justify-center\">\n                <div className=\"text-center\">\n                    <div className=\"bg-red-100 border border-red-400 text-red-700 px-6 py-4 rounded-lg mb-4\">\n                        <i className=\"fas fa-exclamation-triangle mr-2\"></i>\n                        {error}\n                    </div>\n                    <button \n                        onClick={handleBackToHome}\n                        className=\"bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors\"\n                    >\n                        <i className=\"fas fa-arrow-left mr-2\"></i>\n                        Kembali ke Beranda\n                    </button>\n                </div>\n            </div>\n        );\n    }\n\n    if (!news) {\n        return (\n            <div className=\"min-h-screen bg-gray-50 flex items-center justify-center\">\n                <div className=\"text-center\">\n                    <p className=\"text-gray-600\">Berita tidak ditemukan</p>\n                    <button \n                        onClick={handleBackToHome}\n                        className=\"mt-4 bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors\"\n                    >\n                        <i className=\"fas fa-arrow-left mr-2\"></i>\n                        Kembali ke Beranda\n                    </button>\n                </div>\n            </div>\n        );\n    }\n\n    return (\n        <div className=\"min-h-screen bg-gray-50\">\n            {/* Header/Breadcrumb */}\n            <div className=\"bg-white shadow-sm border-b\">\n                <div className=\"max-w-6xl mx-auto px-4 py-4\">\n                    <div className=\"flex items-center space-x-2 text-sm text-gray-600\">\n                        <button \n                            onClick={handleBackToHome}\n                            className=\"hover:text-blue-600 transition-colors\"\n                        >\n                            <i className=\"fas fa-home mr-1\"></i>\n                            Beranda\n                        </button>\n                        <i className=\"fas fa-chevron-right text-xs\"></i>\n                        <span className=\"text-gray-800 font-medium\">Detail Berita</span>\n                    </div>\n                </div>\n            </div>\n\n            {/* Main Content */}\n            <div className=\"max-w-6xl mx-auto px-4 py-8\">\n                <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-8\">\n                    {/* Main Article */}\n                    <div className=\"lg:col-span-2\">\n                        <article className=\"bg-white rounded-lg shadow-md overflow-hidden\">\n                            {/* Article Header */}\n                            <div className=\"relative\">\n                                <img \n                                    src={getImageUrl(news.image)} \n                                    alt={news.title}\n                                    className=\"w-full h-64 md:h-80 object-cover\"\n                                    onError={(e) => {\n                                        e.target.src = '/react-news/frontend/public/placeholder-news.jpg';\n                                    }}\n                                />\n                                <div className=\"absolute top-4 left-4\">\n                                    <span className=\"bg-blue-600 text-white px-3 py-1 rounded-full text-sm font-medium\">\n                                        {news.category_name || 'Berita'}\n                                    </span>\n                                </div>\n                            </div>\n\n                            {/* Article Content */}\n                            <div className=\"p-6 md:p-8\">\n                                <h1 className=\"text-2xl md:text-3xl font-bold text-gray-900 mb-4 leading-tight\">\n                                    {news.title}\n                                </h1>\n\n                                {/* Meta Information */}\n                                <div className=\"flex flex-wrap items-center gap-4 text-sm text-gray-600 mb-6 pb-6 border-b\">\n                                    <div className=\"flex items-center\">\n                                        <i className=\"fas fa-calendar-alt mr-2 text-blue-600\"></i>\n                                        {formatDate(news.created_at || news.date)}\n                                    </div>\n                                    <div className=\"flex items-center\">\n                                        <i className=\"fas fa-clock mr-2 text-blue-600\"></i>\n                                        {formatTime(news.created_at || news.date)}\n                                    </div>\n                                    <div className=\"flex items-center\">\n                                        <i className=\"fas fa-eye mr-2 text-blue-600\"></i>\n                                        {news.views || 0} views\n                                    </div>\n                                </div>\n\n                                {/* Article Body */}\n                                <div className=\"prose prose-lg max-w-none\">\n                                    <div \n                                        className=\"text-gray-700 leading-relaxed whitespace-pre-line\"\n                                        dangerouslySetInnerHTML={{ \n                                            __html: news.content.replace(/\\n/g, '<br>') \n                                        }}\n                                    />\n                                </div>\n\n                                {/* Share Buttons */}\n                                <div className=\"mt-8 pt-6 border-t\">\n                                    <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">Bagikan Artikel</h3>\n                                    <div className=\"flex space-x-3\">\n                                        <button className=\"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors\">\n                                            <i className=\"fab fa-facebook-f mr-2\"></i>\n                                            Facebook\n                                        </button>\n                                        <button className=\"bg-blue-400 text-white px-4 py-2 rounded-lg hover:bg-blue-500 transition-colors\">\n                                            <i className=\"fab fa-twitter mr-2\"></i>\n                                            Twitter\n                                        </button>\n                                        <button className=\"bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors\">\n                                            <i className=\"fab fa-whatsapp mr-2\"></i>\n                                            WhatsApp\n                                        </button>\n                                    </div>\n                                </div>\n                            </div>\n                        </article>\n                    </div>\n\n                    {/* Sidebar */}\n                    <div className=\"lg:col-span-1\">\n                        {/* Related News */}\n                        {relatedNews.length > 0 && (\n                            <div className=\"bg-white rounded-lg shadow-md p-6 mb-6\">\n                                <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">\n                                    <i className=\"fas fa-newspaper mr-2 text-blue-600\"></i>\n                                    Berita Terkait\n                                </h3>\n                                <div className=\"space-y-4\">\n                                    {relatedNews.map((item) => (\n                                        <div \n                                            key={item.id}\n                                            onClick={() => handleRelatedNewsClick(item.id)}\n                                            className=\"flex cursor-pointer hover:bg-gray-50 p-2 rounded-lg transition-colors\"\n                                        >\n                                            <img \n                                                src={getImageUrl(item.image)} \n                                                alt={item.title}\n                                                className=\"w-16 h-16 object-cover rounded-lg mr-3 flex-shrink-0\"\n                                                onError={(e) => {\n                                                    e.target.src = '/react-news/frontend/public/placeholder-news.jpg';\n                                                }}\n                                            />\n                                            <div className=\"flex-1 min-w-0\">\n                                                <h4 className=\"text-sm font-medium text-gray-900 line-clamp-2 mb-1\">\n                                                    {item.title}\n                                                </h4>\n                                                <p className=\"text-xs text-gray-600\">\n                                                    {formatDate(item.created_at || item.date)}\n                                                </p>\n                                            </div>\n                                        </div>\n                                    ))}\n                                </div>\n                            </div>\n                        )}\n\n                        {/* Back to Home Button */}\n                        <div className=\"bg-white rounded-lg shadow-md p-6\">\n                            <button \n                                onClick={handleBackToHome}\n                                className=\"w-full bg-blue-600 text-white py-3 px-4 rounded-lg hover:bg-blue-700 transition-colors font-medium\"\n                            >\n                                <i className=\"fas fa-arrow-left mr-2\"></i>\n                                Kembali ke Beranda\n                            </button>\n                        </div>\n                    </div>\n                </div>\n            </div>\n        </div>\n    );\n};\n\nexport default DataNews;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,SAAS,EAAEC,WAAW,QAAQ,kBAAkB;AACzD,OAAOC,MAAM,MAAM,qBAAqB;AACxC,OAAOC,YAAY,MAAM,2BAA2B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAErD,MAAMC,QAAQ,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACnB,MAAM;IAAEC;EAAG,CAAC,GAAGR,SAAS,CAAC,CAAC;EAC1B,MAAMS,QAAQ,GAAGR,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACS,IAAI,EAAEC,OAAO,CAAC,GAAGb,QAAQ,CAAC,IAAI,CAAC;EACtC,MAAM,CAACc,WAAW,EAAEC,cAAc,CAAC,GAAGf,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACgB,OAAO,EAAEC,UAAU,CAAC,GAAGjB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACkB,KAAK,EAAEC,QAAQ,CAAC,GAAGnB,QAAQ,CAAC,IAAI,CAAC;EAExCC,SAAS,CAAC,MAAM;IACZ,IAAIS,EAAE,EAAE;MACJU,eAAe,CAACV,EAAE,CAAC;IACvB;EACJ,CAAC,EAAE,CAACA,EAAE,CAAC,CAAC;EAER,MAAMU,eAAe,GAAG,MAAOC,MAAM,IAAK;IACtC,IAAI;MACAJ,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMK,QAAQ,GAAG,MAAMC,KAAK,CAAC,wDAAwDF,MAAM,EAAE,CAAC;MAC9F,MAAMG,IAAI,GAAG,MAAMF,QAAQ,CAACG,IAAI,CAAC,CAAC;MAElC,IAAID,IAAI,CAACE,OAAO,EAAE;QACdb,OAAO,CAACW,IAAI,CAACA,IAAI,CAAC;QAClB;QACAG,gBAAgB,CAACH,IAAI,CAACA,IAAI,CAACI,WAAW,EAAEP,MAAM,CAAC;MACnD,CAAC,MAAM;QACHF,QAAQ,CAAC,wBAAwB,CAAC;MACtC;IACJ,CAAC,CAAC,OAAOD,KAAK,EAAE;MACZW,OAAO,CAACX,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC5CC,QAAQ,CAAC,qBAAqB,CAAC;IACnC,CAAC,SAAS;MACNF,UAAU,CAAC,KAAK,CAAC;IACrB;EACJ,CAAC;EAED,MAAMU,gBAAgB,GAAG,MAAAA,CAAOG,UAAU,EAAEC,aAAa,KAAK;IAC1D,IAAI;MACA,MAAMT,QAAQ,GAAG,MAAMC,KAAK,CAAC,wDAAwDO,UAAU,UAAU,CAAC;MAC1G,MAAMN,IAAI,GAAG,MAAMF,QAAQ,CAACG,IAAI,CAAC,CAAC;MAElC,IAAID,IAAI,CAACE,OAAO,EAAE;QACd;QACA,MAAMM,QAAQ,GAAGR,IAAI,CAACA,IAAI,CAACS,MAAM,CAACC,IAAI,IAAIA,IAAI,CAACxB,EAAE,KAAKyB,QAAQ,CAACJ,aAAa,CAAC,CAAC,CAACK,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;QAC1FrB,cAAc,CAACiB,QAAQ,CAAC;MAC5B;IACJ,CAAC,CAAC,OAAOd,KAAK,EAAE;MACZW,OAAO,CAACX,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;IACxD;EACJ,CAAC;EAED,MAAMmB,WAAW,GAAIC,SAAS,IAAK;IAC/B,IAAI,CAACA,SAAS,EAAE,OAAO,kDAAkD;IACzE,IAAIA,SAAS,CAACC,UAAU,CAAC,MAAM,CAAC,EAAE,OAAOD,SAAS;IAClD,IAAIA,SAAS,CAACC,UAAU,CAAC,GAAG,CAAC,EAAE,OAAO,wBAAwBD,SAAS,EAAE;IACzE,OAAO,+BAA+BA,SAAS,EAAE;EACrD,CAAC;EAED,MAAME,UAAU,GAAIC,UAAU,IAAK;IAC/B,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAACF,UAAU,CAAC;IACjC,OAAOC,IAAI,CAACE,kBAAkB,CAAC,OAAO,EAAE;MACpCC,OAAO,EAAE,MAAM;MACfC,IAAI,EAAE,SAAS;MACfC,KAAK,EAAE,MAAM;MACbC,GAAG,EAAE;IACT,CAAC,CAAC;EACN,CAAC;EAED,MAAMC,UAAU,GAAIR,UAAU,IAAK;IAC/B,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAACF,UAAU,CAAC;IACjC,OAAOC,IAAI,CAACQ,kBAAkB,CAAC,OAAO,EAAE;MACpCC,IAAI,EAAE,SAAS;MACfC,MAAM,EAAE;IACZ,CAAC,CAAC;EACN,CAAC;EAED,MAAMC,sBAAsB,GAAIhC,MAAM,IAAK;IACvCV,QAAQ,CAAC,cAAcU,MAAM,EAAE,CAAC;EACpC,CAAC;EAED,MAAMiC,gBAAgB,GAAGA,CAAA,KAAM;IAC3B3C,QAAQ,CAAC,GAAG,CAAC;EACjB,CAAC;EAED,IAAIK,OAAO,EAAE;IACT,oBACIT,OAAA;MAAKgD,SAAS,EAAC,0DAA0D;MAAAC,QAAA,eACrEjD,OAAA;QAAKgD,SAAS,EAAC,aAAa;QAAAC,QAAA,gBACxBjD,OAAA;UAAKgD,SAAS,EAAC;QAA6E;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACnGrD,OAAA;UAAGgD,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAAgB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAEd;EAEA,IAAI1C,KAAK,EAAE;IACP,oBACIX,OAAA;MAAKgD,SAAS,EAAC,0DAA0D;MAAAC,QAAA,eACrEjD,OAAA;QAAKgD,SAAS,EAAC,aAAa;QAAAC,QAAA,gBACxBjD,OAAA;UAAKgD,SAAS,EAAC,yEAAyE;UAAAC,QAAA,gBACpFjD,OAAA;YAAGgD,SAAS,EAAC;UAAkC;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,EACnD1C,KAAK;QAAA;UAAAuC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eACNrD,OAAA;UACIsD,OAAO,EAAEP,gBAAiB;UAC1BC,SAAS,EAAC,iFAAiF;UAAAC,QAAA,gBAE3FjD,OAAA;YAAGgD,SAAS,EAAC;UAAwB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,sBAE9C;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAEd;EAEA,IAAI,CAAChD,IAAI,EAAE;IACP,oBACIL,OAAA;MAAKgD,SAAS,EAAC,0DAA0D;MAAAC,QAAA,eACrEjD,OAAA;QAAKgD,SAAS,EAAC,aAAa;QAAAC,QAAA,gBACxBjD,OAAA;UAAGgD,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAAsB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACvDrD,OAAA;UACIsD,OAAO,EAAEP,gBAAiB;UAC1BC,SAAS,EAAC,sFAAsF;UAAAC,QAAA,gBAEhGjD,OAAA;YAAGgD,SAAS,EAAC;UAAwB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,sBAE9C;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAEd;EAEA,oBACIrD,OAAA;IAAKgD,SAAS,EAAC,yBAAyB;IAAAC,QAAA,gBAEpCjD,OAAA;MAAKgD,SAAS,EAAC,6BAA6B;MAAAC,QAAA,eACxCjD,OAAA;QAAKgD,SAAS,EAAC,6BAA6B;QAAAC,QAAA,eACxCjD,OAAA;UAAKgD,SAAS,EAAC,mDAAmD;UAAAC,QAAA,gBAC9DjD,OAAA;YACIsD,OAAO,EAAEP,gBAAiB;YAC1BC,SAAS,EAAC,uCAAuC;YAAAC,QAAA,gBAEjDjD,OAAA;cAAGgD,SAAS,EAAC;YAAkB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,WAExC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTrD,OAAA;YAAGgD,SAAS,EAAC;UAA8B;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAChDrD,OAAA;YAAMgD,SAAS,EAAC,2BAA2B;YAAAC,QAAA,EAAC;UAAa;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/D;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,eAGNrD,OAAA;MAAKgD,SAAS,EAAC,6BAA6B;MAAAC,QAAA,eACxCjD,OAAA;QAAKgD,SAAS,EAAC,uCAAuC;QAAAC,QAAA,gBAElDjD,OAAA;UAAKgD,SAAS,EAAC,eAAe;UAAAC,QAAA,eAC1BjD,OAAA;YAASgD,SAAS,EAAC,+CAA+C;YAAAC,QAAA,gBAE9DjD,OAAA;cAAKgD,SAAS,EAAC,UAAU;cAAAC,QAAA,gBACrBjD,OAAA;gBACIuD,GAAG,EAAEzB,WAAW,CAACzB,IAAI,CAACmD,KAAK,CAAE;gBAC7BC,GAAG,EAAEpD,IAAI,CAACqD,KAAM;gBAChBV,SAAS,EAAC,kCAAkC;gBAC5CW,OAAO,EAAGC,CAAC,IAAK;kBACZA,CAAC,CAACC,MAAM,CAACN,GAAG,GAAG,kDAAkD;gBACrE;cAAE;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,eACFrD,OAAA;gBAAKgD,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,eAClCjD,OAAA;kBAAMgD,SAAS,EAAC,mEAAmE;kBAAAC,QAAA,EAC9E5C,IAAI,CAACyD,aAAa,IAAI;gBAAQ;kBAAAZ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7B;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,eAGNrD,OAAA;cAAKgD,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACvBjD,OAAA;gBAAIgD,SAAS,EAAC,iEAAiE;gBAAAC,QAAA,EAC1E5C,IAAI,CAACqD;cAAK;gBAAAR,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACX,CAAC,eAGLrD,OAAA;gBAAKgD,SAAS,EAAC,4EAA4E;gBAAAC,QAAA,gBACvFjD,OAAA;kBAAKgD,SAAS,EAAC,mBAAmB;kBAAAC,QAAA,gBAC9BjD,OAAA;oBAAGgD,SAAS,EAAC;kBAAwC;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,EACzDpB,UAAU,CAAC5B,IAAI,CAAC0D,UAAU,IAAI1D,IAAI,CAAC8B,IAAI,CAAC;gBAAA;kBAAAe,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxC,CAAC,eACNrD,OAAA;kBAAKgD,SAAS,EAAC,mBAAmB;kBAAAC,QAAA,gBAC9BjD,OAAA;oBAAGgD,SAAS,EAAC;kBAAiC;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,EAClDX,UAAU,CAACrC,IAAI,CAAC0D,UAAU,IAAI1D,IAAI,CAAC8B,IAAI,CAAC;gBAAA;kBAAAe,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxC,CAAC,eACNrD,OAAA;kBAAKgD,SAAS,EAAC,mBAAmB;kBAAAC,QAAA,gBAC9BjD,OAAA;oBAAGgD,SAAS,EAAC;kBAA+B;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,EAChDhD,IAAI,CAAC2D,KAAK,IAAI,CAAC,EAAC,QACrB;gBAAA;kBAAAd,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,eAGNrD,OAAA;gBAAKgD,SAAS,EAAC,2BAA2B;gBAAAC,QAAA,eACtCjD,OAAA;kBACIgD,SAAS,EAAC,mDAAmD;kBAC7DiB,uBAAuB,EAAE;oBACrBC,MAAM,EAAE7D,IAAI,CAAC8D,OAAO,CAACC,OAAO,CAAC,KAAK,EAAE,MAAM;kBAC9C;gBAAE;kBAAAlB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC,eAGNrD,OAAA;gBAAKgD,SAAS,EAAC,oBAAoB;gBAAAC,QAAA,gBAC/BjD,OAAA;kBAAIgD,SAAS,EAAC,0CAA0C;kBAAAC,QAAA,EAAC;gBAAe;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC7ErD,OAAA;kBAAKgD,SAAS,EAAC,gBAAgB;kBAAAC,QAAA,gBAC3BjD,OAAA;oBAAQgD,SAAS,EAAC,iFAAiF;oBAAAC,QAAA,gBAC/FjD,OAAA;sBAAGgD,SAAS,EAAC;oBAAwB;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,YAE9C;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACTrD,OAAA;oBAAQgD,SAAS,EAAC,iFAAiF;oBAAAC,QAAA,gBAC/FjD,OAAA;sBAAGgD,SAAS,EAAC;oBAAqB;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,WAE3C;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACTrD,OAAA;oBAAQgD,SAAS,EAAC,mFAAmF;oBAAAC,QAAA,gBACjGjD,OAAA;sBAAGgD,SAAS,EAAC;oBAAsB;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,YAE5C;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACR,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eAGNrD,OAAA;UAAKgD,SAAS,EAAC,eAAe;UAAAC,QAAA,GAEzB1C,WAAW,CAAC8D,MAAM,GAAG,CAAC,iBACnBrE,OAAA;YAAKgD,SAAS,EAAC,wCAAwC;YAAAC,QAAA,gBACnDjD,OAAA;cAAIgD,SAAS,EAAC,0CAA0C;cAAAC,QAAA,gBACpDjD,OAAA;gBAAGgD,SAAS,EAAC;cAAqC;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,kBAE3D;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACLrD,OAAA;cAAKgD,SAAS,EAAC,WAAW;cAAAC,QAAA,EACrB1C,WAAW,CAAC+D,GAAG,CAAE3C,IAAI,iBAClB3B,OAAA;gBAEIsD,OAAO,EAAEA,CAAA,KAAMR,sBAAsB,CAACnB,IAAI,CAACxB,EAAE,CAAE;gBAC/C6C,SAAS,EAAC,uEAAuE;gBAAAC,QAAA,gBAEjFjD,OAAA;kBACIuD,GAAG,EAAEzB,WAAW,CAACH,IAAI,CAAC6B,KAAK,CAAE;kBAC7BC,GAAG,EAAE9B,IAAI,CAAC+B,KAAM;kBAChBV,SAAS,EAAC,sDAAsD;kBAChEW,OAAO,EAAGC,CAAC,IAAK;oBACZA,CAAC,CAACC,MAAM,CAACN,GAAG,GAAG,kDAAkD;kBACrE;gBAAE;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC,eACFrD,OAAA;kBAAKgD,SAAS,EAAC,gBAAgB;kBAAAC,QAAA,gBAC3BjD,OAAA;oBAAIgD,SAAS,EAAC,qDAAqD;oBAAAC,QAAA,EAC9DtB,IAAI,CAAC+B;kBAAK;oBAAAR,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACX,CAAC,eACLrD,OAAA;oBAAGgD,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,EAC/BhB,UAAU,CAACN,IAAI,CAACoC,UAAU,IAAIpC,IAAI,CAACQ,IAAI;kBAAC;oBAAAe,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1C,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA,GAnBD1B,IAAI,CAACxB,EAAE;gBAAA+C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAoBX,CACR;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CACR,eAGDrD,OAAA;YAAKgD,SAAS,EAAC,mCAAmC;YAAAC,QAAA,eAC9CjD,OAAA;cACIsD,OAAO,EAAEP,gBAAiB;cAC1BC,SAAS,EAAC,oGAAoG;cAAAC,QAAA,gBAE9GjD,OAAA;gBAAGgD,SAAS,EAAC;cAAwB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,sBAE9C;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEd,CAAC;AAACnD,EAAA,CAxRID,QAAQ;EAAA,QACKN,SAAS,EACPC,WAAW;AAAA;AAAA2E,EAAA,GAF1BtE,QAAQ;AA0Rd,eAAeA,QAAQ;AAAC,IAAAsE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}