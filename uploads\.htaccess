# Allow access to uploaded images
Options +Indexes
DirectoryIndex disabled

# Security settings for uploads
<Files "*.php">
    Order Deny,Allow
    Deny from all
</Files>

<Files "*.phtml">
    Order Deny,Allow
    Deny from all
</Files>

<Files "*.php3">
    Order Deny,Allow
    Deny from all
</Files>

<Files "*.php4">
    Order Deny,Allow
    Deny from all
</Files>

<Files "*.php5">
    Order Deny,Allow
    Deny from all
</Files>

# Allow image files
<FilesMatch "\.(jpg|jpeg|png|gif|webp|svg|bmp|ico)$">
    Order Allow,Deny
    Allow from all
</FilesMatch>

# Set proper MIME types
<IfModule mod_mime.c>
    AddType image/jpeg .jpg .jpeg
    AddType image/png .png
    AddType image/gif .gif
    AddType image/webp .webp
    AddType image/svg+xml .svg
    AddType image/x-icon .ico
</IfModule>

# Enable compression for images
<IfModule mod_deflate.c>
    <FilesMatch "\.(jpg|jpeg|png|gif|webp)$">
        SetOutputFilter DEFLATE
    </FilesMatch>
</IfModule>

# Set cache headers for images
<IfModule mod_expires.c>
    ExpiresActive On
    ExpiresByType image/jpeg "access plus 1 month"
    ExpiresByType image/png "access plus 1 month"
    ExpiresByType image/gif "access plus 1 month"
    ExpiresByType image/webp "access plus 1 month"
    ExpiresByType image/svg+xml "access plus 1 month"
</IfModule>
