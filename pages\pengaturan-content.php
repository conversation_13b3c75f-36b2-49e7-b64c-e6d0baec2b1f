<!-- Settings Tabs -->
<div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
    <div class="border-b border-gray-200">
        <nav class="flex space-x-8 px-6">
            <button onclick="showSettingsTab('general')" class="settings-tab py-4 px-1 border-b-2 font-medium text-sm flex items-center gap-2 border-primary-500 text-primary-600" data-tab="general">
                <i class="fas fa-cog"></i>
                Umum
            </button>
            <button onclick="showSettingsTab('appearance')" class="settings-tab py-4 px-1 border-b-2 font-medium text-sm flex items-center gap-2 border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300" data-tab="appearance">
                <i class="fas fa-palette"></i>
                Tampilan
            </button>
            <button onclick="showSettingsTab('users')" class="settings-tab py-4 px-1 border-b-2 font-medium text-sm flex items-center gap-2 border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300" data-tab="users">
                <i class="fas fa-users"></i>
                Pengguna
            </button>
            <button onclick="showSettingsTab('security')" class="settings-tab py-4 px-1 border-b-2 font-medium text-sm flex items-center gap-2 border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300" data-tab="security">
                <i class="fas fa-shield-alt"></i>
                Keamanan
            </button>
        </nav>
    </div>
</div>

<!-- General Settings -->
<div id="general-settings" class="settings-content">
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">Pengaturan Umum</h3>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Nama Website</label>
                <input type="text" value="Portal Berita" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent">
            </div>
            
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Email Kontak</label>
                <input type="email" value="<EMAIL>" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent">
            </div>
        </div>
        
        <div class="mt-6">
            <label class="block text-sm font-medium text-gray-700 mb-2">Deskripsi Website</label>
            <textarea rows="3" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent">Portal berita terpercaya dengan informasi terkini dan akurat</textarea>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mt-6">
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Nomor Telepon</label>
                <input type="tel" value="+62 ************" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent">
            </div>
            
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Alamat</label>
                <input type="text" value="Jakarta, Indonesia" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent">
            </div>
        </div>
        
        <div class="mt-6 pt-4 border-t border-gray-200">
            <button class="bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-lg transition-colors">
                <i class="fas fa-save mr-2"></i>
                Simpan Pengaturan
            </button>
        </div>
    </div>
</div>

<!-- Appearance Settings -->
<div id="appearance-settings" class="settings-content hidden">
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">Pengaturan Tampilan</h3>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Logo Website</label>
                <div class="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                    <i class="fas fa-cloud-upload-alt text-3xl text-gray-400 mb-4"></i>
                    <p class="text-sm text-gray-600 mb-2">Klik untuk upload logo</p>
                    <input type="file" accept="image/*" class="hidden" id="logo-upload">
                    <label for="logo-upload" class="cursor-pointer bg-primary-600 text-white px-4 py-2 rounded-lg hover:bg-primary-700 transition-colors">
                        Pilih File
                    </label>
                </div>
            </div>
            
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Favicon</label>
                <div class="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                    <i class="fas fa-image text-3xl text-gray-400 mb-4"></i>
                    <p class="text-sm text-gray-600 mb-2">Klik untuk upload favicon</p>
                    <input type="file" accept="image/*" class="hidden" id="favicon-upload">
                    <label for="favicon-upload" class="cursor-pointer bg-primary-600 text-white px-4 py-2 rounded-lg hover:bg-primary-700 transition-colors">
                        Pilih File
                    </label>
                </div>
            </div>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mt-6">
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Warna Primer</label>
                <div class="flex items-center space-x-3">
                    <input type="color" value="#2563eb" class="w-12 h-10 border border-gray-300 rounded cursor-pointer">
                    <input type="text" value="#2563eb" class="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent">
                </div>
            </div>
            
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Warna Sekunder</label>
                <div class="flex items-center space-x-3">
                    <input type="color" value="#1e293b" class="w-12 h-10 border border-gray-300 rounded cursor-pointer">
                    <input type="text" value="#1e293b" class="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent">
                </div>
            </div>
        </div>
        
        <div class="mt-6 pt-4 border-t border-gray-200">
            <button class="bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-lg transition-colors">
                <i class="fas fa-save mr-2"></i>
                Simpan Pengaturan
            </button>
        </div>
    </div>
</div>

<!-- Users Settings -->
<div id="users-settings" class="settings-content hidden">
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
        <div class="flex justify-between items-center mb-4">
            <h3 class="text-lg font-semibold text-gray-900">Manajemen Pengguna</h3>
            <button class="bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-lg transition-colors">
                <i class="fas fa-plus mr-2"></i>
                Tambah Pengguna
            </button>
        </div>
        
        <div class="overflow-x-auto">
            <table class="w-full">
                <thead>
                    <tr class="border-b border-gray-200">
                        <th class="text-left py-3 px-4 font-medium text-gray-600 text-sm">Nama</th>
                        <th class="text-left py-3 px-4 font-medium text-gray-600 text-sm">Email</th>
                        <th class="text-left py-3 px-4 font-medium text-gray-600 text-sm">Role</th>
                        <th class="text-left py-3 px-4 font-medium text-gray-600 text-sm">Status</th>
                        <th class="text-left py-3 px-4 font-medium text-gray-600 text-sm">Aksi</th>
                    </tr>
                </thead>
                <tbody>
                    <tr class="border-b border-gray-100 hover:bg-gray-50">
                        <td class="py-3 px-4">
                            <div class="flex items-center">
                                <div class="w-8 h-8 bg-primary-600 rounded-full flex items-center justify-center text-white text-sm font-medium mr-3">
                                    A
                                </div>
                                <span class="font-medium text-gray-900">Admin</span>
                            </div>
                        </td>
                        <td class="py-3 px-4 text-sm text-gray-600"><EMAIL></td>
                        <td class="py-3 px-4">
                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-purple-100 text-purple-800">
                                Administrator
                            </span>
                        </td>
                        <td class="py-3 px-4">
                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
                                Aktif
                            </span>
                        </td>
                        <td class="py-3 px-4">
                            <div class="flex gap-2">
                                <button class="text-blue-600 hover:text-blue-900 p-1 rounded transition-colors">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="text-red-600 hover:text-red-900 p-1 rounded transition-colors">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    
                    <tr class="border-b border-gray-100 hover:bg-gray-50">
                        <td class="py-3 px-4">
                            <div class="flex items-center">
                                <div class="w-8 h-8 bg-green-600 rounded-full flex items-center justify-center text-white text-sm font-medium mr-3">
                                    E
                                </div>
                                <span class="font-medium text-gray-900">Editor</span>
                            </div>
                        </td>
                        <td class="py-3 px-4 text-sm text-gray-600"><EMAIL></td>
                        <td class="py-3 px-4">
                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">
                                Editor
                            </span>
                        </td>
                        <td class="py-3 px-4">
                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
                                Aktif
                            </span>
                        </td>
                        <td class="py-3 px-4">
                            <div class="flex gap-2">
                                <button class="text-blue-600 hover:text-blue-900 p-1 rounded transition-colors">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="text-red-600 hover:text-red-900 p-1 rounded transition-colors">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Security Settings -->
<div id="security-settings" class="settings-content hidden">
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">Pengaturan Keamanan</h3>
        
        <div class="space-y-6">
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Ubah Password</label>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <input type="password" placeholder="Password Lama" class="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent">
                    <input type="password" placeholder="Password Baru" class="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent">
                </div>
                <input type="password" placeholder="Konfirmasi Password Baru" class="w-full mt-4 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent">
            </div>
            
            <div class="border-t border-gray-200 pt-6">
                <h4 class="text-md font-medium text-gray-900 mb-4">Pengaturan Login</h4>
                
                <div class="space-y-4">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm font-medium text-gray-700">Two-Factor Authentication</p>
                            <p class="text-xs text-gray-500">Tambahkan lapisan keamanan ekstra</p>
                        </div>
                        <label class="relative inline-flex items-center cursor-pointer">
                            <input type="checkbox" class="sr-only peer">
                            <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary-600"></div>
                        </label>
                    </div>
                    
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm font-medium text-gray-700">Login Notifications</p>
                            <p class="text-xs text-gray-500">Notifikasi email saat ada login baru</p>
                        </div>
                        <label class="relative inline-flex items-center cursor-pointer">
                            <input type="checkbox" checked class="sr-only peer">
                            <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary-600"></div>
                        </label>
                    </div>
                </div>
            </div>
            
            <div class="pt-4 border-t border-gray-200">
                <button class="bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-lg transition-colors">
                    <i class="fas fa-save mr-2"></i>
                    Simpan Pengaturan
                </button>
            </div>
        </div>
    </div>
</div>

<script>
function showSettingsTab(tabName) {
    // Hide all settings content
    document.querySelectorAll('.settings-content').forEach(content => {
        content.classList.add('hidden');
    });
    
    // Show selected content
    document.getElementById(tabName + '-settings').classList.remove('hidden');
    
    // Update tab styles
    document.querySelectorAll('.settings-tab').forEach(tab => {
        tab.classList.remove('border-primary-500', 'text-primary-600');
        tab.classList.add('border-transparent', 'text-gray-500');
    });
    
    // Set active tab
    document.querySelector(`[data-tab="${tabName}"]`).classList.remove('border-transparent', 'text-gray-500');
    document.querySelector(`[data-tab="${tabName}"]`).classList.add('border-primary-500', 'text-primary-600');
}
</script>
