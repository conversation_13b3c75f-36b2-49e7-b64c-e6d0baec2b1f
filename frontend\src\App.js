import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import LandingPage from './pages/user/LandingPage';
import DataNews from './pages/user/data-news';
import Saved from './pages/user/components/Saved';
import './App.css';
import Login from './pages/admin/auth/Login';
import Register from './pages/admin/auth/Register';

// Component untuk embed DashboardTable.php tanpa menampilkan .php di URL
const DashboardTable = () => {
  React.useEffect(() => {
    // Load DashboardTable.php content in iframe
    const iframe = document.getElementById('dashboard-iframe');
    if (iframe) {
      iframe.src = 'http://localhost/react-news/frontend/src/pages/admin/DashboardTable.php';
    }
  }, []);

  return (
    <div style={{ width: '100%', height: '100vh', margin: 0, padding: 0 }}>
      <iframe
        id="dashboard-iframe"
        src="about:blank"
        style={{
          width: '100%',
          height: '100%',
          border: 'none',
          margin: 0,
          padding: 0
        }}
        title="Admin Dashboard"
      />
    </div>
  );
};

// Component untuk redirect ke dashboard
const AdminRedirect = () => {
  React.useEffect(() => {
    // Redirect to clean dashboard URL
    window.location.href = '/dashboard';
  }, []);

  return (
    <div style={{ padding: '20px', textAlign: 'center', fontFamily: 'Arial, sans-serif' }}>
      <div style={{ maxWidth: '400px', margin: '0 auto', marginTop: '100px' }}>
        <h2 style={{ color: '#3B82F6', marginBottom: '20px' }}>🚀 Redirecting to Dashboard...</h2>
        <p style={{ fontSize: '16px', color: '#666' }}>
          Mengalihkan ke dashboard admin...
        </p>
      </div>
    </div>
  );
};

function App() {
  return (
    <Router>
      <Routes>
        {/* User Routes */}
        <Route path="/" element={<LandingPage />} />
        <Route path="/home" element={<LandingPage />} />
        <Route path="/data-news/:id" element={<DataNews />} />
        <Route path="/saved" element={<Saved />} />
        <Route path="/bookmark" element={<Saved />} />

        {/* Admin Routes */}
        <Route path="/admin" element={<AdminRedirect />} />
        <Route path="/admin/login" element={<Login />} />
        <Route path="/admin/register" element={<Register />} />

        {/* Dashboard Routes - Clean URLs without .php */}
        <Route path="/dashboard" element={<DashboardTable />} />
        <Route path="/admin/dashboard" element={<DashboardTable />} />

        {/* Fallback Route */}
        <Route path="*" element={<LandingPage />} />
      </Routes>
    </Router>
  );
}

export default App;
