-- Clean up invalid logo paths in database
-- Run this in your MySQL database

USE react_news;

-- Check current logo settings
SELECT 'Current logo settings:' as info;
SELECT setting_key, setting_value FROM settings WHERE setting_key = 'website_logo';
SELECT website_logo FROM settings ORDER BY id DESC LIMIT 1;

-- Clean up invalid logo paths in key-value settings table
UPDATE settings 
SET setting_value = '/logo192.png' 
WHERE setting_key = 'website_logo' 
AND (setting_value LIKE '%assets/logo.png%' OR setting_value = '' OR setting_value IS NULL);

-- Clean up invalid logo paths in direct column settings table
UPDATE settings 
SET website_logo = '/logo192.png' 
WHERE website_logo LIKE '%assets/logo.png%' 
OR website_logo = '' 
OR website_logo IS NULL;

-- Insert default settings if none exist
INSERT IGNORE INTO settings (setting_key, setting_value) VALUES ('website_logo', '/logo192.png');

-- For direct column structure, ensure default record exists
INSERT IGNORE INTO settings (id, website_name, website_logo, website_description, primary_color, secondary_color, accent_color) 
VALUES (1, 'React News Portal', '/logo192.png', 'Portal berita terkini dan terpercaya', '#3B82F6', '#10B981', '#F59E0B');

-- Show results after cleanup
SELECT 'Logo settings after cleanup:' as info;
SELECT setting_key, setting_value FROM settings WHERE setting_key = 'website_logo';
SELECT id, website_name, website_logo FROM settings ORDER BY id DESC LIMIT 3;

-- Verify no invalid paths remain
SELECT 'Checking for invalid paths:' as info;
SELECT COUNT(*) as invalid_count FROM settings WHERE setting_value LIKE '%assets/%' OR website_logo LIKE '%assets/%';

SELECT 'Cleanup completed successfully!' as message;
